/**
 * TypeScript type definitions for MCP (Model Context Protocol) integration
 */

// Base types
export type MCPTransportType = 'http' | 'stdio' | 'websocket' | 'sse';
export type MCPServerStatus = 'active' | 'inactive' | 'error' | 'connecting';
export type MCPConfigType = 'json' | 'form';
export type MCPServerAction = 'start' | 'stop' | 'restart' | 'test' | 'discover';
export type MCPInputVariableType = 'promptString' | 'promptChoice' | 'env';

// Server configuration interfaces
export interface MCPInputDefinition {
  type: MCPInputVariableType;
  id: string;
  description: string;
  password?: boolean;
  choices?: string[];
  default?: string;
}

export interface MCPServerDefinition {
  type?: 'stdio' | 'http' | 'websocket' | 'sse';
  url?: string;
  command?: string;
  args?: string[];
  env?: Record<string, string>;
  headers?: Record<string, string>;
  timeout?: number;
}

export interface MCPJSONConfiguration {
  inputs?: MCPInputDefinition[];
  servers: Record<string, MCPServerDefinition>;
}

export interface MCPServerConfig {
  name: string;
  transport_type: MCPTransportType;
  url?: string;
  command?: string;
  args?: string[];
  env?: Record<string, string>;
  headers?: Record<string, string>;
  timeout?: number;
}

// Server data models
export interface MCPServer {
  id: string;
  user_id: number;
  business_profile_id?: string;
  name: string;
  description?: string;
  config_type: MCPConfigType;
  transport_type: MCPTransportType;
  status: MCPServerStatus;
  configuration: Record<string, any>;
  tool_count: number;
  resource_count: number;
  prompt_count: number;
  created_at: string;
  updated_at: string;
  last_connected_at?: string;
  error_message?: string;
}

export interface MCPTool {
  id: string;
  server_id: string;
  tool_name: string;
  tool_description?: string;
  parameters?: Record<string, any>;
  capabilities?: Record<string, any>;
  is_enabled: boolean;
  usage_count: number;
  last_used_at?: string;
  created_at: string;
  updated_at: string;
}

export interface MCPResource {
  id: string;
  server_id: string;
  resource_name: string;
  resource_type: string;
  resource_description?: string;
  parameters?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MCPPrompt {
  id: string;
  server_id: string;
  prompt_name: string;
  prompt_description?: string;
  template: string;
  parameters?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MCPInputVariable {
  id: string;
  user_id: number;
  variable_id: string;
  variable_type: MCPInputVariableType;
  description: string;
  is_password: boolean;
  encrypted_value: string;
  created_at: string;
  updated_at: string;
}

// API request/response types
export interface MCPServerCreate {
  name: string;
  description?: string;
  business_profile_id?: string;
  config_type: MCPConfigType;
  json_config?: MCPJSONConfiguration;
  server_config?: MCPServerConfig;
}

export interface MCPServerUpdate {
  name?: string;
  description?: string;
  business_profile_id?: string;
  json_config?: MCPJSONConfiguration;
  server_config?: MCPServerConfig;
}

export interface MCPServerResponse extends MCPServer {}

export interface MCPServerListResponse {
  servers: MCPServer[];
  total: number;
  page: number;
  per_page: number;
}

export interface MCPServerActionResponse {
  success: boolean;
  message: string;
  data?: Record<string, any>;
}

export interface MCPDiscoveryResponse {
  tools: MCPTool[];
  resources: MCPResource[];
  prompts: MCPPrompt[];
  server_info?: Record<string, any>;
}

export interface MCPInputVariableCreate {
  variable_id: string;
  variable_type: MCPInputVariableType;
  description: string;
  is_password: boolean;
  value: string;
}

export interface MCPInputVariableUpdate {
  description?: string;
  value?: string;
}

export interface MCPInputVariableResponse extends MCPInputVariable {
  // Exclude encrypted_value from response
  encrypted_value?: never;
}

// Error response types
export interface MCPErrorResponse {
  success: false;
  error_code: string;
  message: string;
  details?: string;
  field_errors?: Record<string, string[]>;
  timestamp: string;
  request_id?: string;
  suggestions?: string[];
}

// Configuration template types
export interface MCPServerTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  icon?: string;
  config: MCPJSONConfiguration;
  required_inputs: string[];
  documentation_url?: string;
  examples?: Record<string, any>[];
}

// Agent integration types
export interface MCPAgentTool {
  id: string;
  name: string;
  description: string;
  server_name: string;
  server_id: string;
  parameters: Record<string, any>;
  capabilities: Record<string, any>;
  usage_count: number;
  type: 'mcp_tool';
}

export interface MCPAgentResource {
  id: string;
  name: string;
  type: string;
  description: string;
  server_name: string;
  server_id: string;
  parameters: Record<string, any>;
}

export interface MCPAgentPrompt {
  id: string;
  name: string;
  description: string;
  template: string;
  server_name: string;
  server_id: string;
  parameters: Record<string, any>;
}

// Tool execution types
export interface MCPToolExecutionRequest {
  tool_name: string;
  arguments: Record<string, any>;
}

export interface MCPToolExecutionResponse {
  success: boolean;
  result?: any;
  error?: string;
  tool_name: string;
  server_name?: string;
  execution_time: string;
  metadata?: Record<string, any>;
}

// Resource access types
export interface MCPResourceReadRequest {
  resource_name: string;
}

export interface MCPResourceReadResponse {
  success: boolean;
  content?: any;
  error?: string;
  resource_name: string;
  server_name?: string;
}

// Statistics and monitoring types
export interface MCPServerStats {
  server_id: string;
  server_name: string;
  status: MCPServerStatus;
  uptime?: number;
  total_requests: number;
  successful_requests: number;
  failed_requests: number;
  average_response_time: number;
  last_activity?: string;
}

export interface MCPToolStats {
  tool_id: string;
  tool_name: string;
  server_name: string;
  usage_count: number;
  success_rate: number;
  average_execution_time: number;
  last_used?: string;
}

export interface MCPSystemStats {
  total_servers: number;
  active_servers: number;
  total_tools: number;
  total_executions: number;
  success_rate: number;
  server_stats: MCPServerStats[];
  popular_tools: MCPToolStats[];
}

// UI component props types
export interface MCPServerCardProps {
  server: MCPServer;
  onAction: (server: MCPServer, action: MCPServerAction) => void;
  onEdit: (server: MCPServer) => void;
  onDelete: (server: MCPServer) => void;
}

export interface MCPConfigurationModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: MCPServerCreate) => void;
  initialData?: MCPServer;
  businessProfileId?: string;
}

export interface MCPToolListProps {
  serverId: string;
  tools: MCPTool[];
  onToolToggle: (toolId: string, enabled: boolean) => void;
  onToolExecute: (toolId: string, args: Record<string, any>) => void;
}

// Validation types
export interface MCPValidationResult {
  valid: boolean;
  errors: string[];
  warnings?: string[];
}

export interface MCPSecurityCheck {
  passed: boolean;
  issues: string[];
  severity: 'low' | 'medium' | 'high' | 'critical';
}
