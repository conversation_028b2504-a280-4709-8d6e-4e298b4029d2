#!/usr/bin/env python3
"""
Simple test script for the unified agent system that avoids problematic imports.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_agent_registry():
    """Test the agent registry configuration loading."""
    try:
        from pathlib import Path
        import yaml
        
        print("✅ Successfully imported required modules")
        
        # Load agent registry
        registry_path = Path(__file__).parent / "agents" / "langgraph" / "config" / "agent_registry.yaml"
        if registry_path.exists():
            with open(registry_path, 'r', encoding='utf-8') as f:
                registry = yaml.safe_load(f)
            
            print(f"✅ Successfully loaded agent registry from {registry_path}")
            
            agents = registry.get("agents", {})
            print(f"✅ Found {len(agents)} agents in registry:")
            for agent_id, config in agents.items():
                print(f"   - {agent_id}: {config.get('name', 'Unknown')}")
            
            # Check required fields
            for agent_id, config in agents.items():
                required_fields = ["agent_type", "name", "agent_class"]
                missing_fields = [field for field in required_fields if field not in config]
                if missing_fields:
                    print(f"❌ Agent {agent_id} missing required fields: {missing_fields}")
                else:
                    print(f"✅ Agent {agent_id} has all required fields")
            
            return True
        else:
            print(f"❌ Agent registry not found: {registry_path}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing agent registry: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_routing_config():
    """Test the routing configuration loading."""
    try:
        from pathlib import Path
        import yaml
        
        print("✅ Successfully imported required modules")
        
        # Load routing config
        config_path = Path(__file__).parent / "agents" / "langgraph" / "config" / "routing_config.yaml"
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            print(f"✅ Successfully loaded routing config from {config_path}")
            
            # Check intent patterns
            intent_patterns = config.get("intent_patterns", {})
            print(f"✅ Found {len(intent_patterns)} intent patterns:")
            for intent, patterns in intent_patterns.items():
                print(f"   - {intent}: {len(patterns)} patterns")
            
            # Check routing rules
            routing_rules = config.get("routing_rules", {})
            default_mappings = routing_rules.get("default_mappings", {})
            print(f"✅ Found {len(default_mappings)} default routing mappings:")
            for intent, agent in default_mappings.items():
                print(f"   - {intent} -> {agent}")
            
            return True
        else:
            print(f"❌ Routing config not found: {config_path}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing routing config: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_unified_agent_node_class():
    """Test the UnifiedAgentNode class definition."""
    try:
        # Import just the class definition without triggering other imports
        import importlib.util
        from pathlib import Path
        
        # Load the module directly
        module_path = Path(__file__).parent / "agents" / "langgraph" / "nodes" / "base_agent_node.py"
        spec = importlib.util.spec_from_file_location("base_agent_node", module_path)
        module = importlib.util.module_from_spec(spec)
        
        # Mock the problematic imports
        import sys
        sys.modules['agents.langgraph.states.unified_state'] = type('MockModule', (), {
            'UnifiedDatageniusState': dict,
            'update_agent_transition': lambda *args: None,
            'add_message': lambda *args: None,
            'add_cross_agent_insight': lambda *args: None,
            'MessageType': type('MessageType', (), {'AGENT': type('Agent', (), {'value': 'agent'})})(),
            'AgentRole': type('AgentRole', (), {'PRIMARY': 'primary'})()
        })()
        
        spec.loader.exec_module(module)
        
        print("✅ Successfully loaded base_agent_node module")
        
        # Check if UnifiedAgentNode exists
        if hasattr(module, 'UnifiedAgentNode'):
            UnifiedAgentNode = getattr(module, 'UnifiedAgentNode')
            print("✅ UnifiedAgentNode class found")
            
            # Check key methods
            required_methods = ['_derive_agent_type', '_load_agent_instance', '_extract_agent_metadata', '_process_message']
            for method in required_methods:
                if hasattr(UnifiedAgentNode, method):
                    print(f"✅ Method {method} found")
                else:
                    print(f"❌ Method {method} missing")
            
            return True
        else:
            print("❌ UnifiedAgentNode class not found")
            return False
        
    except Exception as e:
        print(f"❌ Error testing UnifiedAgentNode class: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🧪 Testing Unified Agent System (Simple)")
    print("=" * 50)
    
    success = True
    
    print("\n📋 Test 1: Agent Registry Configuration")
    success &= test_agent_registry()
    
    print("\n📋 Test 2: Routing Configuration")
    success &= test_routing_config()
    
    print("\n📋 Test 3: UnifiedAgentNode Class")
    success &= test_unified_agent_node_class()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed! The unified agent system configuration is working.")
        print("\n📝 Summary of the Unified Agent System:")
        print("   ✅ Single UnifiedAgentNode handles all agents")
        print("   ✅ Configuration-driven agent registration")
        print("   ✅ Dynamic agent loading from YAML registry")
        print("   ✅ Intent-based routing with configuration")
        print("   ✅ Extensible - add new agents by updating YAML")
    else:
        print("💥 Some tests failed. Check the errors above.")
    
    return success

if __name__ == "__main__":
    main()
