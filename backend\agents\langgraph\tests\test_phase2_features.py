"""
Tests for Phase 2: Enhanced User Experience Features (Updated)

This module tests the Phase 2 features with all production-ready improvements:
1. AgentSwitchNode with performance optimizations and security
2. Agent coordination with circuit breakers and timeouts
3. Enhanced initial agent selection
4. Performance monitoring and metrics
5. Configuration management
6. Rate limiting and security features
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch, mock_open
from datetime import datetime
from typing import Dict, Any
from pathlib import Path

from ..nodes.routing_node import AgentSwitchNode, RoutingNode
from ..nodes.base_agent_node import BaseAgentNode, CircuitBreaker, CircuitBreakerState
from ..core.workflow_manager import WorkflowManager
from ..utils.performance_monitor import PerformanceMonitor, performance_monitor
from ..states.unified_state import (
    UnifiedDatageniusState,
    create_unified_state,
    ConversationMode,
    set_selected_agent
)


class TestCircuitBreaker:
    """Test circuit breaker functionality."""

    def test_circuit_breaker_initial_state(self):
        """Test circuit breaker starts in closed state."""
        cb = CircuitBreaker()
        assert cb.state == CircuitBreakerState.CLOSED
        assert cb.can_execute() is True
        assert cb.failure_count == 0

    def test_circuit_breaker_failure_threshold(self):
        """Test circuit breaker opens after failure threshold."""
        cb = CircuitBreaker(failure_threshold=3)

        # Record failures
        for _ in range(2):
            cb.record_failure()
            assert cb.state == CircuitBreakerState.CLOSED

        # Third failure should open circuit
        cb.record_failure()
        assert cb.state == CircuitBreakerState.OPEN
        assert cb.can_execute() is False

    def test_circuit_breaker_recovery(self):
        """Test circuit breaker recovery after timeout."""
        cb = CircuitBreaker(failure_threshold=1, recovery_timeout=0.1)

        # Trigger failure
        cb.record_failure()
        assert cb.state == CircuitBreakerState.OPEN

        # Wait for recovery timeout
        time.sleep(0.2)
        assert cb.can_execute() is True
        assert cb.state == CircuitBreakerState.HALF_OPEN

        # Success should close circuit
        cb.record_success()
        assert cb.state == CircuitBreakerState.CLOSED


class TestPerformanceMonitor:
    """Test performance monitoring functionality."""

    @pytest.fixture
    def monitor(self):
        """Create fresh performance monitor for testing."""
        return PerformanceMonitor(max_metrics_history=100)

    def test_record_metric(self, monitor):
        """Test metric recording."""
        monitor.record_metric("test_metric", 1.5, agent_id="test-agent")

        assert len(monitor.metrics_history) == 1
        metric = monitor.metrics_history[0]
        assert metric.name == "test_metric"
        assert metric.value == 1.5
        assert metric.metadata["agent_id"] == "test-agent"

    def test_measure_operation_context_manager(self, monitor):
        """Test operation measurement context manager."""
        with monitor.measure_operation("test_operation", "test-agent"):
            time.sleep(0.1)

        # Should have recorded a duration metric
        duration_metrics = [m for m in monitor.metrics_history if "duration" in m.name]
        assert len(duration_metrics) == 1
        assert duration_metrics[0].value >= 0.1

    def test_agent_stats_update(self, monitor):
        """Test agent statistics updates."""
        monitor.update_agent_stats("test-agent", True, 0.5, "request")

        stats = monitor.get_agent_stats("test-agent")
        assert stats is not None
        assert stats.total_requests == 1
        assert stats.successful_requests == 1
        assert stats.average_response_time == 0.5


class TestAgentSwitchNodeEnhanced:
    """Test the enhanced AgentSwitchNode functionality."""

    @pytest.fixture
    def mock_config_data(self):
        """Mock configuration data."""
        return {
            'patterns': [
                r"switch to (\w+)",
                r"talk to (\w+)",
                r"connect me with (\w+)"
            ],
            'aliases': {
                'concierge': ['help', 'support', 'assistant'],
                'analysis': ['analyze', 'data', 'analytics']
            },
            'rate_limiting': {
                'max_switches_per_minute': 5,
                'rate_limit_window_seconds': 60
            },
            'security': {
                'max_message_length': 500,
                'allowed_agent_id_pattern': r'^[a-zA-Z0-9_-]+$',
                'max_agent_id_length': 30
            }
        }

    @pytest.fixture
    def mock_agent_nodes(self):
        """Create mock agent nodes for testing."""
        return {
            "concierge-agent": Mock(),
            "analysis-agent": Mock(),
            "marketing-agent": Mock()
        }

    @pytest.fixture
    def agent_switch_node(self, mock_agent_nodes, mock_config_data):
        """Create AgentSwitchNode instance for testing."""
        with patch('builtins.open', mock_open()), \
             patch('yaml.safe_load', return_value=mock_config_data), \
             patch.object(Path, 'exists', return_value=True):
            return AgentSwitchNode(mock_agent_nodes)
    
    @pytest.fixture
    def sample_state(self):
        """Create sample state for testing."""
        return create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation",
            workflow_type="default"
        )

    def test_rate_limiting(self, agent_switch_node):
        """Test rate limiting functionality."""
        user_id = "test_user"

        # Should not be rate limited initially
        assert not agent_switch_node._is_rate_limited(user_id)

        # Record multiple requests
        for _ in range(5):  # Max is 5 per minute in mock config
            agent_switch_node._record_switch_request(user_id)

        # Should now be rate limited
        assert agent_switch_node._is_rate_limited(user_id)

    def test_security_validation(self, agent_switch_node):
        """Test security validation for agent IDs."""
        # Valid agent ID
        assert agent_switch_node._is_valid_agent_id("valid-agent-123")

        # Invalid agent IDs
        assert not agent_switch_node._is_valid_agent_id("invalid@agent")  # Special chars
        assert not agent_switch_node._is_valid_agent_id("a" * 50)  # Too long (max 30 in config)
        assert not agent_switch_node._is_valid_agent_id("")  # Empty
        assert not agent_switch_node._is_valid_agent_id(None)  # None

    def test_message_length_validation(self, agent_switch_node):
        """Test message length validation."""
        # Valid message
        assert agent_switch_node._is_explicit_switch_request("switch to analysis")

        # Message too long (max 500 in config)
        long_message = "switch to analysis " + "x" * 500
        assert not agent_switch_node._is_explicit_switch_request(long_message)

    def test_optimized_agent_lookup(self, agent_switch_node):
        """Test optimized agent lookup dictionary."""
        # Should have built lookup dictionary
        assert hasattr(agent_switch_node, 'agent_lookup_dict')
        assert len(agent_switch_node.agent_lookup_dict) > 0

        # Test alias lookup
        if 'help' in agent_switch_node.agent_lookup_dict:
            assert agent_switch_node.agent_lookup_dict['help'] == 'concierge-agent'

    def test_configuration_loading(self, agent_switch_node):
        """Test configuration loading from file."""
        # Should have loaded configuration
        assert hasattr(agent_switch_node, 'switch_rate_limit')
        assert agent_switch_node.switch_rate_limit == 5  # From mock config
        assert agent_switch_node.max_message_length == 500  # From mock config

    @pytest.mark.asyncio
    async def test_rate_limited_execution(self, agent_switch_node, sample_state):
        """Test execution with rate limiting."""
        # Set up rate limited user
        user_id = "rate_limited_user"
        for _ in range(10):  # Exceed rate limit
            agent_switch_node._record_switch_request(user_id)

        # Set up state
        sample_state["user_id"] = user_id
        sample_state["current_message"] = {
            "content": "switch to analysis",
            "timestamp": datetime.now().isoformat()
        }
        sample_state["messages"] = [sample_state["current_message"]]

        # Execute should add rate limit warning
        result_state = await agent_switch_node.execute(sample_state)

        # Should have added rate limit message
        messages = result_state["messages"]
        rate_limit_msg = next(
            (msg for msg in messages if "too many agent switch requests" in msg.get("content", "")),
            None
        )
        assert rate_limit_msg is not None
    
    def test_is_explicit_switch_request_positive_cases(self, agent_switch_node):
        """Test detection of explicit switch requests."""
        positive_cases = [
            "switch to analysis",
            "talk to marketing",
            "connect me with concierge",
            "I want to speak with analysis-agent",
            "can I talk to marketing",
            "transfer me to concierge",
            "route me to analysis",
            "send me to marketing",
            "go to concierge",
            "take me to analysis"
        ]
        
        for message in positive_cases:
            assert agent_switch_node._is_explicit_switch_request(message), \
                f"Should detect switch request in: {message}"
    
    def test_is_explicit_switch_request_negative_cases(self, agent_switch_node):
        """Test that non-switch messages are not detected."""
        negative_cases = [
            "Hello, how are you?",
            "I need help with analysis",
            "Can you analyze this data?",
            "What marketing strategies do you recommend?",
            "Please help me with my business",
            "",
            "switch",  # Incomplete
            "talk",    # Incomplete
        ]
        
        for message in negative_cases:
            assert not agent_switch_node._is_explicit_switch_request(message), \
                f"Should not detect switch request in: {message}"
    
    def test_extract_target_agent_success(self, agent_switch_node):
        """Test successful target agent extraction."""
        test_cases = [
            ("switch to analysis", "analysis-agent"),
            ("talk to marketing", "marketing-agent"),
            ("connect me with concierge", "concierge-agent"),
            ("I want to speak with analysis-agent", "analysis-agent")
        ]
        
        for message, expected_agent in test_cases:
            result = agent_switch_node._extract_target_agent(message)
            assert result == expected_agent, \
                f"Expected {expected_agent} from '{message}', got {result}"
    
    def test_extract_target_agent_not_found(self, agent_switch_node):
        """Test target agent extraction when agent doesn't exist."""
        result = agent_switch_node._extract_target_agent("switch to nonexistent")
        assert result is None
    
    @pytest.mark.asyncio
    async def test_execute_with_explicit_switch(self, agent_switch_node, sample_state):
        """Test execute method with explicit switch request."""
        # Setup state with switch message
        sample_state["current_message"] = {
            "content": "switch to analysis",
            "timestamp": datetime.now().isoformat()
        }
        sample_state["messages"] = [sample_state["current_message"]]
        
        # Execute
        result_state = await agent_switch_node.execute(sample_state)
        
        # Verify switch occurred
        assert result_state["selected_agent"] == "analysis-agent"
        assert result_state["conversation_mode"] == ConversationMode.AGENT_SWITCH
        
        # Verify confirmation message was added
        messages = result_state["messages"]
        assert len(messages) >= 2  # Original + confirmation
        confirmation_msg = messages[-1]
        assert "Switching you to analysis-agent" in confirmation_msg["content"]
    
    @pytest.mark.asyncio
    async def test_execute_without_switch_request(self, agent_switch_node, sample_state):
        """Test execute method without switch request."""
        # Setup state with normal message
        sample_state["current_message"] = {
            "content": "Hello, how are you?",
            "timestamp": datetime.now().isoformat()
        }
        sample_state["messages"] = [sample_state["current_message"]]
        
        original_selected_agent = sample_state.get("selected_agent")
        
        # Execute
        result_state = await agent_switch_node.execute(sample_state)
        
        # Verify no switch occurred
        assert result_state["selected_agent"] == original_selected_agent
        assert len(result_state["messages"]) == 1  # No additional messages


class TestBaseAgentNodeCoordination:
    """Test agent coordination features in BaseAgentNode."""
    
    @pytest.fixture
    def mock_agent_node(self):
        """Create mock BaseAgentNode for testing."""
        class MockAgentNode(BaseAgentNode):
            async def _process_message(self, state):
                return state
        
        return MockAgentNode("test-agent", "test", {})
    
    @pytest.fixture
    def sample_state_with_agents(self):
        """Create sample state with available agents."""
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation",
            workflow_type="default"
        )
        state["available_agents"] = ["concierge-agent", "analysis-agent", "marketing-agent"]
        return state
    
    @pytest.mark.asyncio
    async def test_coordinate_with_specialist_success(self, mock_agent_node, sample_state_with_agents):
        """Test successful specialist coordination."""
        result = await mock_agent_node._coordinate_with_specialist(
            sample_state_with_agents,
            "analysis-agent",
            {"context": "test consultation"}
        )
        
        assert result["success"] is True
        assert result["specialist_agent"] == "analysis-agent"
        assert "insights" in result
        assert len(result["insights"]) > 0
        
        # Verify coordination log was updated
        assert "agent_coordination_log" in sample_state_with_agents
        coordination_log = sample_state_with_agents["agent_coordination_log"]
        assert len(coordination_log) > 0
        assert coordination_log[-1]["type"] == "specialist_consultation"
    
    @pytest.mark.asyncio
    async def test_coordinate_with_unavailable_specialist(self, mock_agent_node, sample_state_with_agents):
        """Test coordination with unavailable specialist."""
        result = await mock_agent_node._coordinate_with_specialist(
            sample_state_with_agents,
            "nonexistent-agent"
        )
        
        assert result["success"] is False
        assert "error" in result
        assert "not available" in result["error"]
    
    @pytest.mark.asyncio
    async def test_suggest_handoff(self, mock_agent_node, sample_state_with_agents):
        """Test handoff suggestion functionality."""
        result_state = await mock_agent_node._suggest_handoff(
            sample_state_with_agents,
            "analysis-agent",
            "better suited for data analysis",
            confidence=0.9
        )
        
        # Verify handoff suggestion was stored
        assert "pending_handoff_suggestion" in result_state
        suggestion = result_state["pending_handoff_suggestion"]
        assert suggestion["target_agent"] == "analysis-agent"
        assert suggestion["confidence"] == 0.9
        assert suggestion["status"] == "pending_user_response"
        
        # Verify suggestion message was added
        messages = result_state["messages"]
        suggestion_msg = messages[-1]
        assert "analysis-agent specialist might be better suited" in suggestion_msg["content"]
        assert "Would you like me to connect you" in suggestion_msg["content"]
    
    @pytest.mark.asyncio
    async def test_simulate_specialist_consultation(self, mock_agent_node, sample_state_with_agents):
        """Test specialist consultation simulation."""
        consultation_request = {
            "requesting_agent": "test-agent",
            "specialist_agent": "analysis-agent",
            "current_message": {"content": "analyze this data"}
        }
        
        insights = await mock_agent_node._simulate_specialist_consultation(
            "analysis-agent",
            consultation_request,
            sample_state_with_agents
        )
        
        assert len(insights) > 0
        assert any(insight["type"] == "data_analysis_recommendation" for insight in insights)
        assert all("specialist" in insight for insight in insights)


class TestWorkflowManagerEnhancements:
    """Test WorkflowManager enhancements for Phase 2."""
    
    @pytest.fixture
    def workflow_manager(self):
        """Create WorkflowManager instance for testing."""
        with patch('backend.agents.langgraph.core.workflow_manager.WorkflowManager._initialize_agents'):
            manager = WorkflowManager()
            # Mock some agents
            manager.agent_nodes = {
                "concierge-agent": Mock(),
                "analysis-agent": Mock(),
                "marketing-agent": Mock()
            }
            return manager
    
    def test_determine_initial_agent_with_selection(self, workflow_manager):
        """Test initial agent determination with user selection."""
        result = workflow_manager._determine_initial_agent("analysis-agent")
        assert result == "analysis-agent"
    
    def test_determine_initial_agent_fallback_to_concierge(self, workflow_manager):
        """Test fallback to concierge when no selection."""
        result = workflow_manager._determine_initial_agent(None)
        assert result == "concierge-agent"
    
    def test_determine_initial_agent_invalid_selection(self, workflow_manager):
        """Test fallback when invalid agent selected."""
        result = workflow_manager._determine_initial_agent("nonexistent-agent")
        assert result == "concierge-agent"
    
    def test_initialize_workflow_state(self, workflow_manager):
        """Test workflow state initialization."""
        state = workflow_manager._initialize_workflow_state(
            user_id="test_user",
            conversation_id="test_conversation",
            selected_agent="analysis-agent",
            context={"message": "test message"}
        )
        
        assert state["selected_agent"] == "analysis-agent"
        assert state["conversation_mode"] == ConversationMode.CONVERSATION
        assert state["available_agents"] == list(workflow_manager.agent_nodes.keys())
        assert "agent_coordination_log" in state
        
        # Verify initialization record
        coordination_log = state["agent_coordination_log"]
        assert len(coordination_log) > 0
        assert coordination_log[0]["action"] == "workflow_initialized"
        assert coordination_log[0]["initial_agent"] == "analysis-agent"


class TestBackwardCompatibility:
    """Test backward compatibility with existing RoutingNode."""
    
    @pytest.fixture
    def mock_agent_nodes(self):
        """Create mock agent nodes for testing."""
        return {
            "concierge-agent": Mock(),
            "analysis-agent": Mock()
        }
    
    def test_routing_node_extends_agent_switch_node(self, mock_agent_nodes):
        """Test that RoutingNode extends AgentSwitchNode for compatibility."""
        routing_node = RoutingNode(mock_agent_nodes)
        assert isinstance(routing_node, AgentSwitchNode)
    
    def test_routing_node_has_legacy_methods(self, mock_agent_nodes):
        """Test that RoutingNode has legacy methods for compatibility."""
        routing_node = RoutingNode(mock_agent_nodes)
        
        # Test legacy methods exist and return expected types
        assert hasattr(routing_node, '_detect_intent')
        assert hasattr(routing_node, '_analyze_context')
        assert hasattr(routing_node, '_score_agents')
        assert hasattr(routing_node, '_select_agent')
        
        # Test they return appropriate defaults
        assert routing_node._detect_intent("test") == "general_inquiry"
        assert routing_node._analyze_context({}) == {}
        assert routing_node._score_agents("test", "intent", {}) == {"concierge-agent": 5.0}


if __name__ == "__main__":
    pytest.main([__file__])
