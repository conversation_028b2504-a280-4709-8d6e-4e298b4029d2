"""
MCP Agent Integration Service for Datagenius.

This service integrates discovered MCP tools with the AI agent system,
making MCP server capabilities available to all personas in the platform.
"""

import json
import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Tuple, TYPE_CHECKING
from sqlalchemy.orm import Session

if TYPE_CHECKING:
    from agents.tools.mcp.agent_integration import Agent<PERSON><PERSON><PERSON>ool

from ..database import (
    MCPServer, MCPTool, MCPResource, MCPPrompt, 
    BusinessProfile, get_db
)
from ..models.mcp_server import MCPServerStatus
from ..services.mcp_server_manager import MCPServerManager
from ..utils.mcp_client import EnhancedMCPClient
# Import will be done dynamically to avoid circular imports

logger = logging.getLogger(__name__)


class MCPAgentIntegration:
    """Service for integrating MCP servers with the AI agent system."""
    
    def __init__(self):
        """Initialize the MCP agent integration service."""
        self.mcp_manager = MCPServerManager()
        self.active_tools: Dict[str, Dict[str, Any]] = {}  # server_id -> tools
        self.tool_registry: Dict[str, Any] = {}  # tool_id -> agent tool
    
    async def initialize_for_business_profile(
        self, 
        db: Session, 
        business_profile_id: str
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Initialize MCP tools for a specific business profile.
        
        Args:
            db: Database session
            business_profile_id: Business profile ID
            
        Returns:
            Dictionary containing available tools, resources, and prompts
        """
        logger.info(f"Initializing MCP tools for business profile: {business_profile_id}")
        
        # Get active MCP servers for the business profile
        servers = db.query(MCPServer).filter(
            MCPServer.business_profile_id == business_profile_id,
            MCPServer.status == MCPServerStatus.ACTIVE
        ).all()
        
        available_capabilities = {
            "tools": [],
            "resources": [],
            "prompts": []
        }
        
        for server in servers:
            try:
                # Ensure server is connected
                if server.id not in self.mcp_manager.active_clients:
                    success = await self.mcp_manager.start_server(db, server.id)
                    if not success:
                        logger.warning(f"Failed to start MCP server {server.id}")
                        continue
                
                # Get server capabilities
                capabilities = await self._get_server_capabilities(db, server)
                
                # Add to available capabilities
                available_capabilities["tools"].extend(capabilities["tools"])
                available_capabilities["resources"].extend(capabilities["resources"])
                available_capabilities["prompts"].extend(capabilities["prompts"])
                
                # Register tools for agent use
                await self._register_server_tools(db, server, capabilities["tools"])
                
            except Exception as e:
                logger.error(f"Error initializing server {server.id}: {e}")
                continue
        
        logger.info(
            f"Initialized {len(available_capabilities['tools'])} tools, "
            f"{len(available_capabilities['resources'])} resources, "
            f"{len(available_capabilities['prompts'])} prompts for profile {business_profile_id}"
        )
        
        return available_capabilities
    
    async def get_available_tools_for_agent(
        self, 
        db: Session, 
        user_id: int, 
        business_profile_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get available MCP tools for an AI agent.
        
        Args:
            db: Database session
            user_id: User ID
            business_profile_id: Optional business profile ID
            
        Returns:
            List of available tools with metadata
        """
        query = db.query(MCPServer).filter(MCPServer.user_id == user_id)
        
        if business_profile_id:
            query = query.filter(MCPServer.business_profile_id == business_profile_id)
        
        servers = query.filter(MCPServer.status == MCPServerStatus.ACTIVE).all()
        
        available_tools = []
        
        for server in servers:
            tools = db.query(MCPTool).filter(
                MCPTool.server_id == server.id,
                MCPTool.is_enabled == True
            ).all()
            
            for tool in tools:
                tool_info = {
                    "id": f"mcp_{server.id}_{tool.tool_name}",
                    "name": tool.tool_name,
                    "description": tool.tool_description or f"Tool from {server.name}",
                    "server_name": server.name,
                    "server_id": server.id,
                    "parameters": tool.parameters or {},
                    "capabilities": tool.capabilities or {},
                    "usage_count": tool.usage_count,
                    "type": "mcp_tool"
                }
                available_tools.append(tool_info)
        
        return available_tools
    
    async def execute_mcp_tool(
        self, 
        db: Session, 
        tool_id: str, 
        arguments: Dict[str, Any],
        user_id: int
    ) -> Dict[str, Any]:
        """
        Execute an MCP tool.
        
        Args:
            db: Database session
            tool_id: Tool ID in format "mcp_{server_id}_{tool_name}"
            arguments: Tool arguments
            user_id: User ID for authorization
            
        Returns:
            Tool execution result
        """
        try:
            # Parse tool ID
            if not tool_id.startswith("mcp_"):
                raise ValueError("Invalid MCP tool ID format")
            
            parts = tool_id[4:].split("_", 1)  # Remove "mcp_" prefix
            if len(parts) != 2:
                raise ValueError("Invalid MCP tool ID format")
            
            server_id, tool_name = parts
            
            # Verify server ownership
            server = db.query(MCPServer).filter(
                MCPServer.id == server_id,
                MCPServer.user_id == user_id
            ).first()
            
            if not server:
                raise ValueError("MCP server not found or access denied")
            
            # Get MCP client
            client = self.mcp_manager.active_clients.get(server_id)
            if not client:
                # Try to start the server
                success = await self.mcp_manager.start_server(db, server_id)
                if not success:
                    raise RuntimeError("Failed to start MCP server")
                client = self.mcp_manager.active_clients.get(server_id)
            
            if not client:
                raise RuntimeError("MCP client not available")
            
            # Execute the tool
            result = await client.call_tool(tool_name, arguments)
            
            # Update usage statistics
            tool = db.query(MCPTool).filter(
                MCPTool.server_id == server_id,
                MCPTool.tool_name == tool_name
            ).first()
            
            if tool:
                tool.usage_count += 1
                tool.last_used_at = datetime.now(timezone.utc)
                db.commit()
            
            return {
                "success": True,
                "result": result,
                "tool_name": tool_name,
                "server_name": server.name
            }
            
        except Exception as e:
            logger.error(f"Error executing MCP tool {tool_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "tool_id": tool_id
            }
    
    async def get_mcp_resources(
        self, 
        db: Session, 
        user_id: int, 
        business_profile_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get available MCP resources for context."""
        query = db.query(MCPServer).filter(MCPServer.user_id == user_id)
        
        if business_profile_id:
            query = query.filter(MCPServer.business_profile_id == business_profile_id)
        
        servers = query.filter(MCPServer.status == MCPServerStatus.ACTIVE).all()
        
        available_resources = []
        
        for server in servers:
            resources = db.query(MCPResource).filter(
                MCPResource.server_id == server.id
            ).all()
            
            for resource in resources:
                resource_info = {
                    "id": f"mcp_resource_{server.id}_{resource.resource_name}",
                    "name": resource.resource_name,
                    "type": resource.resource_type,
                    "description": resource.resource_description,
                    "server_name": server.name,
                    "server_id": server.id,
                    "parameters": resource.parameters or {}
                }
                available_resources.append(resource_info)
        
        return available_resources
    
    async def read_mcp_resource(
        self, 
        db: Session, 
        resource_id: str, 
        user_id: int
    ) -> Dict[str, Any]:
        """Read content from an MCP resource."""
        try:
            # Parse resource ID
            if not resource_id.startswith("mcp_resource_"):
                raise ValueError("Invalid MCP resource ID format")
            
            parts = resource_id[13:].split("_", 1)  # Remove "mcp_resource_" prefix
            if len(parts) != 2:
                raise ValueError("Invalid MCP resource ID format")
            
            server_id, resource_name = parts
            
            # Verify server ownership
            server = db.query(MCPServer).filter(
                MCPServer.id == server_id,
                MCPServer.user_id == user_id
            ).first()
            
            if not server:
                raise ValueError("MCP server not found or access denied")
            
            # Get MCP client
            client = self.mcp_manager.active_clients.get(server_id)
            if not client:
                raise RuntimeError("MCP client not available")
            
            # Read the resource
            resource_uri = f"resource://{resource_name}"
            result = await client.read_resource(resource_uri)
            
            return {
                "success": True,
                "content": result,
                "resource_name": resource_name,
                "server_name": server.name
            }
            
        except Exception as e:
            logger.error(f"Error reading MCP resource {resource_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "resource_id": resource_id
            }
    
    async def get_mcp_prompts(
        self, 
        db: Session, 
        user_id: int, 
        business_profile_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get available MCP prompts."""
        query = db.query(MCPServer).filter(MCPServer.user_id == user_id)
        
        if business_profile_id:
            query = query.filter(MCPServer.business_profile_id == business_profile_id)
        
        servers = query.filter(MCPServer.status == MCPServerStatus.ACTIVE).all()
        
        available_prompts = []
        
        for server in servers:
            prompts = db.query(MCPPrompt).filter(
                MCPPrompt.server_id == server.id
            ).all()
            
            for prompt in prompts:
                prompt_info = {
                    "id": f"mcp_prompt_{server.id}_{prompt.prompt_name}",
                    "name": prompt.prompt_name,
                    "description": prompt.prompt_description,
                    "template": prompt.template,
                    "server_name": server.name,
                    "server_id": server.id,
                    "parameters": prompt.parameters or {}
                }
                available_prompts.append(prompt_info)
        
        return available_prompts
    
    async def _get_server_capabilities(
        self, 
        db: Session, 
        server: MCPServer
    ) -> Dict[str, List[Dict[str, Any]]]:
        """Get capabilities from a server."""
        capabilities = {
            "tools": [],
            "resources": [],
            "prompts": []
        }
        
        # Get tools
        tools = db.query(MCPTool).filter(MCPTool.server_id == server.id).all()
        for tool in tools:
            capabilities["tools"].append({
                "id": f"mcp_{server.id}_{tool.tool_name}",
                "name": tool.tool_name,
                "description": tool.tool_description,
                "parameters": tool.parameters,
                "capabilities": tool.capabilities,
                "is_enabled": tool.is_enabled
            })
        
        # Get resources
        resources = db.query(MCPResource).filter(MCPResource.server_id == server.id).all()
        for resource in resources:
            capabilities["resources"].append({
                "id": f"mcp_resource_{server.id}_{resource.resource_name}",
                "name": resource.resource_name,
                "type": resource.resource_type,
                "description": resource.resource_description,
                "parameters": resource.parameters
            })
        
        # Get prompts
        prompts = db.query(MCPPrompt).filter(MCPPrompt.server_id == server.id).all()
        for prompt in prompts:
            capabilities["prompts"].append({
                "id": f"mcp_prompt_{server.id}_{prompt.prompt_name}",
                "name": prompt.prompt_name,
                "description": prompt.prompt_description,
                "template": prompt.template,
                "parameters": prompt.parameters
            })
        
        return capabilities
    
    async def _register_server_tools(
        self, 
        db: Session, 
        server: MCPServer, 
        tools: List[Dict[str, Any]]
    ) -> None:
        """Register server tools for agent use."""
        for tool_info in tools:
            if not tool_info.get("is_enabled", True):
                continue
            
            tool_id = tool_info["id"]
            
            # Create agent-compatible tool wrapper
            # Import here to avoid circular imports
            from agents.tools.mcp.agent_integration import AgentMCPTool

            agent_tool = AgentMCPTool(
                name=tool_info["name"],
                description=tool_info["description"] or f"MCP tool from {server.name}",
                server_id=server.id,
                tool_name=tool_info["name"],
                parameters=tool_info.get("parameters", {}),
                integration_service=self
            )
            
            self.tool_registry[tool_id] = agent_tool
            
            # Store in active tools
            if server.id not in self.active_tools:
                self.active_tools[server.id] = {}
            
            self.active_tools[server.id][tool_info["name"]] = tool_info
    
    def get_registered_tools(self) -> Dict[str, Any]:
        """Get all registered MCP tools for agents."""
        return self.tool_registry.copy()
    
    async def cleanup_inactive_servers(self, db: Session) -> None:
        """Clean up tools from inactive servers."""
        inactive_servers = db.query(MCPServer).filter(
            MCPServer.status != MCPServerStatus.ACTIVE
        ).all()
        
        for server in inactive_servers:
            # Remove from active tools
            if server.id in self.active_tools:
                del self.active_tools[server.id]
            
            # Remove from tool registry
            tools_to_remove = [
                tool_id for tool_id in self.tool_registry.keys()
                if tool_id.startswith(f"mcp_{server.id}_")
            ]
            
            for tool_id in tools_to_remove:
                del self.tool_registry[tool_id]


# Global instance
mcp_agent_integration = MCPAgentIntegration()
