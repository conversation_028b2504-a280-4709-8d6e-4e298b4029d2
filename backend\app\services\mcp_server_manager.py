"""
Enhanced MCP Server Manager Service for Datagenius.

This service manages MCP server configurations, connections, and tool discovery
with support for multiple transport types and VS Code-compatible JSON configurations.
"""

import json
import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Tuple
from sqlalchemy.orm import Session
from cryptography.fernet import Fernet

from ..database import (
    MCPServer, MCPTool, MCPResource, MCPPrompt, MCPInputVariable,
    get_db
)
from ..models.mcp_server import (
    MCPServerCreate, MCPServerUpdate, MCPServerResponse,
    MCPJSONConfiguration, MCPServerConfig, MCPTransportType,
    MCPServerStatus, MCPConfigType, MCPInputDefinition, MCPServerDefinition
)
from ..utils.mcp_client import EnhancedMCPClient
from ..utils.performance import performance_monitor, cached, connection_pool, resource_manager, test_mcp_connection
from .. import config

logger = logging.getLogger(__name__)


class MCPServerManager:
    """Enhanced MCP server management service."""
    
    def __init__(self):
        """Initialize the MCP server manager."""
        self.encryption_key = self._get_encryption_key()
        self.active_clients: Dict[str, EnhancedMCPClient] = {}
    
    def _get_encryption_key(self) -> bytes:
        """Get encryption key for secure credential storage."""
        key = getattr(config, 'MCP_ENCRYPTION_KEY', None)
        if not key:
            # Generate a default key for development
            from cryptography.fernet import Fernet
            key = Fernet.generate_key().decode()
            logger.warning(
                "MCP_ENCRYPTION_KEY not set, using generated key. "
                "For production, set MCP_ENCRYPTION_KEY in environment variables. "
                f"Generated key: {key}"
            )
        return key if isinstance(key, bytes) else key.encode()
    
    def _encrypt_value(self, value: str) -> str:
        """Encrypt a sensitive value."""
        fernet = Fernet(self.encryption_key)
        return fernet.encrypt(value.encode()).decode()
    
    def _decrypt_value(self, encrypted_value: str) -> str:
        """Decrypt a sensitive value."""
        fernet = Fernet(self.encryption_key)
        return fernet.decrypt(encrypted_value.encode()).decode()
    
    @performance_monitor("create_server_from_json")
    async def create_server_from_json(
        self,
        db: Session,
        user_id: int,
        server_data: MCPServerCreate
    ) -> MCPServer:
        """Create an MCP server from JSON configuration."""
        if not server_data.json_config:
            raise ValueError("JSON configuration is required")

        try:
            # Process input variables
            input_variables = await self._process_input_variables(
                db, user_id, server_data.json_config.inputs
            )

            # Process server configurations
            servers_config = await self._process_server_configurations(
                server_data.json_config.servers
            )

            # Create main server record
            server = await self._create_server_record(
                db, user_id, server_data, servers_config, input_variables
            )

            db.commit()
            db.refresh(server)

            return server

        except Exception as e:
            db.rollback()
            logger.error(f"Failed to create MCP server from JSON: {e}")
            raise
    
    async def create_server_from_form(
        self, 
        db: Session, 
        user_id: int, 
        server_data: MCPServerCreate
    ) -> MCPServer:
        """Create an MCP server from form configuration."""
        if not server_data.server_config:
            raise ValueError("Server configuration is required")
        
        # Convert form config to internal format
        config_dict = server_data.server_config.model_dump()
        
        server = MCPServer(
            id=str(uuid.uuid4()),
            user_id=user_id,
            business_profile_id=server_data.business_profile_id,
            name=server_data.name,
            description=server_data.description,
            config_type=MCPConfigType.FORM,
            transport_type=server_data.server_config.transport_type,
            configuration=config_dict,
            status=MCPServerStatus.INACTIVE
        )
        
        db.add(server)
        db.commit()
        db.refresh(server)
        
        return server
    
    async def update_server(
        self,
        db: Session,
        server_id: str,
        update_data: MCPServerUpdate
    ) -> Optional[MCPServer]:
        """Update an MCP server configuration."""
        try:
            server = db.query(MCPServer).filter(MCPServer.id == server_id).first()
            if not server:
                return None

            # Update basic fields
            if update_data.name is not None:
                server.name = update_data.name
            if update_data.description is not None:
                server.description = update_data.description
            if update_data.business_profile_id is not None:
                server.business_profile_id = update_data.business_profile_id

            # Update configuration
            if update_data.json_config is not None:
                # Process JSON configuration update
                await self._update_json_configuration(db, server, update_data.json_config)

            if update_data.server_config is not None:
                server.configuration = update_data.server_config.model_dump()
                server.transport_type = update_data.server_config.transport_type

            server.updated_at = datetime.now(timezone.utc)
            db.commit()
            db.refresh(server)

            return server

        except Exception as e:
            db.rollback()
            logger.error(f"Failed to update MCP server {server_id}: {e}")
            raise

    async def _update_json_configuration(
        self,
        db: Session,
        server: MCPServer,
        json_config: 'MCPJSONConfiguration'
    ) -> None:
        """Update JSON configuration for an MCP server."""
        try:
            # Update input variables
            if json_config.inputs:
                # Remove existing input variables for this server
                existing_vars = db.query(MCPInputVariable).filter(
                    MCPInputVariable.user_id == server.user_id
                ).all()

                existing_var_ids = {var.variable_id for var in existing_vars}
                new_var_ids = {input_def.id for input_def in json_config.inputs}

                # Remove variables that are no longer needed
                vars_to_remove = existing_var_ids - new_var_ids
                if vars_to_remove:
                    db.query(MCPInputVariable).filter(
                        MCPInputVariable.user_id == server.user_id,
                        MCPInputVariable.variable_id.in_(vars_to_remove)
                    ).delete(synchronize_session=False)

                # Add or update variables
                input_variables = {}
                for input_def in json_config.inputs:
                    existing_var = next(
                        (var for var in existing_vars if var.variable_id == input_def.id),
                        None
                    )

                    if existing_var:
                        # Update existing variable
                        existing_var.variable_type = input_def.type
                        existing_var.description = input_def.description
                        existing_var.is_password = input_def.password
                        input_variables[input_def.id] = existing_var.id
                    else:
                        # Create new variable
                        variable_id = str(uuid.uuid4())
                        input_var = MCPInputVariable(
                            id=variable_id,
                            user_id=server.user_id,
                            variable_id=input_def.id,
                            variable_type=input_def.type,
                            description=input_def.description,
                            is_password=input_def.password,
                            encrypted_value=""  # Will be set when user provides value
                        )
                        db.add(input_var)
                        input_variables[input_def.id] = variable_id
            else:
                input_variables = {}

            # Update server configurations
            servers_config = {}
            for server_name, server_def in json_config.servers.items():
                # Determine transport type
                transport_type = MCPTransportType.HTTP
                if server_def.type == "stdio":
                    transport_type = MCPTransportType.STDIO
                elif server_def.url and "ws" in server_def.url:
                    transport_type = MCPTransportType.WEBSOCKET

                # Build configuration
                config_dict = {
                    "name": server_name,
                    "transport_type": transport_type,
                    **server_def.model_dump(exclude_none=True)
                }

                servers_config[server_name] = config_dict

            # Update server configuration
            server.configuration = {
                "servers": servers_config,
                "input_variables": input_variables
            }
            server.config_type = MCPConfigType.JSON

        except Exception as e:
            logger.error(f"Failed to update JSON configuration for server {server.id}: {e}")
            raise

    async def _process_input_variables(
        self,
        db: Session,
        user_id: int,
        inputs: Optional[List['MCPInputDefinition']]
    ) -> Dict[str, str]:
        """Process input variable definitions."""
        input_variables = {}

        if inputs:
            for input_def in inputs:
                # Store input variable securely
                variable_id = str(uuid.uuid4())
                input_var = MCPInputVariable(
                    id=variable_id,
                    user_id=user_id,
                    variable_id=input_def.id,
                    variable_type=input_def.type,
                    description=input_def.description,
                    is_password=input_def.password,
                    encrypted_value=""  # Will be set when user provides value
                )
                db.add(input_var)
                input_variables[input_def.id] = variable_id

        return input_variables

    async def _process_server_configurations(
        self,
        servers: Dict[str, 'MCPServerDefinition']
    ) -> Dict[str, Dict[str, Any]]:
        """Process server configuration definitions."""
        servers_config = {}

        for server_name, server_def in servers.items():
            # Determine transport type
            transport_type = MCPTransportType.HTTP
            if server_def.type == "stdio":
                transport_type = MCPTransportType.STDIO
            elif server_def.url and "ws" in server_def.url:
                transport_type = MCPTransportType.WEBSOCKET

            # Build configuration
            config_dict = {
                "name": server_name,
                "transport_type": transport_type,
                **server_def.model_dump(exclude_none=True)
            }

            servers_config[server_name] = config_dict

        return servers_config

    async def _create_server_record(
        self,
        db: Session,
        user_id: int,
        server_data: MCPServerCreate,
        servers_config: Dict[str, Dict[str, Any]],
        input_variables: Dict[str, str]
    ) -> MCPServer:
        """Create the main server database record."""
        server = MCPServer(
            id=str(uuid.uuid4()),
            user_id=user_id,
            business_profile_id=server_data.business_profile_id,
            name=server_data.name,
            description=server_data.description,
            config_type=MCPConfigType.JSON,
            transport_type=MCPTransportType.HTTP,  # Will be updated based on first server
            configuration={
                "servers": servers_config,
                "input_variables": input_variables
            },
            status=MCPServerStatus.INACTIVE
        )

        db.add(server)
        return server
    
    @performance_monitor("test_server_connection")
    async def test_server_connection(self, db: Session, server_id: str) -> Tuple[bool, Optional[str]]:
        """Test connection to an MCP server."""
        server = db.query(MCPServer).filter(MCPServer.id == server_id).first()
        if not server:
            return False, "Server not found"
        
        try:
            # Build configuration for testing
            config_dict = self._build_client_config(db, server)
            
            # Test connection
            success = await test_mcp_connection(config_dict)
            
            # Update server status
            if success:
                server.status = MCPServerStatus.ACTIVE
                server.last_connected_at = datetime.now(timezone.utc)
                server.error_message = None
            else:
                server.status = MCPServerStatus.ERROR
                server.error_message = "Connection test failed"
            
            db.commit()
            return success, server.error_message
            
        except Exception as e:
            error_msg = f"Connection test failed: {str(e)}"
            server.status = MCPServerStatus.ERROR
            server.error_message = error_msg
            db.commit()
            return False, error_msg
    
    @performance_monitor("start_server")
    async def start_server(self, db: Session, server_id: str) -> bool:
        """Start an MCP server connection."""
        server = db.query(MCPServer).filter(MCPServer.id == server_id).first()
        if not server:
            return False
        
        try:
            # Build client configuration
            config_dict = self._build_client_config(db, server)
            
            # Create and connect client
            client = EnhancedMCPClient(config_dict)
            connected = await client.connect()
            
            if connected:
                # Initialize the session
                await client.initialize()
                
                # Store active client
                self.active_clients[server_id] = client
                
                # Update server status
                server.status = MCPServerStatus.ACTIVE
                server.last_connected_at = datetime.now(timezone.utc)
                server.error_message = None
                
                # Discover tools, resources, and prompts
                await self._discover_server_capabilities(db, server, client)
                
                db.commit()
                return True
            else:
                server.status = MCPServerStatus.ERROR
                server.error_message = "Failed to connect"
                db.commit()
                return False
                
        except Exception as e:
            logger.error(f"Failed to start server {server_id}: {e}")
            server.status = MCPServerStatus.ERROR
            server.error_message = str(e)
            db.commit()
            return False
    
    async def stop_server(self, db: Session, server_id: str) -> bool:
        """Stop an MCP server connection."""
        server = db.query(MCPServer).filter(MCPServer.id == server_id).first()
        if not server:
            return False
        
        # Disconnect client if active
        if server_id in self.active_clients:
            client = self.active_clients[server_id]
            await client.disconnect()
            del self.active_clients[server_id]
        
        # Update server status
        server.status = MCPServerStatus.INACTIVE
        db.commit()
        
        return True
    
    def _build_client_config(self, db: Session, server: MCPServer) -> Dict[str, Any]:
        """Build client configuration from server record."""
        config = server.configuration.copy()
        
        # Resolve input variables if present
        if "input_variables" in config:
            for var_id, db_var_id in config["input_variables"].items():
                input_var = db.query(MCPInputVariable).filter(
                    MCPInputVariable.id == db_var_id
                ).first()
                if input_var and input_var.encrypted_value:
                    decrypted_value = self._decrypt_value(input_var.encrypted_value)
                    # Replace variable references in config
                    config = self._replace_variable_references(config, var_id, decrypted_value)
        
        return config
    
    def _replace_variable_references(self, config: Dict[str, Any], var_id: str, value: str) -> Dict[str, Any]:
        """Replace variable references in configuration."""
        config_str = json.dumps(config)
        config_str = config_str.replace(f"${{input:{var_id}}}", value)
        return json.loads(config_str)
    
    async def _discover_server_capabilities(
        self, 
        db: Session, 
        server: MCPServer, 
        client: EnhancedMCPClient
    ) -> None:
        """Discover and store server capabilities (tools, resources, prompts)."""
        try:
            # Clear existing capabilities
            db.query(MCPTool).filter(MCPTool.server_id == server.id).delete()
            db.query(MCPResource).filter(MCPResource.server_id == server.id).delete()
            db.query(MCPPrompt).filter(MCPPrompt.server_id == server.id).delete()
            
            # Discover tools
            tools = await client.list_tools()
            for tool_data in tools:
                tool = MCPTool(
                    id=str(uuid.uuid4()),
                    server_id=server.id,
                    tool_name=tool_data.get("name", ""),
                    tool_description=tool_data.get("description", ""),
                    parameters=tool_data.get("inputSchema", {}),
                    capabilities=tool_data
                )
                db.add(tool)
            
            # Discover resources
            resources = await client.list_resources()
            for resource_data in resources:
                resource = MCPResource(
                    id=str(uuid.uuid4()),
                    server_id=server.id,
                    resource_type=resource_data.get("mimeType", ""),
                    resource_name=resource_data.get("name", ""),
                    resource_description=resource_data.get("description", ""),
                    parameters=resource_data
                )
                db.add(resource)
            
            # Discover prompts
            prompts = await client.list_prompts()
            for prompt_data in prompts:
                prompt = MCPPrompt(
                    id=str(uuid.uuid4()),
                    server_id=server.id,
                    prompt_name=prompt_data.get("name", ""),
                    prompt_description=prompt_data.get("description", ""),
                    template=prompt_data.get("template", ""),
                    parameters=prompt_data.get("arguments", {})
                )
                db.add(prompt)
            
            db.commit()
            
        except Exception as e:
            logger.error(f"Failed to discover capabilities for server {server.id}: {e}")
    
    async def get_server_status(self, server_id: str) -> Dict[str, Any]:
        """Get the current status of an MCP server."""
        is_active = server_id in self.active_clients
        client = self.active_clients.get(server_id)
        
        return {
            "server_id": server_id,
            "is_active": is_active,
            "is_connected": client.is_connected() if client else False,
            "last_check": datetime.now(timezone.utc).isoformat()
        }
