/**
 * Enhanced MCP Server Configuration Component
 * 
 * Provides a comprehensive interface for configuring MCP servers with support for:
 * - JSON configuration (VS Code compatible)
 * - Form-based configuration
 * - Template gallery
 * - Real-time validation
 * - Import/Export functionality
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Code, 
  FileText, 
  Download, 
  Upload, 
  Copy, 
  Check, 
  AlertCircle, 
  Play, 
  Square, 
  RotateCcw,
  Settings,
  Zap
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { 
  MCPServerCreate, 
  MCPJSONConfiguration, 
  MCPServerConfig,
  MCPTransportType,
  MCPConfigType,
  mcpServerTemplates,
  validateMCPConfiguration,
  mcpServerApi
} from '@/lib/mcpServerApi';

interface MCPServerConfigProps {
  onServerCreated?: (server: any) => void;
  onCancel?: () => void;
  initialData?: Partial<MCPServerCreate>;
  mode?: 'create' | 'edit';
}

export const MCPServerConfig: React.FC<MCPServerConfigProps> = ({
  onServerCreated,
  onCancel,
  initialData,
  mode = 'create'
}) => {
  const { toast } = useToast();
  const [configMode, setConfigMode] = useState<'simple' | 'json'>('simple');
  const [serverData, setServerData] = useState<MCPServerCreate>({
    name: '',
    description: '',
    config_type: 'form' as MCPConfigType,
    server_config: {
      name: '',
      transport_type: 'http' as MCPTransportType,
      url: ''
    }
  });
  const [jsonConfig, setJsonConfig] = useState<string>('');
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [copied, setCopied] = useState(false);

  // Initialize with provided data
  useEffect(() => {
    if (initialData) {
      setServerData(prev => ({ ...prev, ...initialData }));
      if (initialData.json_config) {
        setJsonConfig(JSON.stringify(initialData.json_config, null, 2));
        setConfigMode('json');
      }
    }
  }, [initialData]);

  // Validate JSON configuration
  useEffect(() => {
    if (configMode === 'json' && jsonConfig.trim()) {
      try {
        const config = JSON.parse(jsonConfig) as MCPJSONConfiguration;
        const errors = validateMCPConfiguration(config);
        setValidationErrors(errors);
      } catch (error) {
        setValidationErrors(['Invalid JSON format']);
      }
    } else {
      setValidationErrors([]);
    }
  }, [jsonConfig, configMode]);

  const handleInputChange = (field: string, value: any) => {
    if (field.startsWith('server_config.')) {
      const configField = field.replace('server_config.', '');
      setServerData(prev => ({
        ...prev,
        server_config: {
          ...prev.server_config!,
          [configField]: value
        }
      }));
    } else {
      setServerData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleTemplateSelect = (templateKey: string) => {
    const template = mcpServerTemplates[templateKey];
    if (template) {
      setJsonConfig(JSON.stringify(template, null, 2));
      setConfigMode('json');
      setServerData(prev => ({
        ...prev,
        config_type: 'json',
        name: prev.name || `${templateKey.charAt(0).toUpperCase() + templateKey.slice(1)} MCP Server`
      }));
    }
  };

  const handleImportConfig = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const content = e.target?.result as string;
          const config = JSON.parse(content);
          setJsonConfig(JSON.stringify(config, null, 2));
          setConfigMode('json');
          setServerData(prev => ({
            ...prev,
            config_type: 'json'
          }));
          toast({
            title: "Configuration Imported",
            description: "MCP server configuration imported successfully."
          });
        } catch (error) {
          toast({
            title: "Import Failed",
            description: "Invalid JSON configuration file.",
            variant: "destructive"
          });
        }
      };
      reader.readAsText(file);
    }
  };

  const handleExportConfig = () => {
    try {
      const config = configMode === 'json' 
        ? JSON.parse(jsonConfig)
        : { servers: { [serverData.name]: serverData.server_config } };
      
      const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${serverData.name || 'mcp-server'}-config.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast({
        title: "Configuration Exported",
        description: "MCP server configuration exported successfully."
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export configuration.",
        variant: "destructive"
      });
    }
  };

  const handleCopyConfig = async () => {
    try {
      const config = configMode === 'json' 
        ? jsonConfig
        : JSON.stringify({ servers: { [serverData.name]: serverData.server_config } }, null, 2);
      
      await navigator.clipboard.writeText(config);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      
      toast({
        title: "Configuration Copied",
        description: "Configuration copied to clipboard."
      });
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy configuration.",
        variant: "destructive"
      });
    }
  };

  const handleSubmit = async () => {
    if (validationErrors.length > 0) {
      toast({
        title: "Validation Error",
        description: "Please fix the configuration errors before submitting.",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      const submitData: MCPServerCreate = {
        ...serverData,
        config_type: configMode === 'json' ? 'json' : 'form'
      };

      if (configMode === 'json') {
        submitData.json_config = JSON.parse(jsonConfig);
        submitData.server_config = undefined;
      } else {
        submitData.json_config = undefined;
      }

      const result = await mcpServerApi.createServer(submitData);
      
      toast({
        title: "MCP Server Created",
        description: `Server "${result.name}" created successfully.`
      });
      
      onServerCreated?.(result);
    } catch (error: any) {
      toast({
        title: "Creation Failed",
        description: error.message || "Failed to create MCP server.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">
            {mode === 'create' ? 'Add MCP Server' : 'Edit MCP Server'}
          </h2>
          <p className="text-muted-foreground">
            Configure a Model Context Protocol server to extend AI capabilities
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleExportConfig}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" onClick={handleCopyConfig}>
            {copied ? <Check className="w-4 h-4 mr-2" /> : <Copy className="w-4 h-4 mr-2" />}
            {copied ? 'Copied!' : 'Copy'}
          </Button>
          <label>
            <Button variant="outline" asChild>
              <span>
                <Upload className="w-4 h-4 mr-2" />
                Import
              </span>
            </Button>
            <input
              type="file"
              accept=".json"
              onChange={handleImportConfig}
              className="hidden"
            />
          </label>
        </div>
      </div>

      {/* Configuration Mode Tabs */}
      <Tabs value={configMode} onValueChange={(value) => setConfigMode(value as 'simple' | 'json')}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="simple" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Simple Configuration
          </TabsTrigger>
          <TabsTrigger value="json" className="flex items-center gap-2">
            <Code className="w-4 h-4" />
            JSON Configuration
          </TabsTrigger>
        </TabsList>

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Server Name</Label>
                <Input
                  id="name"
                  value={serverData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="My MCP Server"
                />
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  value={serverData.description || ''}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Server description"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Simple Configuration */}
        <TabsContent value="simple">
          <Card>
            <CardHeader>
              <CardTitle>Server Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="transport">Transport Type</Label>
                <Select
                  value={serverData.server_config?.transport_type}
                  onValueChange={(value) => handleInputChange('server_config.transport_type', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select transport type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="http">HTTP</SelectItem>
                    <SelectItem value="stdio">Standard I/O</SelectItem>
                    <SelectItem value="sse">Server-Sent Events</SelectItem>
                    <SelectItem value="ws">WebSocket</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {(serverData.server_config?.transport_type === 'http' || 
                serverData.server_config?.transport_type === 'sse' || 
                serverData.server_config?.transport_type === 'ws') && (
                <div>
                  <Label htmlFor="url">Server URL</Label>
                  <Input
                    id="url"
                    value={serverData.server_config?.url || ''}
                    onChange={(e) => handleInputChange('server_config.url', e.target.value)}
                    placeholder="https://api.example.com/mcp"
                  />
                </div>
              )}

              {serverData.server_config?.transport_type === 'stdio' && (
                <>
                  <div>
                    <Label htmlFor="command">Command</Label>
                    <Input
                      id="command"
                      value={serverData.server_config?.command || ''}
                      onChange={(e) => handleInputChange('server_config.command', e.target.value)}
                      placeholder="npx"
                    />
                  </div>
                  <div>
                    <Label htmlFor="args">Arguments (one per line)</Label>
                    <Textarea
                      id="args"
                      value={serverData.server_config?.args?.join('\n') || ''}
                      onChange={(e) => handleInputChange('server_config.args', e.target.value.split('\n').filter(Boolean))}
                      placeholder="-y\n@modelcontextprotocol/server-filesystem\n/workspace"
                      rows={3}
                    />
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* JSON Configuration */}
        <TabsContent value="json">
          <div className="space-y-4">
            {/* Template Gallery */}
            <Card>
              <CardHeader>
                <CardTitle>Configuration Templates</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {Object.keys(mcpServerTemplates).map((templateKey) => (
                    <Button
                      key={templateKey}
                      variant="outline"
                      size="sm"
                      onClick={() => handleTemplateSelect(templateKey)}
                      className="justify-start"
                    >
                      <Zap className="w-4 h-4 mr-2" />
                      {templateKey.charAt(0).toUpperCase() + templateKey.slice(1)}
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* JSON Editor */}
            <Card>
              <CardHeader>
                <CardTitle>JSON Configuration</CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={jsonConfig}
                  onChange={(e) => setJsonConfig(e.target.value)}
                  placeholder={JSON.stringify({
                    "servers": {
                      "example": {
                        "url": "https://api.example.com/mcp"
                      }
                    }
                  }, null, 2)}
                  rows={15}
                  className="font-mono text-sm"
                />
                
                {/* Validation Errors */}
                {validationErrors.length > 0 && (
                  <Alert className="mt-4" variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      <div className="space-y-1">
                        {validationErrors.map((error, index) => (
                          <div key={index}>• {error}</div>
                        ))}
                      </div>
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Actions */}
      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit} 
          disabled={isLoading || validationErrors.length > 0}
        >
          {isLoading ? 'Creating...' : mode === 'create' ? 'Create Server' : 'Update Server'}
        </Button>
      </div>
    </div>
  );
};
