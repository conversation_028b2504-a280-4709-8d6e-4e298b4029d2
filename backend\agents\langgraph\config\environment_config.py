"""
Environment Configuration for LangGraph Integration.

This module provides environment-specific configuration management
for the LangGraph integration system.
"""

import os
import logging
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field, field_validator
from enum import Enum

logger = logging.getLogger(__name__)


class Environment(str, Enum):
    """Supported environment types."""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"


class EnvironmentConfig(BaseModel):
    """
    Environment-specific configuration for LangGraph integration.
    
    This class manages environment-specific settings including
    feature flags, performance tuning, and deployment configurations.
    """
    
    # Environment identification
    environment: Environment = Field(
        default=Environment.DEVELOPMENT,
        description="Current environment"
    )
    
    debug: bool = Field(
        default=False,
        description="Enable debug mode"
    )
    
    testing: bool = Field(
        default=False,
        description="Enable testing mode"
    )
    
    # Performance settings
    enable_performance_monitoring: bool = Field(
        default=True,
        description="Enable performance monitoring"
    )
    
    enable_detailed_logging: bool = Field(
        default=False,
        description="Enable detailed logging"
    )
    
    # Feature flags
    enable_experimental_features: bool = Field(
        default=False,
        description="Enable experimental features"
    )
    
    enable_legacy_fallback: bool = Field(
        default=True,
        description="Enable legacy system fallback"
    )
    
    # Resource limits
    max_concurrent_workflows: int = Field(
        default=10,
        description="Maximum concurrent workflows"
    )
    
    max_memory_usage_mb: int = Field(
        default=1024,
        description="Maximum memory usage in MB"
    )
    
    # Timeout settings
    default_timeout_seconds: int = Field(
        default=300,
        description="Default timeout in seconds"
    )
    
    workflow_timeout_seconds: int = Field(
        default=3600,
        description="Workflow timeout in seconds"
    )
    
    @field_validator('environment')
    @classmethod
    def validate_environment(cls, v):
        """Validate environment value."""
        if isinstance(v, str):
            try:
                return Environment(v.lower())
            except ValueError:
                raise ValueError(f"Invalid environment: {v}")
        return v
    
    @field_validator('max_concurrent_workflows')
    @classmethod
    def validate_max_concurrent_workflows(cls, v):
        """Validate max concurrent workflows."""
        if v <= 0:
            raise ValueError("max_concurrent_workflows must be positive")
        return v
    
    @field_validator('max_memory_usage_mb')
    @classmethod
    def validate_max_memory_usage_mb(cls, v):
        """Validate max memory usage."""
        if v <= 0:
            raise ValueError("max_memory_usage_mb must be positive")
        return v
    
    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment == Environment.DEVELOPMENT
    
    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment == Environment.PRODUCTION
    
    @property
    def is_testing(self) -> bool:
        """Check if running in testing environment."""
        return self.environment == Environment.TESTING or self.testing
    
    @classmethod
    def from_env(cls) -> "EnvironmentConfig":
        """Create configuration from environment variables."""
        return cls(
            environment=os.getenv("ENVIRONMENT", "development"),
            debug=os.getenv("DEBUG", "false").lower() == "true",
            testing=os.getenv("TESTING", "false").lower() == "true",
            enable_performance_monitoring=os.getenv("ENABLE_PERFORMANCE_MONITORING", "true").lower() == "true",
            enable_detailed_logging=os.getenv("ENABLE_DETAILED_LOGGING", "false").lower() == "true",
            enable_experimental_features=os.getenv("ENABLE_EXPERIMENTAL_FEATURES", "false").lower() == "true",
            enable_legacy_fallback=os.getenv("ENABLE_LEGACY_FALLBACK", "true").lower() == "true",
            max_concurrent_workflows=int(os.getenv("MAX_CONCURRENT_WORKFLOWS", "10")),
            max_memory_usage_mb=int(os.getenv("MAX_MEMORY_USAGE_MB", "1024")),
            default_timeout_seconds=int(os.getenv("DEFAULT_TIMEOUT_SECONDS", "300")),
            workflow_timeout_seconds=int(os.getenv("WORKFLOW_TIMEOUT_SECONDS", "3600"))
        )
    
    def get_environment_specific_config(self) -> Dict[str, Any]:
        """Get environment-specific configuration dictionary."""
        config = {
            "environment": self.environment.value,
            "debug": self.debug,
            "testing": self.testing,
            "performance_monitoring": self.enable_performance_monitoring,
            "detailed_logging": self.enable_detailed_logging,
            "experimental_features": self.enable_experimental_features,
            "legacy_fallback": self.enable_legacy_fallback,
            "resource_limits": {
                "max_concurrent_workflows": self.max_concurrent_workflows,
                "max_memory_usage_mb": self.max_memory_usage_mb
            },
            "timeouts": {
                "default_timeout_seconds": self.default_timeout_seconds,
                "workflow_timeout_seconds": self.workflow_timeout_seconds
            }
        }
        
        return config
    
    def update_from_dict(self, config_dict: Dict[str, Any]) -> None:
        """Update configuration from dictionary."""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                logger.warning(f"Unknown configuration key: {key}")


# Global environment configuration instance
_environment_config: Optional[EnvironmentConfig] = None


def get_environment_config() -> EnvironmentConfig:
    """Get the global environment configuration instance."""
    global _environment_config
    if _environment_config is None:
        _environment_config = EnvironmentConfig.from_env()
    return _environment_config


def set_environment_config(config: EnvironmentConfig) -> None:
    """Set the global environment configuration instance."""
    global _environment_config
    _environment_config = config


def reset_environment_config() -> None:
    """Reset the global environment configuration instance."""
    global _environment_config
    _environment_config = None
