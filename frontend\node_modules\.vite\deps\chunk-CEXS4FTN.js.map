{"version": 3, "sources": ["../../d3-dispatch/src/dispatch.js", "../../d3-selection/src/namespaces.js", "../../d3-selection/src/namespace.js", "../../d3-selection/src/creator.js", "../../d3-selection/src/selector.js", "../../d3-selection/src/selectorAll.js", "../../d3-selection/src/matcher.js", "../../d3-selection/src/window.js", "../../d3-selection/src/selection/style.js", "../../d3-selection/src/selection/select.js", "../../d3-selection/src/array.js", "../../d3-selection/src/selection/selectAll.js", "../../d3-selection/src/selection/selectChild.js", "../../d3-selection/src/selection/selectChildren.js", "../../d3-selection/src/selection/filter.js", "../../d3-selection/src/selection/sparse.js", "../../d3-selection/src/selection/enter.js", "../../d3-selection/src/constant.js", "../../d3-selection/src/selection/data.js", "../../d3-selection/src/selection/exit.js", "../../d3-selection/src/selection/join.js", "../../d3-selection/src/selection/merge.js", "../../d3-selection/src/selection/order.js", "../../d3-selection/src/selection/sort.js", "../../d3-selection/src/selection/call.js", "../../d3-selection/src/selection/nodes.js", "../../d3-selection/src/selection/node.js", "../../d3-selection/src/selection/size.js", "../../d3-selection/src/selection/empty.js", "../../d3-selection/src/selection/each.js", "../../d3-selection/src/selection/attr.js", "../../d3-selection/src/selection/property.js", "../../d3-selection/src/selection/classed.js", "../../d3-selection/src/selection/text.js", "../../d3-selection/src/selection/html.js", "../../d3-selection/src/selection/raise.js", "../../d3-selection/src/selection/lower.js", "../../d3-selection/src/selection/append.js", "../../d3-selection/src/selection/insert.js", "../../d3-selection/src/selection/remove.js", "../../d3-selection/src/selection/clone.js", "../../d3-selection/src/selection/datum.js", "../../d3-selection/src/selection/on.js", "../../d3-selection/src/selection/dispatch.js", "../../d3-selection/src/selection/iterator.js", "../../d3-selection/src/selection/index.js", "../../d3-selection/src/select.js", "../../d3-selection/src/create.js", "../../d3-selection/src/local.js", "../../d3-selection/src/sourceEvent.js", "../../d3-selection/src/pointer.js", "../../d3-selection/src/pointers.js", "../../d3-selection/src/selectAll.js", "../../d3-drag/src/noevent.js", "../../d3-drag/src/nodrag.js", "../../d3-drag/src/constant.js", "../../d3-drag/src/event.js", "../../d3-drag/src/drag.js", "../../d3-timer/src/timer.js", "../../d3-timer/src/timeout.js", "../../d3-timer/src/interval.js", "../../d3-transition/src/transition/schedule.js", "../../d3-transition/src/interrupt.js", "../../d3-transition/src/transition/tween.js", "../../d3-transition/src/transition/interpolate.js", "../../d3-transition/src/transition/attr.js", "../../d3-transition/src/transition/attrTween.js", "../../d3-transition/src/transition/delay.js", "../../d3-transition/src/transition/duration.js", "../../d3-transition/src/transition/ease.js", "../../d3-transition/src/transition/easeVarying.js", "../../d3-transition/src/transition/filter.js", "../../d3-transition/src/transition/merge.js", "../../d3-transition/src/transition/on.js", "../../d3-transition/src/transition/remove.js", "../../d3-transition/src/transition/select.js", "../../d3-transition/src/transition/selectAll.js", "../../d3-transition/src/transition/selection.js", "../../d3-transition/src/transition/style.js", "../../d3-transition/src/transition/styleTween.js", "../../d3-transition/src/transition/text.js", "../../d3-transition/src/transition/textTween.js", "../../d3-transition/src/transition/transition.js", "../../d3-transition/src/transition/end.js", "../../d3-transition/src/transition/index.js", "../../d3-ease/src/linear.js", "../../d3-ease/src/quad.js", "../../d3-ease/src/cubic.js", "../../d3-ease/src/poly.js", "../../d3-ease/src/sin.js", "../../d3-ease/src/math.js", "../../d3-ease/src/exp.js", "../../d3-ease/src/circle.js", "../../d3-ease/src/bounce.js", "../../d3-ease/src/back.js", "../../d3-ease/src/elastic.js", "../../d3-transition/src/active.js", "../../d3-transition/src/selection/interrupt.js", "../../d3-transition/src/selection/transition.js", "../../d3-transition/src/selection/index.js", "../../d3-zoom/src/transform.js", "../../d3-zoom/src/constant.js", "../../d3-zoom/src/event.js", "../../d3-zoom/src/noevent.js", "../../d3-zoom/src/zoom.js"], "sourcesContent": ["var noop = {value: () => {}};\n\nfunction dispatch() {\n  for (var i = 0, n = arguments.length, _ = {}, t; i < n; ++i) {\n    if (!(t = arguments[i] + \"\") || (t in _) || /[\\s.]/.test(t)) throw new Error(\"illegal type: \" + t);\n    _[t] = [];\n  }\n  return new Dispatch(_);\n}\n\nfunction Dispatch(_) {\n  this._ = _;\n}\n\nfunction parseTypenames(typenames, types) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n    if (t && !types.hasOwnProperty(t)) throw new Error(\"unknown type: \" + t);\n    return {type: t, name: name};\n  });\n}\n\nDispatch.prototype = dispatch.prototype = {\n  constructor: Dispatch,\n  on: function(typename, callback) {\n    var _ = this._,\n        T = parseTypenames(typename + \"\", _),\n        t,\n        i = -1,\n        n = T.length;\n\n    // If no callback was specified, return the callback of the given type and name.\n    if (arguments.length < 2) {\n      while (++i < n) if ((t = (typename = T[i]).type) && (t = get(_[t], typename.name))) return t;\n      return;\n    }\n\n    // If a type was specified, set the callback for the given type and name.\n    // Otherwise, if a null callback was specified, remove callbacks of the given name.\n    if (callback != null && typeof callback !== \"function\") throw new Error(\"invalid callback: \" + callback);\n    while (++i < n) {\n      if (t = (typename = T[i]).type) _[t] = set(_[t], typename.name, callback);\n      else if (callback == null) for (t in _) _[t] = set(_[t], typename.name, null);\n    }\n\n    return this;\n  },\n  copy: function() {\n    var copy = {}, _ = this._;\n    for (var t in _) copy[t] = _[t].slice();\n    return new Dispatch(copy);\n  },\n  call: function(type, that) {\n    if ((n = arguments.length - 2) > 0) for (var args = new Array(n), i = 0, n, t; i < n; ++i) args[i] = arguments[i + 2];\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  },\n  apply: function(type, that, args) {\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (var t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  }\n};\n\nfunction get(type, name) {\n  for (var i = 0, n = type.length, c; i < n; ++i) {\n    if ((c = type[i]).name === name) {\n      return c.value;\n    }\n  }\n}\n\nfunction set(type, name, callback) {\n  for (var i = 0, n = type.length; i < n; ++i) {\n    if (type[i].name === name) {\n      type[i] = noop, type = type.slice(0, i).concat(type.slice(i + 1));\n      break;\n    }\n  }\n  if (callback != null) type.push({name: name, value: callback});\n  return type;\n}\n\nexport default dispatch;\n", "export var xhtml = \"http://www.w3.org/1999/xhtml\";\n\nexport default {\n  svg: \"http://www.w3.org/2000/svg\",\n  xhtml: xhtml,\n  xlink: \"http://www.w3.org/1999/xlink\",\n  xml: \"http://www.w3.org/XML/1998/namespace\",\n  xmlns: \"http://www.w3.org/2000/xmlns/\"\n};\n", "import namespaces from \"./namespaces.js\";\n\nexport default function(name) {\n  var prefix = name += \"\", i = prefix.indexOf(\":\");\n  if (i >= 0 && (prefix = name.slice(0, i)) !== \"xmlns\") name = name.slice(i + 1);\n  return namespaces.hasOwnProperty(prefix) ? {space: namespaces[prefix], local: name} : name; // eslint-disable-line no-prototype-builtins\n}\n", "import namespace from \"./namespace.js\";\nimport {xhtml} from \"./namespaces.js\";\n\nfunction creatorInherit(name) {\n  return function() {\n    var document = this.ownerDocument,\n        uri = this.namespaceURI;\n    return uri === xhtml && document.documentElement.namespaceURI === xhtml\n        ? document.createElement(name)\n        : document.createElementNS(uri, name);\n  };\n}\n\nfunction creatorFixed(fullname) {\n  return function() {\n    return this.ownerDocument.createElementNS(fullname.space, fullname.local);\n  };\n}\n\nexport default function(name) {\n  var fullname = namespace(name);\n  return (fullname.local\n      ? creatorFixed\n      : creatorInherit)(fullname);\n}\n", "function none() {}\n\nexport default function(selector) {\n  return selector == null ? none : function() {\n    return this.querySelector(selector);\n  };\n}\n", "function empty() {\n  return [];\n}\n\nexport default function(selector) {\n  return selector == null ? empty : function() {\n    return this.querySelectorAll(selector);\n  };\n}\n", "export default function(selector) {\n  return function() {\n    return this.matches(selector);\n  };\n}\n\nexport function childMatcher(selector) {\n  return function(node) {\n    return node.matches(selector);\n  };\n}\n\n", "export default function(node) {\n  return (node.ownerDocument && node.ownerDocument.defaultView) // node is a Node\n      || (node.document && node) // node is a Window\n      || node.defaultView; // node is a Document\n}\n", "import defaultView from \"../window.js\";\n\nfunction styleRemove(name) {\n  return function() {\n    this.style.removeProperty(name);\n  };\n}\n\nfunction styleConstant(name, value, priority) {\n  return function() {\n    this.style.setProperty(name, value, priority);\n  };\n}\n\nfunction styleFunction(name, value, priority) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.style.removeProperty(name);\n    else this.style.setProperty(name, v, priority);\n  };\n}\n\nexport default function(name, value, priority) {\n  return arguments.length > 1\n      ? this.each((value == null\n            ? styleRemove : typeof value === \"function\"\n            ? styleFunction\n            : styleConstant)(name, value, priority == null ? \"\" : priority))\n      : styleValue(this.node(), name);\n}\n\nexport function styleValue(node, name) {\n  return node.style.getPropertyValue(name)\n      || defaultView(node).getComputedStyle(node, null).getPropertyValue(name);\n}\n", "import {Selection} from \"./index.js\";\nimport selector from \"../selector.js\";\n\nexport default function(select) {\n  if (typeof select !== \"function\") select = selector(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = new Array(n), node, subnode, i = 0; i < n; ++i) {\n      if ((node = group[i]) && (subnode = select.call(node, node.__data__, i, group))) {\n        if (\"__data__\" in node) subnode.__data__ = node.__data__;\n        subgroup[i] = subnode;\n      }\n    }\n  }\n\n  return new Selection(subgroups, this._parents);\n}\n", "// Given something array like (or null), returns something that is strictly an\n// array. This is used to ensure that array-like objects passed to d3.selectAll\n// or selection.selectAll are converted into proper arrays when creating a\n// selection; we don’t ever want to create a selection backed by a live\n// HTMLCollection or NodeList. However, note that selection.selectAll will use a\n// static NodeList as a group, since it safely derived from querySelectorAll.\nexport default function array(x) {\n  return x == null ? [] : Array.isArray(x) ? x : Array.from(x);\n}\n", "import {Selection} from \"./index.js\";\nimport array from \"../array.js\";\nimport selectorAll from \"../selectorAll.js\";\n\nfunction arrayAll(select) {\n  return function() {\n    return array(select.apply(this, arguments));\n  };\n}\n\nexport default function(select) {\n  if (typeof select === \"function\") select = arrayAll(select);\n  else select = selectorAll(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = [], parents = [], j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        subgroups.push(select.call(node, node.__data__, i, group));\n        parents.push(node);\n      }\n    }\n  }\n\n  return new Selection(subgroups, parents);\n}\n", "import {childMatcher} from \"../matcher.js\";\n\nvar find = Array.prototype.find;\n\nfunction childFind(match) {\n  return function() {\n    return find.call(this.children, match);\n  };\n}\n\nfunction childFirst() {\n  return this.firstElementChild;\n}\n\nexport default function(match) {\n  return this.select(match == null ? childFirst\n      : childFind(typeof match === \"function\" ? match : childMatcher(match)));\n}\n", "import {childMatcher} from \"../matcher.js\";\n\nvar filter = Array.prototype.filter;\n\nfunction children() {\n  return Array.from(this.children);\n}\n\nfunction childrenFilter(match) {\n  return function() {\n    return filter.call(this.children, match);\n  };\n}\n\nexport default function(match) {\n  return this.selectAll(match == null ? children\n      : childrenFilter(typeof match === \"function\" ? match : childMatcher(match)));\n}\n", "import {Selection} from \"./index.js\";\nimport matcher from \"../matcher.js\";\n\nexport default function(match) {\n  if (typeof match !== \"function\") match = matcher(match);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = [], node, i = 0; i < n; ++i) {\n      if ((node = group[i]) && match.call(node, node.__data__, i, group)) {\n        subgroup.push(node);\n      }\n    }\n  }\n\n  return new Selection(subgroups, this._parents);\n}\n", "export default function(update) {\n  return new Array(update.length);\n}\n", "import sparse from \"./sparse.js\";\nimport {Selection} from \"./index.js\";\n\nexport default function() {\n  return new Selection(this._enter || this._groups.map(sparse), this._parents);\n}\n\nexport function EnterNode(parent, datum) {\n  this.ownerDocument = parent.ownerDocument;\n  this.namespaceURI = parent.namespaceURI;\n  this._next = null;\n  this._parent = parent;\n  this.__data__ = datum;\n}\n\nEnterNode.prototype = {\n  constructor: EnterNode,\n  appendChild: function(child) { return this._parent.insertBefore(child, this._next); },\n  insertBefore: function(child, next) { return this._parent.insertBefore(child, next); },\n  querySelector: function(selector) { return this._parent.querySelector(selector); },\n  querySelectorAll: function(selector) { return this._parent.querySelectorAll(selector); }\n};\n", "export default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "import {Selection} from \"./index.js\";\nimport {EnterNode} from \"./enter.js\";\nimport constant from \"../constant.js\";\n\nfunction bindIndex(parent, group, enter, update, exit, data) {\n  var i = 0,\n      node,\n      groupLength = group.length,\n      dataLength = data.length;\n\n  // Put any non-null nodes that fit into update.\n  // Put any null nodes into enter.\n  // Put any remaining data into enter.\n  for (; i < dataLength; ++i) {\n    if (node = group[i]) {\n      node.__data__ = data[i];\n      update[i] = node;\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n\n  // Put any non-null nodes that don’t fit into exit.\n  for (; i < groupLength; ++i) {\n    if (node = group[i]) {\n      exit[i] = node;\n    }\n  }\n}\n\nfunction bindKey(parent, group, enter, update, exit, data, key) {\n  var i,\n      node,\n      nodeByKeyValue = new Map,\n      groupLength = group.length,\n      dataLength = data.length,\n      keyValues = new Array(groupLength),\n      keyValue;\n\n  // Compute the key for each node.\n  // If multiple nodes have the same key, the duplicates are added to exit.\n  for (i = 0; i < groupLength; ++i) {\n    if (node = group[i]) {\n      keyValues[i] = keyValue = key.call(node, node.__data__, i, group) + \"\";\n      if (nodeByKeyValue.has(keyValue)) {\n        exit[i] = node;\n      } else {\n        nodeByKeyValue.set(keyValue, node);\n      }\n    }\n  }\n\n  // Compute the key for each datum.\n  // If there a node associated with this key, join and add it to update.\n  // If there is not (or the key is a duplicate), add it to enter.\n  for (i = 0; i < dataLength; ++i) {\n    keyValue = key.call(parent, data[i], i, data) + \"\";\n    if (node = nodeByKeyValue.get(keyValue)) {\n      update[i] = node;\n      node.__data__ = data[i];\n      nodeByKeyValue.delete(keyValue);\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n\n  // Add any remaining nodes that were not bound to data to exit.\n  for (i = 0; i < groupLength; ++i) {\n    if ((node = group[i]) && (nodeByKeyValue.get(keyValues[i]) === node)) {\n      exit[i] = node;\n    }\n  }\n}\n\nfunction datum(node) {\n  return node.__data__;\n}\n\nexport default function(value, key) {\n  if (!arguments.length) return Array.from(this, datum);\n\n  var bind = key ? bindKey : bindIndex,\n      parents = this._parents,\n      groups = this._groups;\n\n  if (typeof value !== \"function\") value = constant(value);\n\n  for (var m = groups.length, update = new Array(m), enter = new Array(m), exit = new Array(m), j = 0; j < m; ++j) {\n    var parent = parents[j],\n        group = groups[j],\n        groupLength = group.length,\n        data = arraylike(value.call(parent, parent && parent.__data__, j, parents)),\n        dataLength = data.length,\n        enterGroup = enter[j] = new Array(dataLength),\n        updateGroup = update[j] = new Array(dataLength),\n        exitGroup = exit[j] = new Array(groupLength);\n\n    bind(parent, group, enterGroup, updateGroup, exitGroup, data, key);\n\n    // Now connect the enter nodes to their following update node, such that\n    // appendChild can insert the materialized enter node before this node,\n    // rather than at the end of the parent node.\n    for (var i0 = 0, i1 = 0, previous, next; i0 < dataLength; ++i0) {\n      if (previous = enterGroup[i0]) {\n        if (i0 >= i1) i1 = i0 + 1;\n        while (!(next = updateGroup[i1]) && ++i1 < dataLength);\n        previous._next = next || null;\n      }\n    }\n  }\n\n  update = new Selection(update, parents);\n  update._enter = enter;\n  update._exit = exit;\n  return update;\n}\n\n// Given some data, this returns an array-like view of it: an object that\n// exposes a length property and allows numeric indexing. Note that unlike\n// selectAll, this isn’t worried about “live” collections because the resulting\n// array will only be used briefly while data is being bound. (It is possible to\n// cause the data to change while iterating by using a key function, but please\n// don’t; we’d rather avoid a gratuitous copy.)\nfunction arraylike(data) {\n  return typeof data === \"object\" && \"length\" in data\n    ? data // Array, TypedArray, NodeList, array-like\n    : Array.from(data); // Map, Set, iterable, string, or anything else\n}\n", "import sparse from \"./sparse.js\";\nimport {Selection} from \"./index.js\";\n\nexport default function() {\n  return new Selection(this._exit || this._groups.map(sparse), this._parents);\n}\n", "export default function(onenter, onupdate, onexit) {\n  var enter = this.enter(), update = this, exit = this.exit();\n  if (typeof onenter === \"function\") {\n    enter = onenter(enter);\n    if (enter) enter = enter.selection();\n  } else {\n    enter = enter.append(onenter + \"\");\n  }\n  if (onupdate != null) {\n    update = onupdate(update);\n    if (update) update = update.selection();\n  }\n  if (onexit == null) exit.remove(); else onexit(exit);\n  return enter && update ? enter.merge(update).order() : update;\n}\n", "import {Selection} from \"./index.js\";\n\nexport default function(context) {\n  var selection = context.selection ? context.selection() : context;\n\n  for (var groups0 = this._groups, groups1 = selection._groups, m0 = groups0.length, m1 = groups1.length, m = Math.min(m0, m1), merges = new Array(m0), j = 0; j < m; ++j) {\n    for (var group0 = groups0[j], group1 = groups1[j], n = group0.length, merge = merges[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group0[i] || group1[i]) {\n        merge[i] = node;\n      }\n    }\n  }\n\n  for (; j < m0; ++j) {\n    merges[j] = groups0[j];\n  }\n\n  return new Selection(merges, this._parents);\n}\n", "export default function() {\n\n  for (var groups = this._groups, j = -1, m = groups.length; ++j < m;) {\n    for (var group = groups[j], i = group.length - 1, next = group[i], node; --i >= 0;) {\n      if (node = group[i]) {\n        if (next && node.compareDocumentPosition(next) ^ 4) next.parentNode.insertBefore(node, next);\n        next = node;\n      }\n    }\n  }\n\n  return this;\n}\n", "import {Selection} from \"./index.js\";\n\nexport default function(compare) {\n  if (!compare) compare = ascending;\n\n  function compareNode(a, b) {\n    return a && b ? compare(a.__data__, b.__data__) : !a - !b;\n  }\n\n  for (var groups = this._groups, m = groups.length, sortgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, sortgroup = sortgroups[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        sortgroup[i] = node;\n      }\n    }\n    sortgroup.sort(compareNode);\n  }\n\n  return new Selection(sortgroups, this._parents).order();\n}\n\nfunction ascending(a, b) {\n  return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n", "export default function() {\n  var callback = arguments[0];\n  arguments[0] = this;\n  callback.apply(null, arguments);\n  return this;\n}\n", "export default function() {\n  return Array.from(this);\n}\n", "export default function() {\n\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length; i < n; ++i) {\n      var node = group[i];\n      if (node) return node;\n    }\n  }\n\n  return null;\n}\n", "export default function() {\n  let size = 0;\n  for (const node of this) ++size; // eslint-disable-line no-unused-vars\n  return size;\n}\n", "export default function() {\n  return !this.node();\n}\n", "export default function(callback) {\n\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {\n      if (node = group[i]) callback.call(node, node.__data__, i, group);\n    }\n  }\n\n  return this;\n}\n", "import namespace from \"../namespace.js\";\n\nfunction attrRemove(name) {\n  return function() {\n    this.removeAttribute(name);\n  };\n}\n\nfunction attrRemoveNS(fullname) {\n  return function() {\n    this.removeAttributeNS(fullname.space, fullname.local);\n  };\n}\n\nfunction attrConstant(name, value) {\n  return function() {\n    this.setAttribute(name, value);\n  };\n}\n\nfunction attrConstantNS(fullname, value) {\n  return function() {\n    this.setAttributeNS(fullname.space, fullname.local, value);\n  };\n}\n\nfunction attrFunction(name, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.removeAttribute(name);\n    else this.setAttribute(name, v);\n  };\n}\n\nfunction attrFunctionNS(fullname, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.removeAttributeNS(fullname.space, fullname.local);\n    else this.setAttributeNS(fullname.space, fullname.local, v);\n  };\n}\n\nexport default function(name, value) {\n  var fullname = namespace(name);\n\n  if (arguments.length < 2) {\n    var node = this.node();\n    return fullname.local\n        ? node.getAttributeNS(fullname.space, fullname.local)\n        : node.getAttribute(fullname);\n  }\n\n  return this.each((value == null\n      ? (fullname.local ? attrRemoveNS : attrRemove) : (typeof value === \"function\"\n      ? (fullname.local ? attrFunctionNS : attrFunction)\n      : (fullname.local ? attrConstantNS : attrConstant)))(fullname, value));\n}\n", "function propertyRemove(name) {\n  return function() {\n    delete this[name];\n  };\n}\n\nfunction propertyConstant(name, value) {\n  return function() {\n    this[name] = value;\n  };\n}\n\nfunction propertyFunction(name, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) delete this[name];\n    else this[name] = v;\n  };\n}\n\nexport default function(name, value) {\n  return arguments.length > 1\n      ? this.each((value == null\n          ? propertyRemove : typeof value === \"function\"\n          ? propertyFunction\n          : propertyConstant)(name, value))\n      : this.node()[name];\n}\n", "function classArray(string) {\n  return string.trim().split(/^|\\s+/);\n}\n\nfunction classList(node) {\n  return node.classList || new ClassList(node);\n}\n\nfunction ClassList(node) {\n  this._node = node;\n  this._names = classArray(node.getAttribute(\"class\") || \"\");\n}\n\nClassList.prototype = {\n  add: function(name) {\n    var i = this._names.indexOf(name);\n    if (i < 0) {\n      this._names.push(name);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  remove: function(name) {\n    var i = this._names.indexOf(name);\n    if (i >= 0) {\n      this._names.splice(i, 1);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  contains: function(name) {\n    return this._names.indexOf(name) >= 0;\n  }\n};\n\nfunction classedAdd(node, names) {\n  var list = classList(node), i = -1, n = names.length;\n  while (++i < n) list.add(names[i]);\n}\n\nfunction classedRemove(node, names) {\n  var list = classList(node), i = -1, n = names.length;\n  while (++i < n) list.remove(names[i]);\n}\n\nfunction classedTrue(names) {\n  return function() {\n    classedAdd(this, names);\n  };\n}\n\nfunction classedFalse(names) {\n  return function() {\n    classedRemove(this, names);\n  };\n}\n\nfunction classedFunction(names, value) {\n  return function() {\n    (value.apply(this, arguments) ? classedAdd : classedRemove)(this, names);\n  };\n}\n\nexport default function(name, value) {\n  var names = classArray(name + \"\");\n\n  if (arguments.length < 2) {\n    var list = classList(this.node()), i = -1, n = names.length;\n    while (++i < n) if (!list.contains(names[i])) return false;\n    return true;\n  }\n\n  return this.each((typeof value === \"function\"\n      ? classedFunction : value\n      ? classedTrue\n      : classedFalse)(names, value));\n}\n", "function textRemove() {\n  this.textContent = \"\";\n}\n\nfunction textConstant(value) {\n  return function() {\n    this.textContent = value;\n  };\n}\n\nfunction textFunction(value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    this.textContent = v == null ? \"\" : v;\n  };\n}\n\nexport default function(value) {\n  return arguments.length\n      ? this.each(value == null\n          ? textRemove : (typeof value === \"function\"\n          ? textFunction\n          : textConstant)(value))\n      : this.node().textContent;\n}\n", "function htmlRemove() {\n  this.innerHTML = \"\";\n}\n\nfunction htmlConstant(value) {\n  return function() {\n    this.innerHTML = value;\n  };\n}\n\nfunction htmlFunction(value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    this.innerHTML = v == null ? \"\" : v;\n  };\n}\n\nexport default function(value) {\n  return arguments.length\n      ? this.each(value == null\n          ? htmlRemove : (typeof value === \"function\"\n          ? htmlFunction\n          : htmlConstant)(value))\n      : this.node().innerHTML;\n}\n", "function raise() {\n  if (this.nextSibling) this.parentNode.appendChild(this);\n}\n\nexport default function() {\n  return this.each(raise);\n}\n", "function lower() {\n  if (this.previousSibling) this.parentNode.insertBefore(this, this.parentNode.firstChild);\n}\n\nexport default function() {\n  return this.each(lower);\n}\n", "import creator from \"../creator.js\";\n\nexport default function(name) {\n  var create = typeof name === \"function\" ? name : creator(name);\n  return this.select(function() {\n    return this.appendChild(create.apply(this, arguments));\n  });\n}\n", "import creator from \"../creator.js\";\nimport selector from \"../selector.js\";\n\nfunction constantNull() {\n  return null;\n}\n\nexport default function(name, before) {\n  var create = typeof name === \"function\" ? name : creator(name),\n      select = before == null ? constantNull : typeof before === \"function\" ? before : selector(before);\n  return this.select(function() {\n    return this.insertBefore(create.apply(this, arguments), select.apply(this, arguments) || null);\n  });\n}\n", "function remove() {\n  var parent = this.parentNode;\n  if (parent) parent.removeChild(this);\n}\n\nexport default function() {\n  return this.each(remove);\n}\n", "function selection_cloneShallow() {\n  var clone = this.cloneNode(false), parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\n\nfunction selection_cloneDeep() {\n  var clone = this.cloneNode(true), parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\n\nexport default function(deep) {\n  return this.select(deep ? selection_cloneDeep : selection_cloneShallow);\n}\n", "export default function(value) {\n  return arguments.length\n      ? this.property(\"__data__\", value)\n      : this.node().__data__;\n}\n", "function contextListener(listener) {\n  return function(event) {\n    listener.call(this, event, this.__data__);\n  };\n}\n\nfunction parseTypenames(typenames) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n    return {type: t, name: name};\n  });\n}\n\nfunction onRemove(typename) {\n  return function() {\n    var on = this.__on;\n    if (!on) return;\n    for (var j = 0, i = -1, m = on.length, o; j < m; ++j) {\n      if (o = on[j], (!typename.type || o.type === typename.type) && o.name === typename.name) {\n        this.removeEventListener(o.type, o.listener, o.options);\n      } else {\n        on[++i] = o;\n      }\n    }\n    if (++i) on.length = i;\n    else delete this.__on;\n  };\n}\n\nfunction onAdd(typename, value, options) {\n  return function() {\n    var on = this.__on, o, listener = contextListener(value);\n    if (on) for (var j = 0, m = on.length; j < m; ++j) {\n      if ((o = on[j]).type === typename.type && o.name === typename.name) {\n        this.removeEventListener(o.type, o.listener, o.options);\n        this.addEventListener(o.type, o.listener = listener, o.options = options);\n        o.value = value;\n        return;\n      }\n    }\n    this.addEventListener(typename.type, listener, options);\n    o = {type: typename.type, name: typename.name, value: value, listener: listener, options: options};\n    if (!on) this.__on = [o];\n    else on.push(o);\n  };\n}\n\nexport default function(typename, value, options) {\n  var typenames = parseTypenames(typename + \"\"), i, n = typenames.length, t;\n\n  if (arguments.length < 2) {\n    var on = this.node().__on;\n    if (on) for (var j = 0, m = on.length, o; j < m; ++j) {\n      for (i = 0, o = on[j]; i < n; ++i) {\n        if ((t = typenames[i]).type === o.type && t.name === o.name) {\n          return o.value;\n        }\n      }\n    }\n    return;\n  }\n\n  on = value ? onAdd : onRemove;\n  for (i = 0; i < n; ++i) this.each(on(typenames[i], value, options));\n  return this;\n}\n", "import defaultView from \"../window.js\";\n\nfunction dispatchEvent(node, type, params) {\n  var window = defaultView(node),\n      event = window.CustomEvent;\n\n  if (typeof event === \"function\") {\n    event = new event(type, params);\n  } else {\n    event = window.document.createEvent(\"Event\");\n    if (params) event.initEvent(type, params.bubbles, params.cancelable), event.detail = params.detail;\n    else event.initEvent(type, false, false);\n  }\n\n  node.dispatchEvent(event);\n}\n\nfunction dispatchConstant(type, params) {\n  return function() {\n    return dispatchEvent(this, type, params);\n  };\n}\n\nfunction dispatchFunction(type, params) {\n  return function() {\n    return dispatchEvent(this, type, params.apply(this, arguments));\n  };\n}\n\nexport default function(type, params) {\n  return this.each((typeof params === \"function\"\n      ? dispatchFunction\n      : dispatchConstant)(type, params));\n}\n", "export default function*() {\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {\n      if (node = group[i]) yield node;\n    }\n  }\n}\n", "import selection_select from \"./select.js\";\nimport selection_selectAll from \"./selectAll.js\";\nimport selection_selectChild from \"./selectChild.js\";\nimport selection_selectChildren from \"./selectChildren.js\";\nimport selection_filter from \"./filter.js\";\nimport selection_data from \"./data.js\";\nimport selection_enter from \"./enter.js\";\nimport selection_exit from \"./exit.js\";\nimport selection_join from \"./join.js\";\nimport selection_merge from \"./merge.js\";\nimport selection_order from \"./order.js\";\nimport selection_sort from \"./sort.js\";\nimport selection_call from \"./call.js\";\nimport selection_nodes from \"./nodes.js\";\nimport selection_node from \"./node.js\";\nimport selection_size from \"./size.js\";\nimport selection_empty from \"./empty.js\";\nimport selection_each from \"./each.js\";\nimport selection_attr from \"./attr.js\";\nimport selection_style from \"./style.js\";\nimport selection_property from \"./property.js\";\nimport selection_classed from \"./classed.js\";\nimport selection_text from \"./text.js\";\nimport selection_html from \"./html.js\";\nimport selection_raise from \"./raise.js\";\nimport selection_lower from \"./lower.js\";\nimport selection_append from \"./append.js\";\nimport selection_insert from \"./insert.js\";\nimport selection_remove from \"./remove.js\";\nimport selection_clone from \"./clone.js\";\nimport selection_datum from \"./datum.js\";\nimport selection_on from \"./on.js\";\nimport selection_dispatch from \"./dispatch.js\";\nimport selection_iterator from \"./iterator.js\";\n\nexport var root = [null];\n\nexport function Selection(groups, parents) {\n  this._groups = groups;\n  this._parents = parents;\n}\n\nfunction selection() {\n  return new Selection([[document.documentElement]], root);\n}\n\nfunction selection_selection() {\n  return this;\n}\n\nSelection.prototype = selection.prototype = {\n  constructor: Selection,\n  select: selection_select,\n  selectAll: selection_selectAll,\n  selectChild: selection_selectChild,\n  selectChildren: selection_selectChildren,\n  filter: selection_filter,\n  data: selection_data,\n  enter: selection_enter,\n  exit: selection_exit,\n  join: selection_join,\n  merge: selection_merge,\n  selection: selection_selection,\n  order: selection_order,\n  sort: selection_sort,\n  call: selection_call,\n  nodes: selection_nodes,\n  node: selection_node,\n  size: selection_size,\n  empty: selection_empty,\n  each: selection_each,\n  attr: selection_attr,\n  style: selection_style,\n  property: selection_property,\n  classed: selection_classed,\n  text: selection_text,\n  html: selection_html,\n  raise: selection_raise,\n  lower: selection_lower,\n  append: selection_append,\n  insert: selection_insert,\n  remove: selection_remove,\n  clone: selection_clone,\n  datum: selection_datum,\n  on: selection_on,\n  dispatch: selection_dispatch,\n  [Symbol.iterator]: selection_iterator\n};\n\nexport default selection;\n", "import {Selection, root} from \"./selection/index.js\";\n\nexport default function(selector) {\n  return typeof selector === \"string\"\n      ? new Selection([[document.querySelector(selector)]], [document.documentElement])\n      : new Selection([[selector]], root);\n}\n", "import creator from \"./creator.js\";\nimport select from \"./select.js\";\n\nexport default function(name) {\n  return select(creator(name).call(document.documentElement));\n}\n", "var nextId = 0;\n\nexport default function local() {\n  return new Local;\n}\n\nfunction Local() {\n  this._ = \"@\" + (++nextId).toString(36);\n}\n\nLocal.prototype = local.prototype = {\n  constructor: Local,\n  get: function(node) {\n    var id = this._;\n    while (!(id in node)) if (!(node = node.parentNode)) return;\n    return node[id];\n  },\n  set: function(node, value) {\n    return node[this._] = value;\n  },\n  remove: function(node) {\n    return this._ in node && delete node[this._];\n  },\n  toString: function() {\n    return this._;\n  }\n};\n", "export default function(event) {\n  let sourceEvent;\n  while (sourceEvent = event.sourceEvent) event = sourceEvent;\n  return event;\n}\n", "import sourceEvent from \"./sourceEvent.js\";\n\nexport default function(event, node) {\n  event = sourceEvent(event);\n  if (node === undefined) node = event.currentTarget;\n  if (node) {\n    var svg = node.ownerSVGElement || node;\n    if (svg.createSVGPoint) {\n      var point = svg.createSVGPoint();\n      point.x = event.clientX, point.y = event.clientY;\n      point = point.matrixTransform(node.getScreenCTM().inverse());\n      return [point.x, point.y];\n    }\n    if (node.getBoundingClientRect) {\n      var rect = node.getBoundingClientRect();\n      return [event.clientX - rect.left - node.clientLeft, event.clientY - rect.top - node.clientTop];\n    }\n  }\n  return [event.pageX, event.pageY];\n}\n", "import pointer from \"./pointer.js\";\nimport sourceEvent from \"./sourceEvent.js\";\n\nexport default function(events, node) {\n  if (events.target) { // i.e., instanceof Event, not TouchList or iterable\n    events = sourceEvent(events);\n    if (node === undefined) node = events.currentTarget;\n    events = events.touches || [events];\n  }\n  return Array.from(events, event => pointer(event, node));\n}\n", "import array from \"./array.js\";\nimport {Selection, root} from \"./selection/index.js\";\n\nexport default function(selector) {\n  return typeof selector === \"string\"\n      ? new Selection([document.querySelectorAll(selector)], [document.documentElement])\n      : new Selection([array(selector)], root);\n}\n", "// These are typically used in conjunction with noevent to ensure that we can\n// preventDefault on the event.\nexport const nonpassive = {passive: false};\nexport const nonpassivecapture = {capture: true, passive: false};\n\nexport function nopropagation(event) {\n  event.stopImmediatePropagation();\n}\n\nexport default function(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\n", "import {select} from \"d3-selection\";\nimport noevent, {nonpassivecapture} from \"./noevent.js\";\n\nexport default function(view) {\n  var root = view.document.documentElement,\n      selection = select(view).on(\"dragstart.drag\", noevent, nonpassivecapture);\n  if (\"onselectstart\" in root) {\n    selection.on(\"selectstart.drag\", noevent, nonpassivecapture);\n  } else {\n    root.__noselect = root.style.MozUserSelect;\n    root.style.MozUserSelect = \"none\";\n  }\n}\n\nexport function yesdrag(view, noclick) {\n  var root = view.document.documentElement,\n      selection = select(view).on(\"dragstart.drag\", null);\n  if (noclick) {\n    selection.on(\"click.drag\", noevent, nonpassivecapture);\n    setTimeout(function() { selection.on(\"click.drag\", null); }, 0);\n  }\n  if (\"onselectstart\" in root) {\n    selection.on(\"selectstart.drag\", null);\n  } else {\n    root.style.MozUserSelect = root.__noselect;\n    delete root.__noselect;\n  }\n}\n", "export default x => () => x;\n", "export default function DragEvent(type, {\n  sourceEvent,\n  subject,\n  target,\n  identifier,\n  active,\n  x, y, dx, dy,\n  dispatch\n}) {\n  Object.defineProperties(this, {\n    type: {value: type, enumerable: true, configurable: true},\n    sourceEvent: {value: sourceEvent, enumerable: true, configurable: true},\n    subject: {value: subject, enumerable: true, configurable: true},\n    target: {value: target, enumerable: true, configurable: true},\n    identifier: {value: identifier, enumerable: true, configurable: true},\n    active: {value: active, enumerable: true, configurable: true},\n    x: {value: x, enumerable: true, configurable: true},\n    y: {value: y, enumerable: true, configurable: true},\n    dx: {value: dx, enumerable: true, configurable: true},\n    dy: {value: dy, enumerable: true, configurable: true},\n    _: {value: dispatch}\n  });\n}\n\nDragEvent.prototype.on = function() {\n  var value = this._.on.apply(this._, arguments);\n  return value === this._ ? this : value;\n};\n", "import {dispatch} from \"d3-dispatch\";\nimport {select, pointer} from \"d3-selection\";\nimport nodrag, {yesdrag} from \"./nodrag.js\";\nimport noevent, {nonpassive, nonpassivecapture, nopropagation} from \"./noevent.js\";\nimport constant from \"./constant.js\";\nimport DragEvent from \"./event.js\";\n\n// Ignore right-click, since that should open the context menu.\nfunction defaultFilter(event) {\n  return !event.ctrlKey && !event.button;\n}\n\nfunction defaultContainer() {\n  return this.parentNode;\n}\n\nfunction defaultSubject(event, d) {\n  return d == null ? {x: event.x, y: event.y} : d;\n}\n\nfunction defaultTouchable() {\n  return navigator.maxTouchPoints || (\"ontouchstart\" in this);\n}\n\nexport default function() {\n  var filter = defaultFilter,\n      container = defaultContainer,\n      subject = defaultSubject,\n      touchable = defaultTouchable,\n      gestures = {},\n      listeners = dispatch(\"start\", \"drag\", \"end\"),\n      active = 0,\n      mousedownx,\n      mousedowny,\n      mousemoving,\n      touchending,\n      clickDistance2 = 0;\n\n  function drag(selection) {\n    selection\n        .on(\"mousedown.drag\", mousedowned)\n      .filter(touchable)\n        .on(\"touchstart.drag\", touchstarted)\n        .on(\"touchmove.drag\", touchmoved, nonpassive)\n        .on(\"touchend.drag touchcancel.drag\", touchended)\n        .style(\"touch-action\", \"none\")\n        .style(\"-webkit-tap-highlight-color\", \"rgba(0,0,0,0)\");\n  }\n\n  function mousedowned(event, d) {\n    if (touchending || !filter.call(this, event, d)) return;\n    var gesture = beforestart(this, container.call(this, event, d), event, d, \"mouse\");\n    if (!gesture) return;\n    select(event.view)\n      .on(\"mousemove.drag\", mousemoved, nonpassivecapture)\n      .on(\"mouseup.drag\", mouseupped, nonpassivecapture);\n    nodrag(event.view);\n    nopropagation(event);\n    mousemoving = false;\n    mousedownx = event.clientX;\n    mousedowny = event.clientY;\n    gesture(\"start\", event);\n  }\n\n  function mousemoved(event) {\n    noevent(event);\n    if (!mousemoving) {\n      var dx = event.clientX - mousedownx, dy = event.clientY - mousedowny;\n      mousemoving = dx * dx + dy * dy > clickDistance2;\n    }\n    gestures.mouse(\"drag\", event);\n  }\n\n  function mouseupped(event) {\n    select(event.view).on(\"mousemove.drag mouseup.drag\", null);\n    yesdrag(event.view, mousemoving);\n    noevent(event);\n    gestures.mouse(\"end\", event);\n  }\n\n  function touchstarted(event, d) {\n    if (!filter.call(this, event, d)) return;\n    var touches = event.changedTouches,\n        c = container.call(this, event, d),\n        n = touches.length, i, gesture;\n\n    for (i = 0; i < n; ++i) {\n      if (gesture = beforestart(this, c, event, d, touches[i].identifier, touches[i])) {\n        nopropagation(event);\n        gesture(\"start\", event, touches[i]);\n      }\n    }\n  }\n\n  function touchmoved(event) {\n    var touches = event.changedTouches,\n        n = touches.length, i, gesture;\n\n    for (i = 0; i < n; ++i) {\n      if (gesture = gestures[touches[i].identifier]) {\n        noevent(event);\n        gesture(\"drag\", event, touches[i]);\n      }\n    }\n  }\n\n  function touchended(event) {\n    var touches = event.changedTouches,\n        n = touches.length, i, gesture;\n\n    if (touchending) clearTimeout(touchending);\n    touchending = setTimeout(function() { touchending = null; }, 500); // Ghost clicks are delayed!\n    for (i = 0; i < n; ++i) {\n      if (gesture = gestures[touches[i].identifier]) {\n        nopropagation(event);\n        gesture(\"end\", event, touches[i]);\n      }\n    }\n  }\n\n  function beforestart(that, container, event, d, identifier, touch) {\n    var dispatch = listeners.copy(),\n        p = pointer(touch || event, container), dx, dy,\n        s;\n\n    if ((s = subject.call(that, new DragEvent(\"beforestart\", {\n        sourceEvent: event,\n        target: drag,\n        identifier,\n        active,\n        x: p[0],\n        y: p[1],\n        dx: 0,\n        dy: 0,\n        dispatch\n      }), d)) == null) return;\n\n    dx = s.x - p[0] || 0;\n    dy = s.y - p[1] || 0;\n\n    return function gesture(type, event, touch) {\n      var p0 = p, n;\n      switch (type) {\n        case \"start\": gestures[identifier] = gesture, n = active++; break;\n        case \"end\": delete gestures[identifier], --active; // falls through\n        case \"drag\": p = pointer(touch || event, container), n = active; break;\n      }\n      dispatch.call(\n        type,\n        that,\n        new DragEvent(type, {\n          sourceEvent: event,\n          subject: s,\n          target: drag,\n          identifier,\n          active: n,\n          x: p[0] + dx,\n          y: p[1] + dy,\n          dx: p[0] - p0[0],\n          dy: p[1] - p0[1],\n          dispatch\n        }),\n        d\n      );\n    };\n  }\n\n  drag.filter = function(_) {\n    return arguments.length ? (filter = typeof _ === \"function\" ? _ : constant(!!_), drag) : filter;\n  };\n\n  drag.container = function(_) {\n    return arguments.length ? (container = typeof _ === \"function\" ? _ : constant(_), drag) : container;\n  };\n\n  drag.subject = function(_) {\n    return arguments.length ? (subject = typeof _ === \"function\" ? _ : constant(_), drag) : subject;\n  };\n\n  drag.touchable = function(_) {\n    return arguments.length ? (touchable = typeof _ === \"function\" ? _ : constant(!!_), drag) : touchable;\n  };\n\n  drag.on = function() {\n    var value = listeners.on.apply(listeners, arguments);\n    return value === listeners ? drag : value;\n  };\n\n  drag.clickDistance = function(_) {\n    return arguments.length ? (clickDistance2 = (_ = +_) * _, drag) : Math.sqrt(clickDistance2);\n  };\n\n  return drag;\n}\n", "var frame = 0, // is an animation frame pending?\n    timeout = 0, // is a timeout pending?\n    interval = 0, // are any timers active?\n    pokeDelay = 1000, // how frequently we check for clock skew\n    taskHead,\n    taskTail,\n    clockLast = 0,\n    clockNow = 0,\n    clockSkew = 0,\n    clock = typeof performance === \"object\" && performance.now ? performance : Date,\n    setFrame = typeof window === \"object\" && window.requestAnimationFrame ? window.requestAnimationFrame.bind(window) : function(f) { setTimeout(f, 17); };\n\nexport function now() {\n  return clockNow || (setFrame(clearNow), clockNow = clock.now() + clockSkew);\n}\n\nfunction clearNow() {\n  clockNow = 0;\n}\n\nexport function Timer() {\n  this._call =\n  this._time =\n  this._next = null;\n}\n\nTimer.prototype = timer.prototype = {\n  constructor: Timer,\n  restart: function(callback, delay, time) {\n    if (typeof callback !== \"function\") throw new TypeError(\"callback is not a function\");\n    time = (time == null ? now() : +time) + (delay == null ? 0 : +delay);\n    if (!this._next && taskTail !== this) {\n      if (taskTail) taskTail._next = this;\n      else taskHead = this;\n      taskTail = this;\n    }\n    this._call = callback;\n    this._time = time;\n    sleep();\n  },\n  stop: function() {\n    if (this._call) {\n      this._call = null;\n      this._time = Infinity;\n      sleep();\n    }\n  }\n};\n\nexport function timer(callback, delay, time) {\n  var t = new Timer;\n  t.restart(callback, delay, time);\n  return t;\n}\n\nexport function timerFlush() {\n  now(); // Get the current time, if not already set.\n  ++frame; // Pretend we’ve set an alarm, if we haven’t already.\n  var t = taskHead, e;\n  while (t) {\n    if ((e = clockNow - t._time) >= 0) t._call.call(undefined, e);\n    t = t._next;\n  }\n  --frame;\n}\n\nfunction wake() {\n  clockNow = (clockLast = clock.now()) + clockSkew;\n  frame = timeout = 0;\n  try {\n    timerFlush();\n  } finally {\n    frame = 0;\n    nap();\n    clockNow = 0;\n  }\n}\n\nfunction poke() {\n  var now = clock.now(), delay = now - clockLast;\n  if (delay > pokeDelay) clockSkew -= delay, clockLast = now;\n}\n\nfunction nap() {\n  var t0, t1 = taskHead, t2, time = Infinity;\n  while (t1) {\n    if (t1._call) {\n      if (time > t1._time) time = t1._time;\n      t0 = t1, t1 = t1._next;\n    } else {\n      t2 = t1._next, t1._next = null;\n      t1 = t0 ? t0._next = t2 : taskHead = t2;\n    }\n  }\n  taskTail = t0;\n  sleep(time);\n}\n\nfunction sleep(time) {\n  if (frame) return; // Soonest alarm already set, or will be.\n  if (timeout) timeout = clearTimeout(timeout);\n  var delay = time - clockNow; // Strictly less than if we recomputed clockNow.\n  if (delay > 24) {\n    if (time < Infinity) timeout = setTimeout(wake, time - clock.now() - clockSkew);\n    if (interval) interval = clearInterval(interval);\n  } else {\n    if (!interval) clockLast = clock.now(), interval = setInterval(poke, pokeDelay);\n    frame = 1, setFrame(wake);\n  }\n}\n", "import {Timer} from \"./timer.js\";\n\nexport default function(callback, delay, time) {\n  var t = new Timer;\n  delay = delay == null ? 0 : +delay;\n  t.restart(elapsed => {\n    t.stop();\n    callback(elapsed + delay);\n  }, delay, time);\n  return t;\n}\n", "import {Timer, now} from \"./timer.js\";\n\nexport default function(callback, delay, time) {\n  var t = new Timer, total = delay;\n  if (delay == null) return t.restart(callback, delay, time), t;\n  t._restart = t.restart;\n  t.restart = function(callback, delay, time) {\n    delay = +delay, time = time == null ? now() : +time;\n    t._restart(function tick(elapsed) {\n      elapsed += total;\n      t._restart(tick, total += delay, time);\n      callback(elapsed);\n    }, delay, time);\n  }\n  t.restart(callback, delay, time);\n  return t;\n}\n", "import {dispatch} from \"d3-dispatch\";\nimport {timer, timeout} from \"d3-timer\";\n\nvar emptyOn = dispatch(\"start\", \"end\", \"cancel\", \"interrupt\");\nvar emptyTween = [];\n\nexport var CREATED = 0;\nexport var SCHEDULED = 1;\nexport var STARTING = 2;\nexport var STARTED = 3;\nexport var RUNNING = 4;\nexport var ENDING = 5;\nexport var ENDED = 6;\n\nexport default function(node, name, id, index, group, timing) {\n  var schedules = node.__transition;\n  if (!schedules) node.__transition = {};\n  else if (id in schedules) return;\n  create(node, id, {\n    name: name,\n    index: index, // For context during callback.\n    group: group, // For context during callback.\n    on: emptyOn,\n    tween: emptyTween,\n    time: timing.time,\n    delay: timing.delay,\n    duration: timing.duration,\n    ease: timing.ease,\n    timer: null,\n    state: CREATED\n  });\n}\n\nexport function init(node, id) {\n  var schedule = get(node, id);\n  if (schedule.state > CREATED) throw new Error(\"too late; already scheduled\");\n  return schedule;\n}\n\nexport function set(node, id) {\n  var schedule = get(node, id);\n  if (schedule.state > STARTED) throw new Error(\"too late; already running\");\n  return schedule;\n}\n\nexport function get(node, id) {\n  var schedule = node.__transition;\n  if (!schedule || !(schedule = schedule[id])) throw new Error(\"transition not found\");\n  return schedule;\n}\n\nfunction create(node, id, self) {\n  var schedules = node.__transition,\n      tween;\n\n  // Initialize the self timer when the transition is created.\n  // Note the actual delay is not known until the first callback!\n  schedules[id] = self;\n  self.timer = timer(schedule, 0, self.time);\n\n  function schedule(elapsed) {\n    self.state = SCHEDULED;\n    self.timer.restart(start, self.delay, self.time);\n\n    // If the elapsed delay is less than our first sleep, start immediately.\n    if (self.delay <= elapsed) start(elapsed - self.delay);\n  }\n\n  function start(elapsed) {\n    var i, j, n, o;\n\n    // If the state is not SCHEDULED, then we previously errored on start.\n    if (self.state !== SCHEDULED) return stop();\n\n    for (i in schedules) {\n      o = schedules[i];\n      if (o.name !== self.name) continue;\n\n      // While this element already has a starting transition during this frame,\n      // defer starting an interrupting transition until that transition has a\n      // chance to tick (and possibly end); see d3/d3-transition#54!\n      if (o.state === STARTED) return timeout(start);\n\n      // Interrupt the active transition, if any.\n      if (o.state === RUNNING) {\n        o.state = ENDED;\n        o.timer.stop();\n        o.on.call(\"interrupt\", node, node.__data__, o.index, o.group);\n        delete schedules[i];\n      }\n\n      // Cancel any pre-empted transitions.\n      else if (+i < id) {\n        o.state = ENDED;\n        o.timer.stop();\n        o.on.call(\"cancel\", node, node.__data__, o.index, o.group);\n        delete schedules[i];\n      }\n    }\n\n    // Defer the first tick to end of the current frame; see d3/d3#1576.\n    // Note the transition may be canceled after start and before the first tick!\n    // Note this must be scheduled before the start event; see d3/d3-transition#16!\n    // Assuming this is successful, subsequent callbacks go straight to tick.\n    timeout(function() {\n      if (self.state === STARTED) {\n        self.state = RUNNING;\n        self.timer.restart(tick, self.delay, self.time);\n        tick(elapsed);\n      }\n    });\n\n    // Dispatch the start event.\n    // Note this must be done before the tween are initialized.\n    self.state = STARTING;\n    self.on.call(\"start\", node, node.__data__, self.index, self.group);\n    if (self.state !== STARTING) return; // interrupted\n    self.state = STARTED;\n\n    // Initialize the tween, deleting null tween.\n    tween = new Array(n = self.tween.length);\n    for (i = 0, j = -1; i < n; ++i) {\n      if (o = self.tween[i].value.call(node, node.__data__, self.index, self.group)) {\n        tween[++j] = o;\n      }\n    }\n    tween.length = j + 1;\n  }\n\n  function tick(elapsed) {\n    var t = elapsed < self.duration ? self.ease.call(null, elapsed / self.duration) : (self.timer.restart(stop), self.state = ENDING, 1),\n        i = -1,\n        n = tween.length;\n\n    while (++i < n) {\n      tween[i].call(node, t);\n    }\n\n    // Dispatch the end event.\n    if (self.state === ENDING) {\n      self.on.call(\"end\", node, node.__data__, self.index, self.group);\n      stop();\n    }\n  }\n\n  function stop() {\n    self.state = ENDED;\n    self.timer.stop();\n    delete schedules[id];\n    for (var i in schedules) return; // eslint-disable-line no-unused-vars\n    delete node.__transition;\n  }\n}\n", "import {STARTING, ENDING, ENDED} from \"./transition/schedule.js\";\n\nexport default function(node, name) {\n  var schedules = node.__transition,\n      schedule,\n      active,\n      empty = true,\n      i;\n\n  if (!schedules) return;\n\n  name = name == null ? null : name + \"\";\n\n  for (i in schedules) {\n    if ((schedule = schedules[i]).name !== name) { empty = false; continue; }\n    active = schedule.state > STARTING && schedule.state < ENDING;\n    schedule.state = ENDED;\n    schedule.timer.stop();\n    schedule.on.call(active ? \"interrupt\" : \"cancel\", node, node.__data__, schedule.index, schedule.group);\n    delete schedules[i];\n  }\n\n  if (empty) delete node.__transition;\n}\n", "import {get, set} from \"./schedule.js\";\n\nfunction tweenRemove(id, name) {\n  var tween0, tween1;\n  return function() {\n    var schedule = set(this, id),\n        tween = schedule.tween;\n\n    // If this node shared tween with the previous node,\n    // just assign the updated shared tween and we’re done!\n    // Otherwise, copy-on-write.\n    if (tween !== tween0) {\n      tween1 = tween0 = tween;\n      for (var i = 0, n = tween1.length; i < n; ++i) {\n        if (tween1[i].name === name) {\n          tween1 = tween1.slice();\n          tween1.splice(i, 1);\n          break;\n        }\n      }\n    }\n\n    schedule.tween = tween1;\n  };\n}\n\nfunction tweenFunction(id, name, value) {\n  var tween0, tween1;\n  if (typeof value !== \"function\") throw new Error;\n  return function() {\n    var schedule = set(this, id),\n        tween = schedule.tween;\n\n    // If this node shared tween with the previous node,\n    // just assign the updated shared tween and we’re done!\n    // Otherwise, copy-on-write.\n    if (tween !== tween0) {\n      tween1 = (tween0 = tween).slice();\n      for (var t = {name: name, value: value}, i = 0, n = tween1.length; i < n; ++i) {\n        if (tween1[i].name === name) {\n          tween1[i] = t;\n          break;\n        }\n      }\n      if (i === n) tween1.push(t);\n    }\n\n    schedule.tween = tween1;\n  };\n}\n\nexport default function(name, value) {\n  var id = this._id;\n\n  name += \"\";\n\n  if (arguments.length < 2) {\n    var tween = get(this.node(), id).tween;\n    for (var i = 0, n = tween.length, t; i < n; ++i) {\n      if ((t = tween[i]).name === name) {\n        return t.value;\n      }\n    }\n    return null;\n  }\n\n  return this.each((value == null ? tweenRemove : tweenFunction)(id, name, value));\n}\n\nexport function tweenValue(transition, name, value) {\n  var id = transition._id;\n\n  transition.each(function() {\n    var schedule = set(this, id);\n    (schedule.value || (schedule.value = {}))[name] = value.apply(this, arguments);\n  });\n\n  return function(node) {\n    return get(node, id).value[name];\n  };\n}\n", "import {color} from \"d3-color\";\nimport {interpolateNumber, interpolateRgb, interpolateString} from \"d3-interpolate\";\n\nexport default function(a, b) {\n  var c;\n  return (typeof b === \"number\" ? interpolateNumber\n      : b instanceof color ? interpolateRgb\n      : (c = color(b)) ? (b = c, interpolateRgb)\n      : interpolateString)(a, b);\n}\n", "import {interpolateTransformSvg as interpolateTransform} from \"d3-interpolate\";\nimport {namespace} from \"d3-selection\";\nimport {tweenValue} from \"./tween.js\";\nimport interpolate from \"./interpolate.js\";\n\nfunction attrRemove(name) {\n  return function() {\n    this.removeAttribute(name);\n  };\n}\n\nfunction attrRemoveNS(fullname) {\n  return function() {\n    this.removeAttributeNS(fullname.space, fullname.local);\n  };\n}\n\nfunction attrConstant(name, interpolate, value1) {\n  var string00,\n      string1 = value1 + \"\",\n      interpolate0;\n  return function() {\n    var string0 = this.getAttribute(name);\n    return string0 === string1 ? null\n        : string0 === string00 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\n\nfunction attrConstantNS(fullname, interpolate, value1) {\n  var string00,\n      string1 = value1 + \"\",\n      interpolate0;\n  return function() {\n    var string0 = this.getAttributeNS(fullname.space, fullname.local);\n    return string0 === string1 ? null\n        : string0 === string00 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\n\nfunction attrFunction(name, interpolate, value) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0, value1 = value(this), string1;\n    if (value1 == null) return void this.removeAttribute(name);\n    string0 = this.getAttribute(name);\n    string1 = value1 + \"\";\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\n\nfunction attrFunctionNS(fullname, interpolate, value) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0, value1 = value(this), string1;\n    if (value1 == null) return void this.removeAttributeNS(fullname.space, fullname.local);\n    string0 = this.getAttributeNS(fullname.space, fullname.local);\n    string1 = value1 + \"\";\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\n\nexport default function(name, value) {\n  var fullname = namespace(name), i = fullname === \"transform\" ? interpolateTransform : interpolate;\n  return this.attrTween(name, typeof value === \"function\"\n      ? (fullname.local ? attrFunctionNS : attrFunction)(fullname, i, tweenValue(this, \"attr.\" + name, value))\n      : value == null ? (fullname.local ? attrRemoveNS : attrRemove)(fullname)\n      : (fullname.local ? attrConstantNS : attrConstant)(fullname, i, value));\n}\n", "import {namespace} from \"d3-selection\";\n\nfunction attrInterpolate(name, i) {\n  return function(t) {\n    this.setAttribute(name, i.call(this, t));\n  };\n}\n\nfunction attrInterpolateNS(fullname, i) {\n  return function(t) {\n    this.setAttributeNS(fullname.space, fullname.local, i.call(this, t));\n  };\n}\n\nfunction attrTweenNS(fullname, value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && attrInterpolateNS(fullname, i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\n\nfunction attrTween(name, value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && attrInterpolate(name, i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\n\nexport default function(name, value) {\n  var key = \"attr.\" + name;\n  if (arguments.length < 2) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error;\n  var fullname = namespace(name);\n  return this.tween(key, (fullname.local ? attrTweenNS : attrTween)(fullname, value));\n}\n", "import {get, init} from \"./schedule.js\";\n\nfunction delayFunction(id, value) {\n  return function() {\n    init(this, id).delay = +value.apply(this, arguments);\n  };\n}\n\nfunction delayConstant(id, value) {\n  return value = +value, function() {\n    init(this, id).delay = value;\n  };\n}\n\nexport default function(value) {\n  var id = this._id;\n\n  return arguments.length\n      ? this.each((typeof value === \"function\"\n          ? delayFunction\n          : delayConstant)(id, value))\n      : get(this.node(), id).delay;\n}\n", "import {get, set} from \"./schedule.js\";\n\nfunction durationFunction(id, value) {\n  return function() {\n    set(this, id).duration = +value.apply(this, arguments);\n  };\n}\n\nfunction durationConstant(id, value) {\n  return value = +value, function() {\n    set(this, id).duration = value;\n  };\n}\n\nexport default function(value) {\n  var id = this._id;\n\n  return arguments.length\n      ? this.each((typeof value === \"function\"\n          ? durationFunction\n          : durationConstant)(id, value))\n      : get(this.node(), id).duration;\n}\n", "import {get, set} from \"./schedule.js\";\n\nfunction easeConstant(id, value) {\n  if (typeof value !== \"function\") throw new Error;\n  return function() {\n    set(this, id).ease = value;\n  };\n}\n\nexport default function(value) {\n  var id = this._id;\n\n  return arguments.length\n      ? this.each(easeConstant(id, value))\n      : get(this.node(), id).ease;\n}\n", "import {set} from \"./schedule.js\";\n\nfunction easeVarying(id, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (typeof v !== \"function\") throw new Error;\n    set(this, id).ease = v;\n  };\n}\n\nexport default function(value) {\n  if (typeof value !== \"function\") throw new Error;\n  return this.each(easeVarying(this._id, value));\n}\n", "import {matcher} from \"d3-selection\";\nimport {Transition} from \"./index.js\";\n\nexport default function(match) {\n  if (typeof match !== \"function\") match = matcher(match);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = [], node, i = 0; i < n; ++i) {\n      if ((node = group[i]) && match.call(node, node.__data__, i, group)) {\n        subgroup.push(node);\n      }\n    }\n  }\n\n  return new Transition(subgroups, this._parents, this._name, this._id);\n}\n", "import {Transition} from \"./index.js\";\n\nexport default function(transition) {\n  if (transition._id !== this._id) throw new Error;\n\n  for (var groups0 = this._groups, groups1 = transition._groups, m0 = groups0.length, m1 = groups1.length, m = Math.min(m0, m1), merges = new Array(m0), j = 0; j < m; ++j) {\n    for (var group0 = groups0[j], group1 = groups1[j], n = group0.length, merge = merges[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group0[i] || group1[i]) {\n        merge[i] = node;\n      }\n    }\n  }\n\n  for (; j < m0; ++j) {\n    merges[j] = groups0[j];\n  }\n\n  return new Transition(merges, this._parents, this._name, this._id);\n}\n", "import {get, set, init} from \"./schedule.js\";\n\nfunction start(name) {\n  return (name + \"\").trim().split(/^|\\s+/).every(function(t) {\n    var i = t.indexOf(\".\");\n    if (i >= 0) t = t.slice(0, i);\n    return !t || t === \"start\";\n  });\n}\n\nfunction onFunction(id, name, listener) {\n  var on0, on1, sit = start(name) ? init : set;\n  return function() {\n    var schedule = sit(this, id),\n        on = schedule.on;\n\n    // If this node shared a dispatch with the previous node,\n    // just assign the updated shared dispatch and we’re done!\n    // Otherwise, copy-on-write.\n    if (on !== on0) (on1 = (on0 = on).copy()).on(name, listener);\n\n    schedule.on = on1;\n  };\n}\n\nexport default function(name, listener) {\n  var id = this._id;\n\n  return arguments.length < 2\n      ? get(this.node(), id).on.on(name)\n      : this.each(onFunction(id, name, listener));\n}\n", "function removeFunction(id) {\n  return function() {\n    var parent = this.parentNode;\n    for (var i in this.__transition) if (+i !== id) return;\n    if (parent) parent.removeChild(this);\n  };\n}\n\nexport default function() {\n  return this.on(\"end.remove\", removeFunction(this._id));\n}\n", "import {selector} from \"d3-selection\";\nimport {Transition} from \"./index.js\";\nimport schedule, {get} from \"./schedule.js\";\n\nexport default function(select) {\n  var name = this._name,\n      id = this._id;\n\n  if (typeof select !== \"function\") select = selector(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = new Array(n), node, subnode, i = 0; i < n; ++i) {\n      if ((node = group[i]) && (subnode = select.call(node, node.__data__, i, group))) {\n        if (\"__data__\" in node) subnode.__data__ = node.__data__;\n        subgroup[i] = subnode;\n        schedule(subgroup[i], name, id, i, subgroup, get(node, id));\n      }\n    }\n  }\n\n  return new Transition(subgroups, this._parents, name, id);\n}\n", "import {selectorAll} from \"d3-selection\";\nimport {Transition} from \"./index.js\";\nimport schedule, {get} from \"./schedule.js\";\n\nexport default function(select) {\n  var name = this._name,\n      id = this._id;\n\n  if (typeof select !== \"function\") select = selectorAll(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = [], parents = [], j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        for (var children = select.call(node, node.__data__, i, group), child, inherit = get(node, id), k = 0, l = children.length; k < l; ++k) {\n          if (child = children[k]) {\n            schedule(child, name, id, k, children, inherit);\n          }\n        }\n        subgroups.push(children);\n        parents.push(node);\n      }\n    }\n  }\n\n  return new Transition(subgroups, parents, name, id);\n}\n", "import {selection} from \"d3-selection\";\n\nvar Selection = selection.prototype.constructor;\n\nexport default function() {\n  return new Selection(this._groups, this._parents);\n}\n", "import {interpolateTransformCss as interpolateTransform} from \"d3-interpolate\";\nimport {style} from \"d3-selection\";\nimport {set} from \"./schedule.js\";\nimport {tweenValue} from \"./tween.js\";\nimport interpolate from \"./interpolate.js\";\n\nfunction styleNull(name, interpolate) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0 = style(this, name),\n        string1 = (this.style.removeProperty(name), style(this, name));\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, string10 = string1);\n  };\n}\n\nfunction styleRemove(name) {\n  return function() {\n    this.style.removeProperty(name);\n  };\n}\n\nfunction styleConstant(name, interpolate, value1) {\n  var string00,\n      string1 = value1 + \"\",\n      interpolate0;\n  return function() {\n    var string0 = style(this, name);\n    return string0 === string1 ? null\n        : string0 === string00 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\n\nfunction styleFunction(name, interpolate, value) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0 = style(this, name),\n        value1 = value(this),\n        string1 = value1 + \"\";\n    if (value1 == null) string1 = value1 = (this.style.removeProperty(name), style(this, name));\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\n\nfunction styleMaybeRemove(id, name) {\n  var on0, on1, listener0, key = \"style.\" + name, event = \"end.\" + key, remove;\n  return function() {\n    var schedule = set(this, id),\n        on = schedule.on,\n        listener = schedule.value[key] == null ? remove || (remove = styleRemove(name)) : undefined;\n\n    // If this node shared a dispatch with the previous node,\n    // just assign the updated shared dispatch and we’re done!\n    // Otherwise, copy-on-write.\n    if (on !== on0 || listener0 !== listener) (on1 = (on0 = on).copy()).on(event, listener0 = listener);\n\n    schedule.on = on1;\n  };\n}\n\nexport default function(name, value, priority) {\n  var i = (name += \"\") === \"transform\" ? interpolateTransform : interpolate;\n  return value == null ? this\n      .styleTween(name, styleNull(name, i))\n      .on(\"end.style.\" + name, styleRemove(name))\n    : typeof value === \"function\" ? this\n      .styleTween(name, styleFunction(name, i, tweenValue(this, \"style.\" + name, value)))\n      .each(styleMaybeRemove(this._id, name))\n    : this\n      .styleTween(name, styleConstant(name, i, value), priority)\n      .on(\"end.style.\" + name, null);\n}\n", "function styleInterpolate(name, i, priority) {\n  return function(t) {\n    this.style.setProperty(name, i.call(this, t), priority);\n  };\n}\n\nfunction styleTween(name, value, priority) {\n  var t, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t = (i0 = i) && styleInterpolate(name, i, priority);\n    return t;\n  }\n  tween._value = value;\n  return tween;\n}\n\nexport default function(name, value, priority) {\n  var key = \"style.\" + (name += \"\");\n  if (arguments.length < 2) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error;\n  return this.tween(key, styleTween(name, value, priority == null ? \"\" : priority));\n}\n", "import {tweenValue} from \"./tween.js\";\n\nfunction textConstant(value) {\n  return function() {\n    this.textContent = value;\n  };\n}\n\nfunction textFunction(value) {\n  return function() {\n    var value1 = value(this);\n    this.textContent = value1 == null ? \"\" : value1;\n  };\n}\n\nexport default function(value) {\n  return this.tween(\"text\", typeof value === \"function\"\n      ? textFunction(tweenValue(this, \"text\", value))\n      : textConstant(value == null ? \"\" : value + \"\"));\n}\n", "function textInterpolate(i) {\n  return function(t) {\n    this.textContent = i.call(this, t);\n  };\n}\n\nfunction textTween(value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && textInterpolate(i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\n\nexport default function(value) {\n  var key = \"text\";\n  if (arguments.length < 1) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error;\n  return this.tween(key, textTween(value));\n}\n", "import {Transition, newId} from \"./index.js\";\nimport schedule, {get} from \"./schedule.js\";\n\nexport default function() {\n  var name = this._name,\n      id0 = this._id,\n      id1 = newId();\n\n  for (var groups = this._groups, m = groups.length, j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        var inherit = get(node, id0);\n        schedule(node, name, id1, i, group, {\n          time: inherit.time + inherit.delay + inherit.duration,\n          delay: 0,\n          duration: inherit.duration,\n          ease: inherit.ease\n        });\n      }\n    }\n  }\n\n  return new Transition(groups, this._parents, name, id1);\n}\n", "import {set} from \"./schedule.js\";\n\nexport default function() {\n  var on0, on1, that = this, id = that._id, size = that.size();\n  return new Promise(function(resolve, reject) {\n    var cancel = {value: reject},\n        end = {value: function() { if (--size === 0) resolve(); }};\n\n    that.each(function() {\n      var schedule = set(this, id),\n          on = schedule.on;\n\n      // If this node shared a dispatch with the previous node,\n      // just assign the updated shared dispatch and we’re done!\n      // Otherwise, copy-on-write.\n      if (on !== on0) {\n        on1 = (on0 = on).copy();\n        on1._.cancel.push(cancel);\n        on1._.interrupt.push(cancel);\n        on1._.end.push(end);\n      }\n\n      schedule.on = on1;\n    });\n\n    // The selection was empty, resolve end immediately\n    if (size === 0) resolve();\n  });\n}\n", "import {selection} from \"d3-selection\";\nimport transition_attr from \"./attr.js\";\nimport transition_attrTween from \"./attrTween.js\";\nimport transition_delay from \"./delay.js\";\nimport transition_duration from \"./duration.js\";\nimport transition_ease from \"./ease.js\";\nimport transition_easeVarying from \"./easeVarying.js\";\nimport transition_filter from \"./filter.js\";\nimport transition_merge from \"./merge.js\";\nimport transition_on from \"./on.js\";\nimport transition_remove from \"./remove.js\";\nimport transition_select from \"./select.js\";\nimport transition_selectAll from \"./selectAll.js\";\nimport transition_selection from \"./selection.js\";\nimport transition_style from \"./style.js\";\nimport transition_styleTween from \"./styleTween.js\";\nimport transition_text from \"./text.js\";\nimport transition_textTween from \"./textTween.js\";\nimport transition_transition from \"./transition.js\";\nimport transition_tween from \"./tween.js\";\nimport transition_end from \"./end.js\";\n\nvar id = 0;\n\nexport function Transition(groups, parents, name, id) {\n  this._groups = groups;\n  this._parents = parents;\n  this._name = name;\n  this._id = id;\n}\n\nexport default function transition(name) {\n  return selection().transition(name);\n}\n\nexport function newId() {\n  return ++id;\n}\n\nvar selection_prototype = selection.prototype;\n\nTransition.prototype = transition.prototype = {\n  constructor: Transition,\n  select: transition_select,\n  selectAll: transition_selectAll,\n  selectChild: selection_prototype.selectChild,\n  selectChildren: selection_prototype.selectChildren,\n  filter: transition_filter,\n  merge: transition_merge,\n  selection: transition_selection,\n  transition: transition_transition,\n  call: selection_prototype.call,\n  nodes: selection_prototype.nodes,\n  node: selection_prototype.node,\n  size: selection_prototype.size,\n  empty: selection_prototype.empty,\n  each: selection_prototype.each,\n  on: transition_on,\n  attr: transition_attr,\n  attrTween: transition_attrTween,\n  style: transition_style,\n  styleTween: transition_styleTween,\n  text: transition_text,\n  textTween: transition_textTween,\n  remove: transition_remove,\n  tween: transition_tween,\n  delay: transition_delay,\n  duration: transition_duration,\n  ease: transition_ease,\n  easeVarying: transition_easeVarying,\n  end: transition_end,\n  [Symbol.iterator]: selection_prototype[Symbol.iterator]\n};\n", "export const linear = t => +t;\n", "export function quadIn(t) {\n  return t * t;\n}\n\nexport function quadOut(t) {\n  return t * (2 - t);\n}\n\nexport function quadInOut(t) {\n  return ((t *= 2) <= 1 ? t * t : --t * (2 - t) + 1) / 2;\n}\n", "export function cubicIn(t) {\n  return t * t * t;\n}\n\nexport function cubicOut(t) {\n  return --t * t * t + 1;\n}\n\nexport function cubicInOut(t) {\n  return ((t *= 2) <= 1 ? t * t * t : (t -= 2) * t * t + 2) / 2;\n}\n", "var exponent = 3;\n\nexport var polyIn = (function custom(e) {\n  e = +e;\n\n  function polyIn(t) {\n    return Math.pow(t, e);\n  }\n\n  polyIn.exponent = custom;\n\n  return polyIn;\n})(exponent);\n\nexport var polyOut = (function custom(e) {\n  e = +e;\n\n  function polyOut(t) {\n    return 1 - Math.pow(1 - t, e);\n  }\n\n  polyOut.exponent = custom;\n\n  return polyOut;\n})(exponent);\n\nexport var polyInOut = (function custom(e) {\n  e = +e;\n\n  function polyInOut(t) {\n    return ((t *= 2) <= 1 ? Math.pow(t, e) : 2 - Math.pow(2 - t, e)) / 2;\n  }\n\n  polyInOut.exponent = custom;\n\n  return polyInOut;\n})(exponent);\n", "var pi = Math.PI,\n    halfPi = pi / 2;\n\nexport function sinIn(t) {\n  return (+t === 1) ? 1 : 1 - Math.cos(t * halfPi);\n}\n\nexport function sinOut(t) {\n  return Math.sin(t * halfPi);\n}\n\nexport function sinInOut(t) {\n  return (1 - Math.cos(pi * t)) / 2;\n}\n", "// tpmt is two power minus ten times t scaled to [0,1]\nexport function tpmt(x) {\n  return (Math.pow(2, -10 * x) - 0.0009765625) * 1.0009775171065494;\n}\n", "import {tpmt} from \"./math.js\";\n\nexport function expIn(t) {\n  return tpmt(1 - +t);\n}\n\nexport function expOut(t) {\n  return 1 - tpmt(t);\n}\n\nexport function expInOut(t) {\n  return ((t *= 2) <= 1 ? tpmt(1 - t) : 2 - tpmt(t - 1)) / 2;\n}\n", "export function circleIn(t) {\n  return 1 - Math.sqrt(1 - t * t);\n}\n\nexport function circleOut(t) {\n  return Math.sqrt(1 - --t * t);\n}\n\nexport function circleInOut(t) {\n  return ((t *= 2) <= 1 ? 1 - Math.sqrt(1 - t * t) : Math.sqrt(1 - (t -= 2) * t) + 1) / 2;\n}\n", "var b1 = 4 / 11,\n    b2 = 6 / 11,\n    b3 = 8 / 11,\n    b4 = 3 / 4,\n    b5 = 9 / 11,\n    b6 = 10 / 11,\n    b7 = 15 / 16,\n    b8 = 21 / 22,\n    b9 = 63 / 64,\n    b0 = 1 / b1 / b1;\n\nexport function bounceIn(t) {\n  return 1 - bounceOut(1 - t);\n}\n\nexport function bounceOut(t) {\n  return (t = +t) < b1 ? b0 * t * t : t < b3 ? b0 * (t -= b2) * t + b4 : t < b6 ? b0 * (t -= b5) * t + b7 : b0 * (t -= b8) * t + b9;\n}\n\nexport function bounceInOut(t) {\n  return ((t *= 2) <= 1 ? 1 - bounceOut(1 - t) : bounceOut(t - 1) + 1) / 2;\n}\n", "var overshoot = 1.70158;\n\nexport var backIn = (function custom(s) {\n  s = +s;\n\n  function backIn(t) {\n    return (t = +t) * t * (s * (t - 1) + t);\n  }\n\n  backIn.overshoot = custom;\n\n  return backIn;\n})(overshoot);\n\nexport var backOut = (function custom(s) {\n  s = +s;\n\n  function backOut(t) {\n    return --t * t * ((t + 1) * s + t) + 1;\n  }\n\n  backOut.overshoot = custom;\n\n  return backOut;\n})(overshoot);\n\nexport var backInOut = (function custom(s) {\n  s = +s;\n\n  function backInOut(t) {\n    return ((t *= 2) < 1 ? t * t * ((s + 1) * t - s) : (t -= 2) * t * ((s + 1) * t + s) + 2) / 2;\n  }\n\n  backInOut.overshoot = custom;\n\n  return backInOut;\n})(overshoot);\n", "import {tpmt} from \"./math.js\";\n\nvar tau = 2 * Math.PI,\n    amplitude = 1,\n    period = 0.3;\n\nexport var elasticIn = (function custom(a, p) {\n  var s = Math.asin(1 / (a = Math.max(1, a))) * (p /= tau);\n\n  function elasticIn(t) {\n    return a * tpmt(-(--t)) * Math.sin((s - t) / p);\n  }\n\n  elasticIn.amplitude = function(a) { return custom(a, p * tau); };\n  elasticIn.period = function(p) { return custom(a, p); };\n\n  return elasticIn;\n})(amplitude, period);\n\nexport var elasticOut = (function custom(a, p) {\n  var s = Math.asin(1 / (a = Math.max(1, a))) * (p /= tau);\n\n  function elasticOut(t) {\n    return 1 - a * tpmt(t = +t) * Math.sin((t + s) / p);\n  }\n\n  elasticOut.amplitude = function(a) { return custom(a, p * tau); };\n  elasticOut.period = function(p) { return custom(a, p); };\n\n  return elasticOut;\n})(amplitude, period);\n\nexport var elasticInOut = (function custom(a, p) {\n  var s = Math.asin(1 / (a = Math.max(1, a))) * (p /= tau);\n\n  function elasticInOut(t) {\n    return ((t = t * 2 - 1) < 0\n        ? a * tpmt(-t) * Math.sin((s - t) / p)\n        : 2 - a * tpmt(t) * Math.sin((s + t) / p)) / 2;\n  }\n\n  elasticInOut.amplitude = function(a) { return custom(a, p * tau); };\n  elasticInOut.period = function(p) { return custom(a, p); };\n\n  return elasticInOut;\n})(amplitude, period);\n", "import {Transition} from \"./transition/index.js\";\nimport {SCHEDULED} from \"./transition/schedule.js\";\n\nvar root = [null];\n\nexport default function(node, name) {\n  var schedules = node.__transition,\n      schedule,\n      i;\n\n  if (schedules) {\n    name = name == null ? null : name + \"\";\n    for (i in schedules) {\n      if ((schedule = schedules[i]).state > SCHEDULED && schedule.name === name) {\n        return new Transition([[node]], root, name, +i);\n      }\n    }\n  }\n\n  return null;\n}\n", "import interrupt from \"../interrupt.js\";\n\nexport default function(name) {\n  return this.each(function() {\n    interrupt(this, name);\n  });\n}\n", "import {Transition, newId} from \"../transition/index.js\";\nimport schedule from \"../transition/schedule.js\";\nimport {easeCubicInOut} from \"d3-ease\";\nimport {now} from \"d3-timer\";\n\nvar defaultTiming = {\n  time: null, // Set on use.\n  delay: 0,\n  duration: 250,\n  ease: easeCubicInOut\n};\n\nfunction inherit(node, id) {\n  var timing;\n  while (!(timing = node.__transition) || !(timing = timing[id])) {\n    if (!(node = node.parentNode)) {\n      throw new Error(`transition ${id} not found`);\n    }\n  }\n  return timing;\n}\n\nexport default function(name) {\n  var id,\n      timing;\n\n  if (name instanceof Transition) {\n    id = name._id, name = name._name;\n  } else {\n    id = newId(), (timing = defaultTiming).time = now(), name = name == null ? null : name + \"\";\n  }\n\n  for (var groups = this._groups, m = groups.length, j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        schedule(node, name, id, i, group, timing || inherit(node, id));\n      }\n    }\n  }\n\n  return new Transition(groups, this._parents, name, id);\n}\n", "import {selection} from \"d3-selection\";\nimport selection_interrupt from \"./interrupt.js\";\nimport selection_transition from \"./transition.js\";\n\nselection.prototype.interrupt = selection_interrupt;\nselection.prototype.transition = selection_transition;\n", "export function Transform(k, x, y) {\n  this.k = k;\n  this.x = x;\n  this.y = y;\n}\n\nTransform.prototype = {\n  constructor: Transform,\n  scale: function(k) {\n    return k === 1 ? this : new Transform(this.k * k, this.x, this.y);\n  },\n  translate: function(x, y) {\n    return x === 0 & y === 0 ? this : new Transform(this.k, this.x + this.k * x, this.y + this.k * y);\n  },\n  apply: function(point) {\n    return [point[0] * this.k + this.x, point[1] * this.k + this.y];\n  },\n  applyX: function(x) {\n    return x * this.k + this.x;\n  },\n  applyY: function(y) {\n    return y * this.k + this.y;\n  },\n  invert: function(location) {\n    return [(location[0] - this.x) / this.k, (location[1] - this.y) / this.k];\n  },\n  invertX: function(x) {\n    return (x - this.x) / this.k;\n  },\n  invertY: function(y) {\n    return (y - this.y) / this.k;\n  },\n  rescaleX: function(x) {\n    return x.copy().domain(x.range().map(this.invertX, this).map(x.invert, x));\n  },\n  rescaleY: function(y) {\n    return y.copy().domain(y.range().map(this.invertY, this).map(y.invert, y));\n  },\n  toString: function() {\n    return \"translate(\" + this.x + \",\" + this.y + \") scale(\" + this.k + \")\";\n  }\n};\n\nexport var identity = new Transform(1, 0, 0);\n\ntransform.prototype = Transform.prototype;\n\nexport default function transform(node) {\n  while (!node.__zoom) if (!(node = node.parentNode)) return identity;\n  return node.__zoom;\n}\n", "export default x => () => x;\n", "export default function ZoomEvent(type, {\n  sourceEvent,\n  target,\n  transform,\n  dispatch\n}) {\n  Object.defineProperties(this, {\n    type: {value: type, enumerable: true, configurable: true},\n    sourceEvent: {value: sourceEvent, enumerable: true, configurable: true},\n    target: {value: target, enumerable: true, configurable: true},\n    transform: {value: transform, enumerable: true, configurable: true},\n    _: {value: dispatch}\n  });\n}\n", "export function nopropagation(event) {\n  event.stopImmediatePropagation();\n}\n\nexport default function(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\n", "import {dispatch} from \"d3-dispatch\";\nimport {dragDisable, dragEnable} from \"d3-drag\";\nimport {interpolateZoom} from \"d3-interpolate\";\nimport {select, pointer} from \"d3-selection\";\nimport {interrupt} from \"d3-transition\";\nimport constant from \"./constant.js\";\nimport ZoomEvent from \"./event.js\";\nimport {Transform, identity} from \"./transform.js\";\nimport noevent, {nopropagation} from \"./noevent.js\";\n\n// Ignore right-click, since that should open the context menu.\n// except for pinch-to-zoom, which is sent as a wheel+ctrlKey event\nfunction defaultFilter(event) {\n  return (!event.ctrlKey || event.type === 'wheel') && !event.button;\n}\n\nfunction defaultExtent() {\n  var e = this;\n  if (e instanceof SVGElement) {\n    e = e.ownerSVGElement || e;\n    if (e.hasAttribute(\"viewBox\")) {\n      e = e.viewBox.baseVal;\n      return [[e.x, e.y], [e.x + e.width, e.y + e.height]];\n    }\n    return [[0, 0], [e.width.baseVal.value, e.height.baseVal.value]];\n  }\n  return [[0, 0], [e.clientWidth, e.clientHeight]];\n}\n\nfunction defaultTransform() {\n  return this.__zoom || identity;\n}\n\nfunction defaultWheelDelta(event) {\n  return -event.deltaY * (event.deltaMode === 1 ? 0.05 : event.deltaMode ? 1 : 0.002) * (event.ctrlKey ? 10 : 1);\n}\n\nfunction defaultTouchable() {\n  return navigator.maxTouchPoints || (\"ontouchstart\" in this);\n}\n\nfunction defaultConstrain(transform, extent, translateExtent) {\n  var dx0 = transform.invertX(extent[0][0]) - translateExtent[0][0],\n      dx1 = transform.invertX(extent[1][0]) - translateExtent[1][0],\n      dy0 = transform.invertY(extent[0][1]) - translateExtent[0][1],\n      dy1 = transform.invertY(extent[1][1]) - translateExtent[1][1];\n  return transform.translate(\n    dx1 > dx0 ? (dx0 + dx1) / 2 : Math.min(0, dx0) || Math.max(0, dx1),\n    dy1 > dy0 ? (dy0 + dy1) / 2 : Math.min(0, dy0) || Math.max(0, dy1)\n  );\n}\n\nexport default function() {\n  var filter = defaultFilter,\n      extent = defaultExtent,\n      constrain = defaultConstrain,\n      wheelDelta = defaultWheelDelta,\n      touchable = defaultTouchable,\n      scaleExtent = [0, Infinity],\n      translateExtent = [[-Infinity, -Infinity], [Infinity, Infinity]],\n      duration = 250,\n      interpolate = interpolateZoom,\n      listeners = dispatch(\"start\", \"zoom\", \"end\"),\n      touchstarting,\n      touchfirst,\n      touchending,\n      touchDelay = 500,\n      wheelDelay = 150,\n      clickDistance2 = 0,\n      tapDistance = 10;\n\n  function zoom(selection) {\n    selection\n        .property(\"__zoom\", defaultTransform)\n        .on(\"wheel.zoom\", wheeled, {passive: false})\n        .on(\"mousedown.zoom\", mousedowned)\n        .on(\"dblclick.zoom\", dblclicked)\n      .filter(touchable)\n        .on(\"touchstart.zoom\", touchstarted)\n        .on(\"touchmove.zoom\", touchmoved)\n        .on(\"touchend.zoom touchcancel.zoom\", touchended)\n        .style(\"-webkit-tap-highlight-color\", \"rgba(0,0,0,0)\");\n  }\n\n  zoom.transform = function(collection, transform, point, event) {\n    var selection = collection.selection ? collection.selection() : collection;\n    selection.property(\"__zoom\", defaultTransform);\n    if (collection !== selection) {\n      schedule(collection, transform, point, event);\n    } else {\n      selection.interrupt().each(function() {\n        gesture(this, arguments)\n          .event(event)\n          .start()\n          .zoom(null, typeof transform === \"function\" ? transform.apply(this, arguments) : transform)\n          .end();\n      });\n    }\n  };\n\n  zoom.scaleBy = function(selection, k, p, event) {\n    zoom.scaleTo(selection, function() {\n      var k0 = this.__zoom.k,\n          k1 = typeof k === \"function\" ? k.apply(this, arguments) : k;\n      return k0 * k1;\n    }, p, event);\n  };\n\n  zoom.scaleTo = function(selection, k, p, event) {\n    zoom.transform(selection, function() {\n      var e = extent.apply(this, arguments),\n          t0 = this.__zoom,\n          p0 = p == null ? centroid(e) : typeof p === \"function\" ? p.apply(this, arguments) : p,\n          p1 = t0.invert(p0),\n          k1 = typeof k === \"function\" ? k.apply(this, arguments) : k;\n      return constrain(translate(scale(t0, k1), p0, p1), e, translateExtent);\n    }, p, event);\n  };\n\n  zoom.translateBy = function(selection, x, y, event) {\n    zoom.transform(selection, function() {\n      return constrain(this.__zoom.translate(\n        typeof x === \"function\" ? x.apply(this, arguments) : x,\n        typeof y === \"function\" ? y.apply(this, arguments) : y\n      ), extent.apply(this, arguments), translateExtent);\n    }, null, event);\n  };\n\n  zoom.translateTo = function(selection, x, y, p, event) {\n    zoom.transform(selection, function() {\n      var e = extent.apply(this, arguments),\n          t = this.__zoom,\n          p0 = p == null ? centroid(e) : typeof p === \"function\" ? p.apply(this, arguments) : p;\n      return constrain(identity.translate(p0[0], p0[1]).scale(t.k).translate(\n        typeof x === \"function\" ? -x.apply(this, arguments) : -x,\n        typeof y === \"function\" ? -y.apply(this, arguments) : -y\n      ), e, translateExtent);\n    }, p, event);\n  };\n\n  function scale(transform, k) {\n    k = Math.max(scaleExtent[0], Math.min(scaleExtent[1], k));\n    return k === transform.k ? transform : new Transform(k, transform.x, transform.y);\n  }\n\n  function translate(transform, p0, p1) {\n    var x = p0[0] - p1[0] * transform.k, y = p0[1] - p1[1] * transform.k;\n    return x === transform.x && y === transform.y ? transform : new Transform(transform.k, x, y);\n  }\n\n  function centroid(extent) {\n    return [(+extent[0][0] + +extent[1][0]) / 2, (+extent[0][1] + +extent[1][1]) / 2];\n  }\n\n  function schedule(transition, transform, point, event) {\n    transition\n        .on(\"start.zoom\", function() { gesture(this, arguments).event(event).start(); })\n        .on(\"interrupt.zoom end.zoom\", function() { gesture(this, arguments).event(event).end(); })\n        .tween(\"zoom\", function() {\n          var that = this,\n              args = arguments,\n              g = gesture(that, args).event(event),\n              e = extent.apply(that, args),\n              p = point == null ? centroid(e) : typeof point === \"function\" ? point.apply(that, args) : point,\n              w = Math.max(e[1][0] - e[0][0], e[1][1] - e[0][1]),\n              a = that.__zoom,\n              b = typeof transform === \"function\" ? transform.apply(that, args) : transform,\n              i = interpolate(a.invert(p).concat(w / a.k), b.invert(p).concat(w / b.k));\n          return function(t) {\n            if (t === 1) t = b; // Avoid rounding error on end.\n            else { var l = i(t), k = w / l[2]; t = new Transform(k, p[0] - l[0] * k, p[1] - l[1] * k); }\n            g.zoom(null, t);\n          };\n        });\n  }\n\n  function gesture(that, args, clean) {\n    return (!clean && that.__zooming) || new Gesture(that, args);\n  }\n\n  function Gesture(that, args) {\n    this.that = that;\n    this.args = args;\n    this.active = 0;\n    this.sourceEvent = null;\n    this.extent = extent.apply(that, args);\n    this.taps = 0;\n  }\n\n  Gesture.prototype = {\n    event: function(event) {\n      if (event) this.sourceEvent = event;\n      return this;\n    },\n    start: function() {\n      if (++this.active === 1) {\n        this.that.__zooming = this;\n        this.emit(\"start\");\n      }\n      return this;\n    },\n    zoom: function(key, transform) {\n      if (this.mouse && key !== \"mouse\") this.mouse[1] = transform.invert(this.mouse[0]);\n      if (this.touch0 && key !== \"touch\") this.touch0[1] = transform.invert(this.touch0[0]);\n      if (this.touch1 && key !== \"touch\") this.touch1[1] = transform.invert(this.touch1[0]);\n      this.that.__zoom = transform;\n      this.emit(\"zoom\");\n      return this;\n    },\n    end: function() {\n      if (--this.active === 0) {\n        delete this.that.__zooming;\n        this.emit(\"end\");\n      }\n      return this;\n    },\n    emit: function(type) {\n      var d = select(this.that).datum();\n      listeners.call(\n        type,\n        this.that,\n        new ZoomEvent(type, {\n          sourceEvent: this.sourceEvent,\n          target: zoom,\n          type,\n          transform: this.that.__zoom,\n          dispatch: listeners\n        }),\n        d\n      );\n    }\n  };\n\n  function wheeled(event, ...args) {\n    if (!filter.apply(this, arguments)) return;\n    var g = gesture(this, args).event(event),\n        t = this.__zoom,\n        k = Math.max(scaleExtent[0], Math.min(scaleExtent[1], t.k * Math.pow(2, wheelDelta.apply(this, arguments)))),\n        p = pointer(event);\n\n    // If the mouse is in the same location as before, reuse it.\n    // If there were recent wheel events, reset the wheel idle timeout.\n    if (g.wheel) {\n      if (g.mouse[0][0] !== p[0] || g.mouse[0][1] !== p[1]) {\n        g.mouse[1] = t.invert(g.mouse[0] = p);\n      }\n      clearTimeout(g.wheel);\n    }\n\n    // If this wheel event won’t trigger a transform change, ignore it.\n    else if (t.k === k) return;\n\n    // Otherwise, capture the mouse point and location at the start.\n    else {\n      g.mouse = [p, t.invert(p)];\n      interrupt(this);\n      g.start();\n    }\n\n    noevent(event);\n    g.wheel = setTimeout(wheelidled, wheelDelay);\n    g.zoom(\"mouse\", constrain(translate(scale(t, k), g.mouse[0], g.mouse[1]), g.extent, translateExtent));\n\n    function wheelidled() {\n      g.wheel = null;\n      g.end();\n    }\n  }\n\n  function mousedowned(event, ...args) {\n    if (touchending || !filter.apply(this, arguments)) return;\n    var currentTarget = event.currentTarget,\n        g = gesture(this, args, true).event(event),\n        v = select(event.view).on(\"mousemove.zoom\", mousemoved, true).on(\"mouseup.zoom\", mouseupped, true),\n        p = pointer(event, currentTarget),\n        x0 = event.clientX,\n        y0 = event.clientY;\n\n    dragDisable(event.view);\n    nopropagation(event);\n    g.mouse = [p, this.__zoom.invert(p)];\n    interrupt(this);\n    g.start();\n\n    function mousemoved(event) {\n      noevent(event);\n      if (!g.moved) {\n        var dx = event.clientX - x0, dy = event.clientY - y0;\n        g.moved = dx * dx + dy * dy > clickDistance2;\n      }\n      g.event(event)\n       .zoom(\"mouse\", constrain(translate(g.that.__zoom, g.mouse[0] = pointer(event, currentTarget), g.mouse[1]), g.extent, translateExtent));\n    }\n\n    function mouseupped(event) {\n      v.on(\"mousemove.zoom mouseup.zoom\", null);\n      dragEnable(event.view, g.moved);\n      noevent(event);\n      g.event(event).end();\n    }\n  }\n\n  function dblclicked(event, ...args) {\n    if (!filter.apply(this, arguments)) return;\n    var t0 = this.__zoom,\n        p0 = pointer(event.changedTouches ? event.changedTouches[0] : event, this),\n        p1 = t0.invert(p0),\n        k1 = t0.k * (event.shiftKey ? 0.5 : 2),\n        t1 = constrain(translate(scale(t0, k1), p0, p1), extent.apply(this, args), translateExtent);\n\n    noevent(event);\n    if (duration > 0) select(this).transition().duration(duration).call(schedule, t1, p0, event);\n    else select(this).call(zoom.transform, t1, p0, event);\n  }\n\n  function touchstarted(event, ...args) {\n    if (!filter.apply(this, arguments)) return;\n    var touches = event.touches,\n        n = touches.length,\n        g = gesture(this, args, event.changedTouches.length === n).event(event),\n        started, i, t, p;\n\n    nopropagation(event);\n    for (i = 0; i < n; ++i) {\n      t = touches[i], p = pointer(t, this);\n      p = [p, this.__zoom.invert(p), t.identifier];\n      if (!g.touch0) g.touch0 = p, started = true, g.taps = 1 + !!touchstarting;\n      else if (!g.touch1 && g.touch0[2] !== p[2]) g.touch1 = p, g.taps = 0;\n    }\n\n    if (touchstarting) touchstarting = clearTimeout(touchstarting);\n\n    if (started) {\n      if (g.taps < 2) touchfirst = p[0], touchstarting = setTimeout(function() { touchstarting = null; }, touchDelay);\n      interrupt(this);\n      g.start();\n    }\n  }\n\n  function touchmoved(event, ...args) {\n    if (!this.__zooming) return;\n    var g = gesture(this, args).event(event),\n        touches = event.changedTouches,\n        n = touches.length, i, t, p, l;\n\n    noevent(event);\n    for (i = 0; i < n; ++i) {\n      t = touches[i], p = pointer(t, this);\n      if (g.touch0 && g.touch0[2] === t.identifier) g.touch0[0] = p;\n      else if (g.touch1 && g.touch1[2] === t.identifier) g.touch1[0] = p;\n    }\n    t = g.that.__zoom;\n    if (g.touch1) {\n      var p0 = g.touch0[0], l0 = g.touch0[1],\n          p1 = g.touch1[0], l1 = g.touch1[1],\n          dp = (dp = p1[0] - p0[0]) * dp + (dp = p1[1] - p0[1]) * dp,\n          dl = (dl = l1[0] - l0[0]) * dl + (dl = l1[1] - l0[1]) * dl;\n      t = scale(t, Math.sqrt(dp / dl));\n      p = [(p0[0] + p1[0]) / 2, (p0[1] + p1[1]) / 2];\n      l = [(l0[0] + l1[0]) / 2, (l0[1] + l1[1]) / 2];\n    }\n    else if (g.touch0) p = g.touch0[0], l = g.touch0[1];\n    else return;\n\n    g.zoom(\"touch\", constrain(translate(t, p, l), g.extent, translateExtent));\n  }\n\n  function touchended(event, ...args) {\n    if (!this.__zooming) return;\n    var g = gesture(this, args).event(event),\n        touches = event.changedTouches,\n        n = touches.length, i, t;\n\n    nopropagation(event);\n    if (touchending) clearTimeout(touchending);\n    touchending = setTimeout(function() { touchending = null; }, touchDelay);\n    for (i = 0; i < n; ++i) {\n      t = touches[i];\n      if (g.touch0 && g.touch0[2] === t.identifier) delete g.touch0;\n      else if (g.touch1 && g.touch1[2] === t.identifier) delete g.touch1;\n    }\n    if (g.touch1 && !g.touch0) g.touch0 = g.touch1, delete g.touch1;\n    if (g.touch0) g.touch0[1] = this.__zoom.invert(g.touch0[0]);\n    else {\n      g.end();\n      // If this was a dbltap, reroute to the (optional) dblclick.zoom handler.\n      if (g.taps === 2) {\n        t = pointer(t, this);\n        if (Math.hypot(touchfirst[0] - t[0], touchfirst[1] - t[1]) < tapDistance) {\n          var p = select(this).on(\"dblclick.zoom\");\n          if (p) p.apply(this, arguments);\n        }\n      }\n    }\n  }\n\n  zoom.wheelDelta = function(_) {\n    return arguments.length ? (wheelDelta = typeof _ === \"function\" ? _ : constant(+_), zoom) : wheelDelta;\n  };\n\n  zoom.filter = function(_) {\n    return arguments.length ? (filter = typeof _ === \"function\" ? _ : constant(!!_), zoom) : filter;\n  };\n\n  zoom.touchable = function(_) {\n    return arguments.length ? (touchable = typeof _ === \"function\" ? _ : constant(!!_), zoom) : touchable;\n  };\n\n  zoom.extent = function(_) {\n    return arguments.length ? (extent = typeof _ === \"function\" ? _ : constant([[+_[0][0], +_[0][1]], [+_[1][0], +_[1][1]]]), zoom) : extent;\n  };\n\n  zoom.scaleExtent = function(_) {\n    return arguments.length ? (scaleExtent[0] = +_[0], scaleExtent[1] = +_[1], zoom) : [scaleExtent[0], scaleExtent[1]];\n  };\n\n  zoom.translateExtent = function(_) {\n    return arguments.length ? (translateExtent[0][0] = +_[0][0], translateExtent[1][0] = +_[1][0], translateExtent[0][1] = +_[0][1], translateExtent[1][1] = +_[1][1], zoom) : [[translateExtent[0][0], translateExtent[0][1]], [translateExtent[1][0], translateExtent[1][1]]];\n  };\n\n  zoom.constrain = function(_) {\n    return arguments.length ? (constrain = _, zoom) : constrain;\n  };\n\n  zoom.duration = function(_) {\n    return arguments.length ? (duration = +_, zoom) : duration;\n  };\n\n  zoom.interpolate = function(_) {\n    return arguments.length ? (interpolate = _, zoom) : interpolate;\n  };\n\n  zoom.on = function() {\n    var value = listeners.on.apply(listeners, arguments);\n    return value === listeners ? zoom : value;\n  };\n\n  zoom.clickDistance = function(_) {\n    return arguments.length ? (clickDistance2 = (_ = +_) * _, zoom) : Math.sqrt(clickDistance2);\n  };\n\n  zoom.tapDistance = function(_) {\n    return arguments.length ? (tapDistance = +_, zoom) : tapDistance;\n  };\n\n  return zoom;\n}\n"], "mappings": ";;;;;;;;;;;AAAA,IAAI,OAAO,EAAC,OAAO,MAAM;AAAC,EAAC;AAE3B,SAAS,WAAW;AAClB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,GAAG;AAC3D,QAAI,EAAE,IAAI,UAAU,CAAC,IAAI,OAAQ,KAAK,KAAM,QAAQ,KAAK,CAAC,EAAG,OAAM,IAAI,MAAM,mBAAmB,CAAC;AACjG,MAAE,CAAC,IAAI,CAAC;AAAA,EACV;AACA,SAAO,IAAI,SAAS,CAAC;AACvB;AAEA,SAAS,SAAS,GAAG;AACnB,OAAK,IAAI;AACX;AAEA,SAAS,eAAe,WAAW,OAAO;AACxC,SAAO,UAAU,KAAK,EAAE,MAAM,OAAO,EAAE,IAAI,SAAS,GAAG;AACrD,QAAI,OAAO,IAAI,IAAI,EAAE,QAAQ,GAAG;AAChC,QAAI,KAAK,EAAG,QAAO,EAAE,MAAM,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,GAAG,CAAC;AACnD,QAAI,KAAK,CAAC,MAAM,eAAe,CAAC,EAAG,OAAM,IAAI,MAAM,mBAAmB,CAAC;AACvE,WAAO,EAAC,MAAM,GAAG,KAAU;AAAA,EAC7B,CAAC;AACH;AAEA,SAAS,YAAY,SAAS,YAAY;AAAA,EACxC,aAAa;AAAA,EACb,IAAI,SAAS,UAAU,UAAU;AAC/B,QAAI,IAAI,KAAK,GACT,IAAI,eAAe,WAAW,IAAI,CAAC,GACnC,GACA,IAAI,IACJ,IAAI,EAAE;AAGV,QAAI,UAAU,SAAS,GAAG;AACxB,aAAO,EAAE,IAAI,EAAG,MAAK,KAAK,WAAW,EAAE,CAAC,GAAG,UAAU,IAAI,IAAI,EAAE,CAAC,GAAG,SAAS,IAAI,GAAI,QAAO;AAC3F;AAAA,IACF;AAIA,QAAI,YAAY,QAAQ,OAAO,aAAa,WAAY,OAAM,IAAI,MAAM,uBAAuB,QAAQ;AACvG,WAAO,EAAE,IAAI,GAAG;AACd,UAAI,KAAK,WAAW,EAAE,CAAC,GAAG,KAAM,GAAE,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,SAAS,MAAM,QAAQ;AAAA,eAC/D,YAAY,KAAM,MAAK,KAAK,EAAG,GAAE,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,SAAS,MAAM,IAAI;AAAA,IAC9E;AAEA,WAAO;AAAA,EACT;AAAA,EACA,MAAM,WAAW;AACf,QAAI,OAAO,CAAC,GAAG,IAAI,KAAK;AACxB,aAAS,KAAK,EAAG,MAAK,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM;AACtC,WAAO,IAAI,SAAS,IAAI;AAAA,EAC1B;AAAA,EACA,MAAM,SAAS,MAAM,MAAM;AACzB,SAAK,IAAI,UAAU,SAAS,KAAK,EAAG,UAAS,OAAO,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,EAAE,EAAG,MAAK,CAAC,IAAI,UAAU,IAAI,CAAC;AACpH,QAAI,CAAC,KAAK,EAAE,eAAe,IAAI,EAAG,OAAM,IAAI,MAAM,mBAAmB,IAAI;AACzE,SAAK,IAAI,KAAK,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAI,GAAG,EAAE,EAAG,GAAE,CAAC,EAAE,MAAM,MAAM,MAAM,IAAI;AAAA,EACrF;AAAA,EACA,OAAO,SAAS,MAAM,MAAM,MAAM;AAChC,QAAI,CAAC,KAAK,EAAE,eAAe,IAAI,EAAG,OAAM,IAAI,MAAM,mBAAmB,IAAI;AACzE,aAAS,IAAI,KAAK,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAI,GAAG,EAAE,EAAG,GAAE,CAAC,EAAE,MAAM,MAAM,MAAM,IAAI;AAAA,EACzF;AACF;AAEA,SAAS,IAAI,MAAM,MAAM;AACvB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9C,SAAK,IAAI,KAAK,CAAC,GAAG,SAAS,MAAM;AAC/B,aAAO,EAAE;AAAA,IACX;AAAA,EACF;AACF;AAEA,SAAS,IAAI,MAAM,MAAM,UAAU;AACjC,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC3C,QAAI,KAAK,CAAC,EAAE,SAAS,MAAM;AACzB,WAAK,CAAC,IAAI,MAAM,OAAO,KAAK,MAAM,GAAG,CAAC,EAAE,OAAO,KAAK,MAAM,IAAI,CAAC,CAAC;AAChE;AAAA,IACF;AAAA,EACF;AACA,MAAI,YAAY,KAAM,MAAK,KAAK,EAAC,MAAY,OAAO,SAAQ,CAAC;AAC7D,SAAO;AACT;AAEA,IAAO,mBAAQ;;;ACnFR,IAAI,QAAQ;AAEnB,IAAO,qBAAQ;AAAA,EACb,KAAK;AAAA,EACL;AAAA,EACA,OAAO;AAAA,EACP,KAAK;AAAA,EACL,OAAO;AACT;;;ACNe,SAAR,kBAAiB,MAAM;AAC5B,MAAI,SAAS,QAAQ,IAAI,IAAI,OAAO,QAAQ,GAAG;AAC/C,MAAI,KAAK,MAAM,SAAS,KAAK,MAAM,GAAG,CAAC,OAAO,QAAS,QAAO,KAAK,MAAM,IAAI,CAAC;AAC9E,SAAO,mBAAW,eAAe,MAAM,IAAI,EAAC,OAAO,mBAAW,MAAM,GAAG,OAAO,KAAI,IAAI;AACxF;;;ACHA,SAAS,eAAe,MAAM;AAC5B,SAAO,WAAW;AAChB,QAAIA,YAAW,KAAK,eAChB,MAAM,KAAK;AACf,WAAO,QAAQ,SAASA,UAAS,gBAAgB,iBAAiB,QAC5DA,UAAS,cAAc,IAAI,IAC3BA,UAAS,gBAAgB,KAAK,IAAI;AAAA,EAC1C;AACF;AAEA,SAAS,aAAa,UAAU;AAC9B,SAAO,WAAW;AAChB,WAAO,KAAK,cAAc,gBAAgB,SAAS,OAAO,SAAS,KAAK;AAAA,EAC1E;AACF;AAEe,SAAR,gBAAiB,MAAM;AAC5B,MAAI,WAAW,kBAAU,IAAI;AAC7B,UAAQ,SAAS,QACX,eACA,gBAAgB,QAAQ;AAChC;;;ACxBA,SAAS,OAAO;AAAC;AAEF,SAAR,iBAAiB,UAAU;AAChC,SAAO,YAAY,OAAO,OAAO,WAAW;AAC1C,WAAO,KAAK,cAAc,QAAQ;AAAA,EACpC;AACF;;;ACNA,SAAS,QAAQ;AACf,SAAO,CAAC;AACV;AAEe,SAAR,oBAAiB,UAAU;AAChC,SAAO,YAAY,OAAO,QAAQ,WAAW;AAC3C,WAAO,KAAK,iBAAiB,QAAQ;AAAA,EACvC;AACF;;;ACRe,SAAR,gBAAiB,UAAU;AAChC,SAAO,WAAW;AAChB,WAAO,KAAK,QAAQ,QAAQ;AAAA,EAC9B;AACF;AAEO,SAAS,aAAa,UAAU;AACrC,SAAO,SAAS,MAAM;AACpB,WAAO,KAAK,QAAQ,QAAQ;AAAA,EAC9B;AACF;;;ACVe,SAAR,eAAiB,MAAM;AAC5B,SAAQ,KAAK,iBAAiB,KAAK,cAAc,eACzC,KAAK,YAAY,QAClB,KAAK;AACd;;;ACFA,SAAS,YAAY,MAAM;AACzB,SAAO,WAAW;AAChB,SAAK,MAAM,eAAe,IAAI;AAAA,EAChC;AACF;AAEA,SAAS,cAAc,MAAM,OAAO,UAAU;AAC5C,SAAO,WAAW;AAChB,SAAK,MAAM,YAAY,MAAM,OAAO,QAAQ;AAAA,EAC9C;AACF;AAEA,SAAS,cAAc,MAAM,OAAO,UAAU;AAC5C,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,KAAK,KAAM,MAAK,MAAM,eAAe,IAAI;AAAA,QACxC,MAAK,MAAM,YAAY,MAAM,GAAG,QAAQ;AAAA,EAC/C;AACF;AAEe,SAAR,cAAiB,MAAM,OAAO,UAAU;AAC7C,SAAO,UAAU,SAAS,IACpB,KAAK,MAAM,SAAS,OACd,cAAc,OAAO,UAAU,aAC/B,gBACA,eAAe,MAAM,OAAO,YAAY,OAAO,KAAK,QAAQ,CAAC,IACnE,WAAW,KAAK,KAAK,GAAG,IAAI;AACpC;AAEO,SAAS,WAAW,MAAM,MAAM;AACrC,SAAO,KAAK,MAAM,iBAAiB,IAAI,KAChC,eAAY,IAAI,EAAE,iBAAiB,MAAM,IAAI,EAAE,iBAAiB,IAAI;AAC7E;;;AC/Be,SAAR,eAAiB,QAAQ;AAC9B,MAAI,OAAO,WAAW,WAAY,UAAS,iBAAS,MAAM;AAE1D,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9F,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,WAAW,UAAU,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,SAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtH,WAAK,OAAO,MAAM,CAAC,OAAO,UAAU,OAAO,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,IAAI;AAC/E,YAAI,cAAc,KAAM,SAAQ,WAAW,KAAK;AAChD,iBAAS,CAAC,IAAI;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,UAAU,WAAW,KAAK,QAAQ;AAC/C;;;ACVe,SAAR,MAAuB,GAAG;AAC/B,SAAO,KAAK,OAAO,CAAC,IAAI,MAAM,QAAQ,CAAC,IAAI,IAAI,MAAM,KAAK,CAAC;AAC7D;;;ACJA,SAAS,SAAS,QAAQ;AACxB,SAAO,WAAW;AAChB,WAAO,MAAM,OAAO,MAAM,MAAM,SAAS,CAAC;AAAA,EAC5C;AACF;AAEe,SAAR,kBAAiB,QAAQ;AAC9B,MAAI,OAAO,WAAW,WAAY,UAAS,SAAS,MAAM;AAAA,MACrD,UAAS,oBAAY,MAAM;AAEhC,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,CAAC,GAAG,UAAU,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAClG,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,kBAAU,KAAK,OAAO,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,CAAC;AACzD,gBAAQ,KAAK,IAAI;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,UAAU,WAAW,OAAO;AACzC;;;ACtBA,IAAI,OAAO,MAAM,UAAU;AAE3B,SAAS,UAAU,OAAO;AACxB,SAAO,WAAW;AAChB,WAAO,KAAK,KAAK,KAAK,UAAU,KAAK;AAAA,EACvC;AACF;AAEA,SAAS,aAAa;AACpB,SAAO,KAAK;AACd;AAEe,SAAR,oBAAiB,OAAO;AAC7B,SAAO,KAAK,OAAO,SAAS,OAAO,aAC7B,UAAU,OAAO,UAAU,aAAa,QAAQ,aAAa,KAAK,CAAC,CAAC;AAC5E;;;ACfA,IAAI,SAAS,MAAM,UAAU;AAE7B,SAAS,WAAW;AAClB,SAAO,MAAM,KAAK,KAAK,QAAQ;AACjC;AAEA,SAAS,eAAe,OAAO;AAC7B,SAAO,WAAW;AAChB,WAAO,OAAO,KAAK,KAAK,UAAU,KAAK;AAAA,EACzC;AACF;AAEe,SAAR,uBAAiB,OAAO;AAC7B,SAAO,KAAK,UAAU,SAAS,OAAO,WAChC,eAAe,OAAO,UAAU,aAAa,QAAQ,aAAa,KAAK,CAAC,CAAC;AACjF;;;ACde,SAAR,eAAiB,OAAO;AAC7B,MAAI,OAAO,UAAU,WAAY,SAAQ,gBAAQ,KAAK;AAEtD,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9F,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,WAAW,UAAU,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACnG,WAAK,OAAO,MAAM,CAAC,MAAM,MAAM,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,GAAG;AAClE,iBAAS,KAAK,IAAI;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,UAAU,WAAW,KAAK,QAAQ;AAC/C;;;ACfe,SAAR,eAAiB,QAAQ;AAC9B,SAAO,IAAI,MAAM,OAAO,MAAM;AAChC;;;ACCe,SAAR,gBAAmB;AACxB,SAAO,IAAI,UAAU,KAAK,UAAU,KAAK,QAAQ,IAAI,cAAM,GAAG,KAAK,QAAQ;AAC7E;AAEO,SAAS,UAAU,QAAQC,QAAO;AACvC,OAAK,gBAAgB,OAAO;AAC5B,OAAK,eAAe,OAAO;AAC3B,OAAK,QAAQ;AACb,OAAK,UAAU;AACf,OAAK,WAAWA;AAClB;AAEA,UAAU,YAAY;AAAA,EACpB,aAAa;AAAA,EACb,aAAa,SAAS,OAAO;AAAE,WAAO,KAAK,QAAQ,aAAa,OAAO,KAAK,KAAK;AAAA,EAAG;AAAA,EACpF,cAAc,SAAS,OAAO,MAAM;AAAE,WAAO,KAAK,QAAQ,aAAa,OAAO,IAAI;AAAA,EAAG;AAAA,EACrF,eAAe,SAAS,UAAU;AAAE,WAAO,KAAK,QAAQ,cAAc,QAAQ;AAAA,EAAG;AAAA,EACjF,kBAAkB,SAAS,UAAU;AAAE,WAAO,KAAK,QAAQ,iBAAiB,QAAQ;AAAA,EAAG;AACzF;;;ACrBe,SAAR,iBAAiB,GAAG;AACzB,SAAO,WAAW;AAChB,WAAO;AAAA,EACT;AACF;;;ACAA,SAAS,UAAU,QAAQ,OAAO,OAAO,QAAQ,MAAM,MAAM;AAC3D,MAAI,IAAI,GACJ,MACA,cAAc,MAAM,QACpB,aAAa,KAAK;AAKtB,SAAO,IAAI,YAAY,EAAE,GAAG;AAC1B,QAAI,OAAO,MAAM,CAAC,GAAG;AACnB,WAAK,WAAW,KAAK,CAAC;AACtB,aAAO,CAAC,IAAI;AAAA,IACd,OAAO;AACL,YAAM,CAAC,IAAI,IAAI,UAAU,QAAQ,KAAK,CAAC,CAAC;AAAA,IAC1C;AAAA,EACF;AAGA,SAAO,IAAI,aAAa,EAAE,GAAG;AAC3B,QAAI,OAAO,MAAM,CAAC,GAAG;AACnB,WAAK,CAAC,IAAI;AAAA,IACZ;AAAA,EACF;AACF;AAEA,SAAS,QAAQ,QAAQ,OAAO,OAAO,QAAQ,MAAM,MAAM,KAAK;AAC9D,MAAI,GACA,MACA,iBAAiB,oBAAI,OACrB,cAAc,MAAM,QACpB,aAAa,KAAK,QAClB,YAAY,IAAI,MAAM,WAAW,GACjC;AAIJ,OAAK,IAAI,GAAG,IAAI,aAAa,EAAE,GAAG;AAChC,QAAI,OAAO,MAAM,CAAC,GAAG;AACnB,gBAAU,CAAC,IAAI,WAAW,IAAI,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,IAAI;AACpE,UAAI,eAAe,IAAI,QAAQ,GAAG;AAChC,aAAK,CAAC,IAAI;AAAA,MACZ,OAAO;AACL,uBAAe,IAAI,UAAU,IAAI;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAKA,OAAK,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AAC/B,eAAW,IAAI,KAAK,QAAQ,KAAK,CAAC,GAAG,GAAG,IAAI,IAAI;AAChD,QAAI,OAAO,eAAe,IAAI,QAAQ,GAAG;AACvC,aAAO,CAAC,IAAI;AACZ,WAAK,WAAW,KAAK,CAAC;AACtB,qBAAe,OAAO,QAAQ;AAAA,IAChC,OAAO;AACL,YAAM,CAAC,IAAI,IAAI,UAAU,QAAQ,KAAK,CAAC,CAAC;AAAA,IAC1C;AAAA,EACF;AAGA,OAAK,IAAI,GAAG,IAAI,aAAa,EAAE,GAAG;AAChC,SAAK,OAAO,MAAM,CAAC,MAAO,eAAe,IAAI,UAAU,CAAC,CAAC,MAAM,MAAO;AACpE,WAAK,CAAC,IAAI;AAAA,IACZ;AAAA,EACF;AACF;AAEA,SAAS,MAAM,MAAM;AACnB,SAAO,KAAK;AACd;AAEe,SAAR,aAAiB,OAAO,KAAK;AAClC,MAAI,CAAC,UAAU,OAAQ,QAAO,MAAM,KAAK,MAAM,KAAK;AAEpD,MAAI,OAAO,MAAM,UAAU,WACvB,UAAU,KAAK,UACf,SAAS,KAAK;AAElB,MAAI,OAAO,UAAU,WAAY,SAAQ,iBAAS,KAAK;AAEvD,WAAS,IAAI,OAAO,QAAQ,SAAS,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,MAAM,CAAC,GAAG,OAAO,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/G,QAAI,SAAS,QAAQ,CAAC,GAClB,QAAQ,OAAO,CAAC,GAChB,cAAc,MAAM,QACpB,OAAO,UAAU,MAAM,KAAK,QAAQ,UAAU,OAAO,UAAU,GAAG,OAAO,CAAC,GAC1E,aAAa,KAAK,QAClB,aAAa,MAAM,CAAC,IAAI,IAAI,MAAM,UAAU,GAC5C,cAAc,OAAO,CAAC,IAAI,IAAI,MAAM,UAAU,GAC9C,YAAY,KAAK,CAAC,IAAI,IAAI,MAAM,WAAW;AAE/C,SAAK,QAAQ,OAAO,YAAY,aAAa,WAAW,MAAM,GAAG;AAKjE,aAAS,KAAK,GAAG,KAAK,GAAG,UAAU,MAAM,KAAK,YAAY,EAAE,IAAI;AAC9D,UAAI,WAAW,WAAW,EAAE,GAAG;AAC7B,YAAI,MAAM,GAAI,MAAK,KAAK;AACxB,eAAO,EAAE,OAAO,YAAY,EAAE,MAAM,EAAE,KAAK,WAAW;AACtD,iBAAS,QAAQ,QAAQ;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAEA,WAAS,IAAI,UAAU,QAAQ,OAAO;AACtC,SAAO,SAAS;AAChB,SAAO,QAAQ;AACf,SAAO;AACT;AAQA,SAAS,UAAU,MAAM;AACvB,SAAO,OAAO,SAAS,YAAY,YAAY,OAC3C,OACA,MAAM,KAAK,IAAI;AACrB;;;AC5He,SAAR,eAAmB;AACxB,SAAO,IAAI,UAAU,KAAK,SAAS,KAAK,QAAQ,IAAI,cAAM,GAAG,KAAK,QAAQ;AAC5E;;;ACLe,SAAR,aAAiB,SAAS,UAAU,QAAQ;AACjD,MAAI,QAAQ,KAAK,MAAM,GAAG,SAAS,MAAM,OAAO,KAAK,KAAK;AAC1D,MAAI,OAAO,YAAY,YAAY;AACjC,YAAQ,QAAQ,KAAK;AACrB,QAAI,MAAO,SAAQ,MAAM,UAAU;AAAA,EACrC,OAAO;AACL,YAAQ,MAAM,OAAO,UAAU,EAAE;AAAA,EACnC;AACA,MAAI,YAAY,MAAM;AACpB,aAAS,SAAS,MAAM;AACxB,QAAI,OAAQ,UAAS,OAAO,UAAU;AAAA,EACxC;AACA,MAAI,UAAU,KAAM,MAAK,OAAO;AAAA,MAAQ,QAAO,IAAI;AACnD,SAAO,SAAS,SAAS,MAAM,MAAM,MAAM,EAAE,MAAM,IAAI;AACzD;;;ACZe,SAAR,cAAiB,SAAS;AAC/B,MAAIC,aAAY,QAAQ,YAAY,QAAQ,UAAU,IAAI;AAE1D,WAAS,UAAU,KAAK,SAAS,UAAUA,WAAU,SAAS,KAAK,QAAQ,QAAQ,KAAK,QAAQ,QAAQ,IAAI,KAAK,IAAI,IAAI,EAAE,GAAG,SAAS,IAAI,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACvK,aAAS,SAAS,QAAQ,CAAC,GAAG,SAAS,QAAQ,CAAC,GAAG,IAAI,OAAO,QAAQ,QAAQ,OAAO,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/H,UAAI,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,GAAG;AACjC,cAAM,CAAC,IAAI;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,IAAI,EAAE,GAAG;AAClB,WAAO,CAAC,IAAI,QAAQ,CAAC;AAAA,EACvB;AAEA,SAAO,IAAI,UAAU,QAAQ,KAAK,QAAQ;AAC5C;;;AClBe,SAAR,gBAAmB;AAExB,WAAS,SAAS,KAAK,SAAS,IAAI,IAAI,IAAI,OAAO,QAAQ,EAAE,IAAI,KAAI;AACnE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,SAAS,GAAG,OAAO,MAAM,CAAC,GAAG,MAAM,EAAE,KAAK,KAAI;AAClF,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,YAAI,QAAQ,KAAK,wBAAwB,IAAI,IAAI,EAAG,MAAK,WAAW,aAAa,MAAM,IAAI;AAC3F,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;ACVe,SAAR,aAAiB,SAAS;AAC/B,MAAI,CAAC,QAAS,WAAU;AAExB,WAAS,YAAY,GAAG,GAAG;AACzB,WAAO,KAAK,IAAI,QAAQ,EAAE,UAAU,EAAE,QAAQ,IAAI,CAAC,IAAI,CAAC;AAAA,EAC1D;AAEA,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,aAAa,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/F,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,YAAY,WAAW,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/G,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,kBAAU,CAAC,IAAI;AAAA,MACjB;AAAA,IACF;AACA,cAAU,KAAK,WAAW;AAAA,EAC5B;AAEA,SAAO,IAAI,UAAU,YAAY,KAAK,QAAQ,EAAE,MAAM;AACxD;AAEA,SAAS,UAAU,GAAG,GAAG;AACvB,SAAO,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AAC/C;;;ACvBe,SAAR,eAAmB;AACxB,MAAI,WAAW,UAAU,CAAC;AAC1B,YAAU,CAAC,IAAI;AACf,WAAS,MAAM,MAAM,SAAS;AAC9B,SAAO;AACT;;;ACLe,SAAR,gBAAmB;AACxB,SAAO,MAAM,KAAK,IAAI;AACxB;;;ACFe,SAAR,eAAmB;AAExB,WAAS,SAAS,KAAK,SAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC/D,UAAI,OAAO,MAAM,CAAC;AAClB,UAAI,KAAM,QAAO;AAAA,IACnB;AAAA,EACF;AAEA,SAAO;AACT;;;ACVe,SAAR,eAAmB;AACxB,MAAI,OAAO;AACX,aAAW,QAAQ,KAAM,GAAE;AAC3B,SAAO;AACT;;;ACJe,SAAR,gBAAmB;AACxB,SAAO,CAAC,KAAK,KAAK;AACpB;;;ACFe,SAAR,aAAiB,UAAU;AAEhC,WAAS,SAAS,KAAK,SAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC,EAAG,UAAS,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK;AAAA,IAClE;AAAA,EACF;AAEA,SAAO;AACT;;;ACPA,SAAS,WAAW,MAAM;AACxB,SAAO,WAAW;AAChB,SAAK,gBAAgB,IAAI;AAAA,EAC3B;AACF;AAEA,SAAS,aAAa,UAAU;AAC9B,SAAO,WAAW;AAChB,SAAK,kBAAkB,SAAS,OAAO,SAAS,KAAK;AAAA,EACvD;AACF;AAEA,SAAS,aAAa,MAAM,OAAO;AACjC,SAAO,WAAW;AAChB,SAAK,aAAa,MAAM,KAAK;AAAA,EAC/B;AACF;AAEA,SAAS,eAAe,UAAU,OAAO;AACvC,SAAO,WAAW;AAChB,SAAK,eAAe,SAAS,OAAO,SAAS,OAAO,KAAK;AAAA,EAC3D;AACF;AAEA,SAAS,aAAa,MAAM,OAAO;AACjC,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,KAAK,KAAM,MAAK,gBAAgB,IAAI;AAAA,QACnC,MAAK,aAAa,MAAM,CAAC;AAAA,EAChC;AACF;AAEA,SAAS,eAAe,UAAU,OAAO;AACvC,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,KAAK,KAAM,MAAK,kBAAkB,SAAS,OAAO,SAAS,KAAK;AAAA,QAC/D,MAAK,eAAe,SAAS,OAAO,SAAS,OAAO,CAAC;AAAA,EAC5D;AACF;AAEe,SAAR,aAAiB,MAAM,OAAO;AACnC,MAAI,WAAW,kBAAU,IAAI;AAE7B,MAAI,UAAU,SAAS,GAAG;AACxB,QAAI,OAAO,KAAK,KAAK;AACrB,WAAO,SAAS,QACV,KAAK,eAAe,SAAS,OAAO,SAAS,KAAK,IAClD,KAAK,aAAa,QAAQ;AAAA,EAClC;AAEA,SAAO,KAAK,MAAM,SAAS,OACpB,SAAS,QAAQ,eAAe,aAAe,OAAO,UAAU,aAChE,SAAS,QAAQ,iBAAiB,eAClC,SAAS,QAAQ,iBAAiB,cAAgB,UAAU,KAAK,CAAC;AAC3E;;;ACxDA,SAAS,eAAe,MAAM;AAC5B,SAAO,WAAW;AAChB,WAAO,KAAK,IAAI;AAAA,EAClB;AACF;AAEA,SAAS,iBAAiB,MAAM,OAAO;AACrC,SAAO,WAAW;AAChB,SAAK,IAAI,IAAI;AAAA,EACf;AACF;AAEA,SAAS,iBAAiB,MAAM,OAAO;AACrC,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,KAAK,KAAM,QAAO,KAAK,IAAI;AAAA,QAC1B,MAAK,IAAI,IAAI;AAAA,EACpB;AACF;AAEe,SAAR,iBAAiB,MAAM,OAAO;AACnC,SAAO,UAAU,SAAS,IACpB,KAAK,MAAM,SAAS,OAChB,iBAAiB,OAAO,UAAU,aAClC,mBACA,kBAAkB,MAAM,KAAK,CAAC,IAClC,KAAK,KAAK,EAAE,IAAI;AACxB;;;AC3BA,SAAS,WAAW,QAAQ;AAC1B,SAAO,OAAO,KAAK,EAAE,MAAM,OAAO;AACpC;AAEA,SAAS,UAAU,MAAM;AACvB,SAAO,KAAK,aAAa,IAAI,UAAU,IAAI;AAC7C;AAEA,SAAS,UAAU,MAAM;AACvB,OAAK,QAAQ;AACb,OAAK,SAAS,WAAW,KAAK,aAAa,OAAO,KAAK,EAAE;AAC3D;AAEA,UAAU,YAAY;AAAA,EACpB,KAAK,SAAS,MAAM;AAClB,QAAI,IAAI,KAAK,OAAO,QAAQ,IAAI;AAChC,QAAI,IAAI,GAAG;AACT,WAAK,OAAO,KAAK,IAAI;AACrB,WAAK,MAAM,aAAa,SAAS,KAAK,OAAO,KAAK,GAAG,CAAC;AAAA,IACxD;AAAA,EACF;AAAA,EACA,QAAQ,SAAS,MAAM;AACrB,QAAI,IAAI,KAAK,OAAO,QAAQ,IAAI;AAChC,QAAI,KAAK,GAAG;AACV,WAAK,OAAO,OAAO,GAAG,CAAC;AACvB,WAAK,MAAM,aAAa,SAAS,KAAK,OAAO,KAAK,GAAG,CAAC;AAAA,IACxD;AAAA,EACF;AAAA,EACA,UAAU,SAAS,MAAM;AACvB,WAAO,KAAK,OAAO,QAAQ,IAAI,KAAK;AAAA,EACtC;AACF;AAEA,SAAS,WAAW,MAAM,OAAO;AAC/B,MAAI,OAAO,UAAU,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM;AAC9C,SAAO,EAAE,IAAI,EAAG,MAAK,IAAI,MAAM,CAAC,CAAC;AACnC;AAEA,SAAS,cAAc,MAAM,OAAO;AAClC,MAAI,OAAO,UAAU,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM;AAC9C,SAAO,EAAE,IAAI,EAAG,MAAK,OAAO,MAAM,CAAC,CAAC;AACtC;AAEA,SAAS,YAAY,OAAO;AAC1B,SAAO,WAAW;AAChB,eAAW,MAAM,KAAK;AAAA,EACxB;AACF;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,kBAAc,MAAM,KAAK;AAAA,EAC3B;AACF;AAEA,SAAS,gBAAgB,OAAO,OAAO;AACrC,SAAO,WAAW;AAChB,KAAC,MAAM,MAAM,MAAM,SAAS,IAAI,aAAa,eAAe,MAAM,KAAK;AAAA,EACzE;AACF;AAEe,SAAR,gBAAiB,MAAM,OAAO;AACnC,MAAI,QAAQ,WAAW,OAAO,EAAE;AAEhC,MAAI,UAAU,SAAS,GAAG;AACxB,QAAI,OAAO,UAAU,KAAK,KAAK,CAAC,GAAG,IAAI,IAAI,IAAI,MAAM;AACrD,WAAO,EAAE,IAAI,EAAG,KAAI,CAAC,KAAK,SAAS,MAAM,CAAC,CAAC,EAAG,QAAO;AACrD,WAAO;AAAA,EACT;AAEA,SAAO,KAAK,MAAM,OAAO,UAAU,aAC7B,kBAAkB,QAClB,cACA,cAAc,OAAO,KAAK,CAAC;AACnC;;;AC1EA,SAAS,aAAa;AACpB,OAAK,cAAc;AACrB;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,SAAK,cAAc;AAAA,EACrB;AACF;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,SAAK,cAAc,KAAK,OAAO,KAAK;AAAA,EACtC;AACF;AAEe,SAAR,aAAiB,OAAO;AAC7B,SAAO,UAAU,SACX,KAAK,KAAK,SAAS,OACf,cAAc,OAAO,UAAU,aAC/B,eACA,cAAc,KAAK,CAAC,IACxB,KAAK,KAAK,EAAE;AACpB;;;ACxBA,SAAS,aAAa;AACpB,OAAK,YAAY;AACnB;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,SAAK,YAAY;AAAA,EACnB;AACF;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,SAAK,YAAY,KAAK,OAAO,KAAK;AAAA,EACpC;AACF;AAEe,SAAR,aAAiB,OAAO;AAC7B,SAAO,UAAU,SACX,KAAK,KAAK,SAAS,OACf,cAAc,OAAO,UAAU,aAC/B,eACA,cAAc,KAAK,CAAC,IACxB,KAAK,KAAK,EAAE;AACpB;;;ACxBA,SAAS,QAAQ;AACf,MAAI,KAAK,YAAa,MAAK,WAAW,YAAY,IAAI;AACxD;AAEe,SAAR,gBAAmB;AACxB,SAAO,KAAK,KAAK,KAAK;AACxB;;;ACNA,SAAS,QAAQ;AACf,MAAI,KAAK,gBAAiB,MAAK,WAAW,aAAa,MAAM,KAAK,WAAW,UAAU;AACzF;AAEe,SAAR,gBAAmB;AACxB,SAAO,KAAK,KAAK,KAAK;AACxB;;;ACJe,SAAR,eAAiB,MAAM;AAC5B,MAAIC,UAAS,OAAO,SAAS,aAAa,OAAO,gBAAQ,IAAI;AAC7D,SAAO,KAAK,OAAO,WAAW;AAC5B,WAAO,KAAK,YAAYA,QAAO,MAAM,MAAM,SAAS,CAAC;AAAA,EACvD,CAAC;AACH;;;ACJA,SAAS,eAAe;AACtB,SAAO;AACT;AAEe,SAAR,eAAiB,MAAM,QAAQ;AACpC,MAAIC,UAAS,OAAO,SAAS,aAAa,OAAO,gBAAQ,IAAI,GACzD,SAAS,UAAU,OAAO,eAAe,OAAO,WAAW,aAAa,SAAS,iBAAS,MAAM;AACpG,SAAO,KAAK,OAAO,WAAW;AAC5B,WAAO,KAAK,aAAaA,QAAO,MAAM,MAAM,SAAS,GAAG,OAAO,MAAM,MAAM,SAAS,KAAK,IAAI;AAAA,EAC/F,CAAC;AACH;;;ACbA,SAAS,SAAS;AAChB,MAAI,SAAS,KAAK;AAClB,MAAI,OAAQ,QAAO,YAAY,IAAI;AACrC;AAEe,SAAR,iBAAmB;AACxB,SAAO,KAAK,KAAK,MAAM;AACzB;;;ACPA,SAAS,yBAAyB;AAChC,MAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,SAAS,KAAK;AACjD,SAAO,SAAS,OAAO,aAAa,OAAO,KAAK,WAAW,IAAI;AACjE;AAEA,SAAS,sBAAsB;AAC7B,MAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,SAAS,KAAK;AAChD,SAAO,SAAS,OAAO,aAAa,OAAO,KAAK,WAAW,IAAI;AACjE;AAEe,SAAR,cAAiB,MAAM;AAC5B,SAAO,KAAK,OAAO,OAAO,sBAAsB,sBAAsB;AACxE;;;ACZe,SAAR,cAAiB,OAAO;AAC7B,SAAO,UAAU,SACX,KAAK,SAAS,YAAY,KAAK,IAC/B,KAAK,KAAK,EAAE;AACpB;;;ACJA,SAAS,gBAAgB,UAAU;AACjC,SAAO,SAAS,OAAO;AACrB,aAAS,KAAK,MAAM,OAAO,KAAK,QAAQ;AAAA,EAC1C;AACF;AAEA,SAASC,gBAAe,WAAW;AACjC,SAAO,UAAU,KAAK,EAAE,MAAM,OAAO,EAAE,IAAI,SAAS,GAAG;AACrD,QAAI,OAAO,IAAI,IAAI,EAAE,QAAQ,GAAG;AAChC,QAAI,KAAK,EAAG,QAAO,EAAE,MAAM,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,GAAG,CAAC;AACnD,WAAO,EAAC,MAAM,GAAG,KAAU;AAAA,EAC7B,CAAC;AACH;AAEA,SAAS,SAAS,UAAU;AAC1B,SAAO,WAAW;AAChB,QAAI,KAAK,KAAK;AACd,QAAI,CAAC,GAAI;AACT,aAAS,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,QAAQ,GAAG,IAAI,GAAG,EAAE,GAAG;AACpD,UAAI,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,QAAQ,EAAE,SAAS,SAAS,SAAS,EAAE,SAAS,SAAS,MAAM;AACvF,aAAK,oBAAoB,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO;AAAA,MACxD,OAAO;AACL,WAAG,EAAE,CAAC,IAAI;AAAA,MACZ;AAAA,IACF;AACA,QAAI,EAAE,EAAG,IAAG,SAAS;AAAA,QAChB,QAAO,KAAK;AAAA,EACnB;AACF;AAEA,SAAS,MAAM,UAAU,OAAO,SAAS;AACvC,SAAO,WAAW;AAChB,QAAI,KAAK,KAAK,MAAM,GAAG,WAAW,gBAAgB,KAAK;AACvD,QAAI,GAAI,UAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,EAAE,GAAG;AACjD,WAAK,IAAI,GAAG,CAAC,GAAG,SAAS,SAAS,QAAQ,EAAE,SAAS,SAAS,MAAM;AAClE,aAAK,oBAAoB,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO;AACtD,aAAK,iBAAiB,EAAE,MAAM,EAAE,WAAW,UAAU,EAAE,UAAU,OAAO;AACxE,UAAE,QAAQ;AACV;AAAA,MACF;AAAA,IACF;AACA,SAAK,iBAAiB,SAAS,MAAM,UAAU,OAAO;AACtD,QAAI,EAAC,MAAM,SAAS,MAAM,MAAM,SAAS,MAAM,OAAc,UAAoB,QAAgB;AACjG,QAAI,CAAC,GAAI,MAAK,OAAO,CAAC,CAAC;AAAA,QAClB,IAAG,KAAK,CAAC;AAAA,EAChB;AACF;AAEe,SAAR,WAAiB,UAAU,OAAO,SAAS;AAChD,MAAI,YAAYA,gBAAe,WAAW,EAAE,GAAG,GAAG,IAAI,UAAU,QAAQ;AAExE,MAAI,UAAU,SAAS,GAAG;AACxB,QAAI,KAAK,KAAK,KAAK,EAAE;AACrB,QAAI,GAAI,UAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,GAAG,IAAI,GAAG,EAAE,GAAG;AACpD,WAAK,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG;AACjC,aAAK,IAAI,UAAU,CAAC,GAAG,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM;AAC3D,iBAAO,EAAE;AAAA,QACX;AAAA,MACF;AAAA,IACF;AACA;AAAA,EACF;AAEA,OAAK,QAAQ,QAAQ;AACrB,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,MAAK,KAAK,GAAG,UAAU,CAAC,GAAG,OAAO,OAAO,CAAC;AAClE,SAAO;AACT;;;AChEA,SAAS,cAAc,MAAM,MAAM,QAAQ;AACzC,MAAIC,UAAS,eAAY,IAAI,GACzB,QAAQA,QAAO;AAEnB,MAAI,OAAO,UAAU,YAAY;AAC/B,YAAQ,IAAI,MAAM,MAAM,MAAM;AAAA,EAChC,OAAO;AACL,YAAQA,QAAO,SAAS,YAAY,OAAO;AAC3C,QAAI,OAAQ,OAAM,UAAU,MAAM,OAAO,SAAS,OAAO,UAAU,GAAG,MAAM,SAAS,OAAO;AAAA,QACvF,OAAM,UAAU,MAAM,OAAO,KAAK;AAAA,EACzC;AAEA,OAAK,cAAc,KAAK;AAC1B;AAEA,SAAS,iBAAiB,MAAM,QAAQ;AACtC,SAAO,WAAW;AAChB,WAAO,cAAc,MAAM,MAAM,MAAM;AAAA,EACzC;AACF;AAEA,SAAS,iBAAiB,MAAM,QAAQ;AACtC,SAAO,WAAW;AAChB,WAAO,cAAc,MAAM,MAAM,OAAO,MAAM,MAAM,SAAS,CAAC;AAAA,EAChE;AACF;AAEe,SAARC,kBAAiB,MAAM,QAAQ;AACpC,SAAO,KAAK,MAAM,OAAO,WAAW,aAC9B,mBACA,kBAAkB,MAAM,MAAM,CAAC;AACvC;;;ACjCe,UAAR,mBAAoB;AACzB,WAAS,SAAS,KAAK,SAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC,EAAG,OAAM;AAAA,IAC7B;AAAA,EACF;AACF;;;AC6BO,IAAI,OAAO,CAAC,IAAI;AAEhB,SAAS,UAAU,QAAQ,SAAS;AACzC,OAAK,UAAU;AACf,OAAK,WAAW;AAClB;AAEA,SAAS,YAAY;AACnB,SAAO,IAAI,UAAU,CAAC,CAAC,SAAS,eAAe,CAAC,GAAG,IAAI;AACzD;AAEA,SAAS,sBAAsB;AAC7B,SAAO;AACT;AAEA,UAAU,YAAY,UAAU,YAAY;AAAA,EAC1C,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,WAAW;AAAA,EACX,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,UAAUC;AAAA,EACV,CAAC,OAAO,QAAQ,GAAG;AACrB;AAEA,IAAO,oBAAQ;;;ACvFA,SAARC,gBAAiB,UAAU;AAChC,SAAO,OAAO,aAAa,WACrB,IAAI,UAAU,CAAC,CAAC,SAAS,cAAc,QAAQ,CAAC,CAAC,GAAG,CAAC,SAAS,eAAe,CAAC,IAC9E,IAAI,UAAU,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI;AACxC;;;ACHe,SAAR,eAAiB,MAAM;AAC5B,SAAOC,gBAAO,gBAAQ,IAAI,EAAE,KAAK,SAAS,eAAe,CAAC;AAC5D;;;ACLA,IAAI,SAAS;AAEE,SAAR,QAAyB;AAC9B,SAAO,IAAI;AACb;AAEA,SAAS,QAAQ;AACf,OAAK,IAAI,OAAO,EAAE,QAAQ,SAAS,EAAE;AACvC;AAEA,MAAM,YAAY,MAAM,YAAY;AAAA,EAClC,aAAa;AAAA,EACb,KAAK,SAAS,MAAM;AAClB,QAAIC,MAAK,KAAK;AACd,WAAO,EAAEA,OAAM,MAAO,KAAI,EAAE,OAAO,KAAK,YAAa;AACrD,WAAO,KAAKA,GAAE;AAAA,EAChB;AAAA,EACA,KAAK,SAAS,MAAM,OAAO;AACzB,WAAO,KAAK,KAAK,CAAC,IAAI;AAAA,EACxB;AAAA,EACA,QAAQ,SAAS,MAAM;AACrB,WAAO,KAAK,KAAK,QAAQ,OAAO,KAAK,KAAK,CAAC;AAAA,EAC7C;AAAA,EACA,UAAU,WAAW;AACnB,WAAO,KAAK;AAAA,EACd;AACF;;;AC1Be,SAAR,oBAAiB,OAAO;AAC7B,MAAI;AACJ,SAAO,cAAc,MAAM,YAAa,SAAQ;AAChD,SAAO;AACT;;;ACFe,SAAR,gBAAiB,OAAO,MAAM;AACnC,UAAQ,oBAAY,KAAK;AACzB,MAAI,SAAS,OAAW,QAAO,MAAM;AACrC,MAAI,MAAM;AACR,QAAI,MAAM,KAAK,mBAAmB;AAClC,QAAI,IAAI,gBAAgB;AACtB,UAAI,QAAQ,IAAI,eAAe;AAC/B,YAAM,IAAI,MAAM,SAAS,MAAM,IAAI,MAAM;AACzC,cAAQ,MAAM,gBAAgB,KAAK,aAAa,EAAE,QAAQ,CAAC;AAC3D,aAAO,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,IAC1B;AACA,QAAI,KAAK,uBAAuB;AAC9B,UAAI,OAAO,KAAK,sBAAsB;AACtC,aAAO,CAAC,MAAM,UAAU,KAAK,OAAO,KAAK,YAAY,MAAM,UAAU,KAAK,MAAM,KAAK,SAAS;AAAA,IAChG;AAAA,EACF;AACA,SAAO,CAAC,MAAM,OAAO,MAAM,KAAK;AAClC;;;AChBe,SAAR,iBAAiB,QAAQ,MAAM;AACpC,MAAI,OAAO,QAAQ;AACjB,aAAS,oBAAY,MAAM;AAC3B,QAAI,SAAS,OAAW,QAAO,OAAO;AACtC,aAAS,OAAO,WAAW,CAAC,MAAM;AAAA,EACpC;AACA,SAAO,MAAM,KAAK,QAAQ,WAAS,gBAAQ,OAAO,IAAI,CAAC;AACzD;;;ACPe,SAARC,mBAAiB,UAAU;AAChC,SAAO,OAAO,aAAa,WACrB,IAAI,UAAU,CAAC,SAAS,iBAAiB,QAAQ,CAAC,GAAG,CAAC,SAAS,eAAe,CAAC,IAC/E,IAAI,UAAU,CAAC,MAAM,QAAQ,CAAC,GAAG,IAAI;AAC7C;;;ACLO,IAAM,aAAa,EAAC,SAAS,MAAK;AAClC,IAAM,oBAAoB,EAAC,SAAS,MAAM,SAAS,MAAK;AAExD,SAAS,cAAc,OAAO;AACnC,QAAM,yBAAyB;AACjC;AAEe,SAAR,gBAAiB,OAAO;AAC7B,QAAM,eAAe;AACrB,QAAM,yBAAyB;AACjC;;;ACTe,SAAR,eAAiB,MAAM;AAC5B,MAAIC,QAAO,KAAK,SAAS,iBACrBC,aAAYC,gBAAO,IAAI,EAAE,GAAG,kBAAkB,iBAAS,iBAAiB;AAC5E,MAAI,mBAAmBF,OAAM;AAC3B,IAAAC,WAAU,GAAG,oBAAoB,iBAAS,iBAAiB;AAAA,EAC7D,OAAO;AACL,IAAAD,MAAK,aAAaA,MAAK,MAAM;AAC7B,IAAAA,MAAK,MAAM,gBAAgB;AAAA,EAC7B;AACF;AAEO,SAAS,QAAQ,MAAM,SAAS;AACrC,MAAIA,QAAO,KAAK,SAAS,iBACrBC,aAAYC,gBAAO,IAAI,EAAE,GAAG,kBAAkB,IAAI;AACtD,MAAI,SAAS;AACX,IAAAD,WAAU,GAAG,cAAc,iBAAS,iBAAiB;AACrD,eAAW,WAAW;AAAE,MAAAA,WAAU,GAAG,cAAc,IAAI;AAAA,IAAG,GAAG,CAAC;AAAA,EAChE;AACA,MAAI,mBAAmBD,OAAM;AAC3B,IAAAC,WAAU,GAAG,oBAAoB,IAAI;AAAA,EACvC,OAAO;AACL,IAAAD,MAAK,MAAM,gBAAgBA,MAAK;AAChC,WAAOA,MAAK;AAAA,EACd;AACF;;;AC3BA,IAAOG,oBAAQ,OAAK,MAAM;;;ACAX,SAAR,UAA2B,MAAM;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAAG;AAAA,EAAG;AAAA,EAAI;AAAA,EACV,UAAAC;AACF,GAAG;AACD,SAAO,iBAAiB,MAAM;AAAA,IAC5B,MAAM,EAAC,OAAO,MAAM,YAAY,MAAM,cAAc,KAAI;AAAA,IACxD,aAAa,EAAC,OAAO,aAAa,YAAY,MAAM,cAAc,KAAI;AAAA,IACtE,SAAS,EAAC,OAAO,SAAS,YAAY,MAAM,cAAc,KAAI;AAAA,IAC9D,QAAQ,EAAC,OAAO,QAAQ,YAAY,MAAM,cAAc,KAAI;AAAA,IAC5D,YAAY,EAAC,OAAO,YAAY,YAAY,MAAM,cAAc,KAAI;AAAA,IACpE,QAAQ,EAAC,OAAO,QAAQ,YAAY,MAAM,cAAc,KAAI;AAAA,IAC5D,GAAG,EAAC,OAAO,GAAG,YAAY,MAAM,cAAc,KAAI;AAAA,IAClD,GAAG,EAAC,OAAO,GAAG,YAAY,MAAM,cAAc,KAAI;AAAA,IAClD,IAAI,EAAC,OAAO,IAAI,YAAY,MAAM,cAAc,KAAI;AAAA,IACpD,IAAI,EAAC,OAAO,IAAI,YAAY,MAAM,cAAc,KAAI;AAAA,IACpD,GAAG,EAAC,OAAOA,UAAQ;AAAA,EACrB,CAAC;AACH;AAEA,UAAU,UAAU,KAAK,WAAW;AAClC,MAAI,QAAQ,KAAK,EAAE,GAAG,MAAM,KAAK,GAAG,SAAS;AAC7C,SAAO,UAAU,KAAK,IAAI,OAAO;AACnC;;;ACnBA,SAAS,cAAc,OAAO;AAC5B,SAAO,CAAC,MAAM,WAAW,CAAC,MAAM;AAClC;AAEA,SAAS,mBAAmB;AAC1B,SAAO,KAAK;AACd;AAEA,SAAS,eAAe,OAAO,GAAG;AAChC,SAAO,KAAK,OAAO,EAAC,GAAG,MAAM,GAAG,GAAG,MAAM,EAAC,IAAI;AAChD;AAEA,SAAS,mBAAmB;AAC1B,SAAO,UAAU,kBAAmB,kBAAkB;AACxD;AAEe,SAAR,eAAmB;AACxB,MAAIC,UAAS,eACT,YAAY,kBACZ,UAAU,gBACV,YAAY,kBACZ,WAAW,CAAC,GACZ,YAAY,iBAAS,SAAS,QAAQ,KAAK,GAC3C,SAAS,GACT,YACA,YACA,aACA,aACA,iBAAiB;AAErB,WAAS,KAAKC,YAAW;AACvB,IAAAA,WACK,GAAG,kBAAkB,WAAW,EAClC,OAAO,SAAS,EACd,GAAG,mBAAmB,YAAY,EAClC,GAAG,kBAAkB,YAAY,UAAU,EAC3C,GAAG,kCAAkC,UAAU,EAC/C,MAAM,gBAAgB,MAAM,EAC5B,MAAM,+BAA+B,eAAe;AAAA,EAC3D;AAEA,WAAS,YAAY,OAAO,GAAG;AAC7B,QAAI,eAAe,CAACD,QAAO,KAAK,MAAM,OAAO,CAAC,EAAG;AACjD,QAAI,UAAU,YAAY,MAAM,UAAU,KAAK,MAAM,OAAO,CAAC,GAAG,OAAO,GAAG,OAAO;AACjF,QAAI,CAAC,QAAS;AACd,IAAAE,gBAAO,MAAM,IAAI,EACd,GAAG,kBAAkB,YAAY,iBAAiB,EAClD,GAAG,gBAAgB,YAAY,iBAAiB;AACnD,mBAAO,MAAM,IAAI;AACjB,kBAAc,KAAK;AACnB,kBAAc;AACd,iBAAa,MAAM;AACnB,iBAAa,MAAM;AACnB,YAAQ,SAAS,KAAK;AAAA,EACxB;AAEA,WAAS,WAAW,OAAO;AACzB,oBAAQ,KAAK;AACb,QAAI,CAAC,aAAa;AAChB,UAAI,KAAK,MAAM,UAAU,YAAY,KAAK,MAAM,UAAU;AAC1D,oBAAc,KAAK,KAAK,KAAK,KAAK;AAAA,IACpC;AACA,aAAS,MAAM,QAAQ,KAAK;AAAA,EAC9B;AAEA,WAAS,WAAW,OAAO;AACzB,IAAAA,gBAAO,MAAM,IAAI,EAAE,GAAG,+BAA+B,IAAI;AACzD,YAAQ,MAAM,MAAM,WAAW;AAC/B,oBAAQ,KAAK;AACb,aAAS,MAAM,OAAO,KAAK;AAAA,EAC7B;AAEA,WAAS,aAAa,OAAO,GAAG;AAC9B,QAAI,CAACF,QAAO,KAAK,MAAM,OAAO,CAAC,EAAG;AAClC,QAAI,UAAU,MAAM,gBAChB,IAAI,UAAU,KAAK,MAAM,OAAO,CAAC,GACjC,IAAI,QAAQ,QAAQ,GAAG;AAE3B,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,UAAI,UAAU,YAAY,MAAM,GAAG,OAAO,GAAG,QAAQ,CAAC,EAAE,YAAY,QAAQ,CAAC,CAAC,GAAG;AAC/E,sBAAc,KAAK;AACnB,gBAAQ,SAAS,OAAO,QAAQ,CAAC,CAAC;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AAEA,WAAS,WAAW,OAAO;AACzB,QAAI,UAAU,MAAM,gBAChB,IAAI,QAAQ,QAAQ,GAAG;AAE3B,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,UAAI,UAAU,SAAS,QAAQ,CAAC,EAAE,UAAU,GAAG;AAC7C,wBAAQ,KAAK;AACb,gBAAQ,QAAQ,OAAO,QAAQ,CAAC,CAAC;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAEA,WAAS,WAAW,OAAO;AACzB,QAAI,UAAU,MAAM,gBAChB,IAAI,QAAQ,QAAQ,GAAG;AAE3B,QAAI,YAAa,cAAa,WAAW;AACzC,kBAAc,WAAW,WAAW;AAAE,oBAAc;AAAA,IAAM,GAAG,GAAG;AAChE,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,UAAI,UAAU,SAAS,QAAQ,CAAC,EAAE,UAAU,GAAG;AAC7C,sBAAc,KAAK;AACnB,gBAAQ,OAAO,OAAO,QAAQ,CAAC,CAAC;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAEA,WAAS,YAAY,MAAMG,YAAW,OAAO,GAAG,YAAY,OAAO;AACjE,QAAIC,YAAW,UAAU,KAAK,GAC1B,IAAI,gBAAQ,SAAS,OAAOD,UAAS,GAAG,IAAI,IAC5C;AAEJ,SAAK,IAAI,QAAQ,KAAK,MAAM,IAAI,UAAU,eAAe;AAAA,MACrD,aAAa;AAAA,MACb,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA,GAAG,EAAE,CAAC;AAAA,MACN,GAAG,EAAE,CAAC;AAAA,MACN,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,UAAAC;AAAA,IACF,CAAC,GAAG,CAAC,MAAM,KAAM;AAEnB,SAAK,EAAE,IAAI,EAAE,CAAC,KAAK;AACnB,SAAK,EAAE,IAAI,EAAE,CAAC,KAAK;AAEnB,WAAO,SAAS,QAAQ,MAAMC,QAAOC,QAAO;AAC1C,UAAI,KAAK,GAAG;AACZ,cAAQ,MAAM;AAAA,QACZ,KAAK;AAAS,mBAAS,UAAU,IAAI,SAAS,IAAI;AAAU;AAAA,QAC5D,KAAK;AAAO,iBAAO,SAAS,UAAU,GAAG,EAAE;AAAA;AAAA,QAC3C,KAAK;AAAQ,cAAI,gBAAQA,UAASD,QAAOF,UAAS,GAAG,IAAI;AAAQ;AAAA,MACnE;AACA,MAAAC,UAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA,IAAI,UAAU,MAAM;AAAA,UAClB,aAAaC;AAAA,UACb,SAAS;AAAA,UACT,QAAQ;AAAA,UACR;AAAA,UACA,QAAQ;AAAA,UACR,GAAG,EAAE,CAAC,IAAI;AAAA,UACV,GAAG,EAAE,CAAC,IAAI;AAAA,UACV,IAAI,EAAE,CAAC,IAAI,GAAG,CAAC;AAAA,UACf,IAAI,EAAE,CAAC,IAAI,GAAG,CAAC;AAAA,UACf,UAAAD;AAAA,QACF,CAAC;AAAA,QACD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,OAAK,SAAS,SAAS,GAAG;AACxB,WAAO,UAAU,UAAUJ,UAAS,OAAO,MAAM,aAAa,IAAIO,kBAAS,CAAC,CAAC,CAAC,GAAG,QAAQP;AAAA,EAC3F;AAEA,OAAK,YAAY,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,YAAY,OAAO,MAAM,aAAa,IAAIO,kBAAS,CAAC,GAAG,QAAQ;AAAA,EAC5F;AAEA,OAAK,UAAU,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,UAAU,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,GAAG,QAAQ;AAAA,EAC1F;AAEA,OAAK,YAAY,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,YAAY,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,CAAC,CAAC,GAAG,QAAQ;AAAA,EAC9F;AAEA,OAAK,KAAK,WAAW;AACnB,QAAI,QAAQ,UAAU,GAAG,MAAM,WAAW,SAAS;AACnD,WAAO,UAAU,YAAY,OAAO;AAAA,EACtC;AAEA,OAAK,gBAAgB,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,kBAAkB,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK,KAAK,cAAc;AAAA,EAC5F;AAEA,SAAO;AACT;;;ACjMA,IAAI,QAAQ;AAAZ,IACI,UAAU;AADd,IAEI,WAAW;AAFf,IAGI,YAAY;AAHhB,IAII;AAJJ,IAKI;AALJ,IAMI,YAAY;AANhB,IAOI,WAAW;AAPf,IAQI,YAAY;AARhB,IASI,QAAQ,OAAO,gBAAgB,YAAY,YAAY,MAAM,cAAc;AAT/E,IAUI,WAAW,OAAO,WAAW,YAAY,OAAO,wBAAwB,OAAO,sBAAsB,KAAK,MAAM,IAAI,SAAS,GAAG;AAAE,aAAW,GAAG,EAAE;AAAG;AAElJ,SAAS,MAAM;AACpB,SAAO,aAAa,SAAS,QAAQ,GAAG,WAAW,MAAM,IAAI,IAAI;AACnE;AAEA,SAAS,WAAW;AAClB,aAAW;AACb;AAEO,SAAS,QAAQ;AACtB,OAAK,QACL,KAAK,QACL,KAAK,QAAQ;AACf;AAEA,MAAM,YAAY,MAAM,YAAY;AAAA,EAClC,aAAa;AAAA,EACb,SAAS,SAAS,UAAU,OAAO,MAAM;AACvC,QAAI,OAAO,aAAa,WAAY,OAAM,IAAI,UAAU,4BAA4B;AACpF,YAAQ,QAAQ,OAAO,IAAI,IAAI,CAAC,SAAS,SAAS,OAAO,IAAI,CAAC;AAC9D,QAAI,CAAC,KAAK,SAAS,aAAa,MAAM;AACpC,UAAI,SAAU,UAAS,QAAQ;AAAA,UAC1B,YAAW;AAChB,iBAAW;AAAA,IACb;AACA,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,UAAM;AAAA,EACR;AAAA,EACA,MAAM,WAAW;AACf,QAAI,KAAK,OAAO;AACd,WAAK,QAAQ;AACb,WAAK,QAAQ;AACb,YAAM;AAAA,IACR;AAAA,EACF;AACF;AAEO,SAAS,MAAM,UAAU,OAAO,MAAM;AAC3C,MAAI,IAAI,IAAI;AACZ,IAAE,QAAQ,UAAU,OAAO,IAAI;AAC/B,SAAO;AACT;AAEO,SAAS,aAAa;AAC3B,MAAI;AACJ,IAAE;AACF,MAAI,IAAI,UAAU;AAClB,SAAO,GAAG;AACR,SAAK,IAAI,WAAW,EAAE,UAAU,EAAG,GAAE,MAAM,KAAK,QAAW,CAAC;AAC5D,QAAI,EAAE;AAAA,EACR;AACA,IAAE;AACJ;AAEA,SAAS,OAAO;AACd,cAAY,YAAY,MAAM,IAAI,KAAK;AACvC,UAAQ,UAAU;AAClB,MAAI;AACF,eAAW;AAAA,EACb,UAAE;AACA,YAAQ;AACR,QAAI;AACJ,eAAW;AAAA,EACb;AACF;AAEA,SAAS,OAAO;AACd,MAAIC,OAAM,MAAM,IAAI,GAAG,QAAQA,OAAM;AACrC,MAAI,QAAQ,UAAW,cAAa,OAAO,YAAYA;AACzD;AAEA,SAAS,MAAM;AACb,MAAI,IAAI,KAAK,UAAU,IAAI,OAAO;AAClC,SAAO,IAAI;AACT,QAAI,GAAG,OAAO;AACZ,UAAI,OAAO,GAAG,MAAO,QAAO,GAAG;AAC/B,WAAK,IAAI,KAAK,GAAG;AAAA,IACnB,OAAO;AACL,WAAK,GAAG,OAAO,GAAG,QAAQ;AAC1B,WAAK,KAAK,GAAG,QAAQ,KAAK,WAAW;AAAA,IACvC;AAAA,EACF;AACA,aAAW;AACX,QAAM,IAAI;AACZ;AAEA,SAAS,MAAM,MAAM;AACnB,MAAI,MAAO;AACX,MAAI,QAAS,WAAU,aAAa,OAAO;AAC3C,MAAI,QAAQ,OAAO;AACnB,MAAI,QAAQ,IAAI;AACd,QAAI,OAAO,SAAU,WAAU,WAAW,MAAM,OAAO,MAAM,IAAI,IAAI,SAAS;AAC9E,QAAI,SAAU,YAAW,cAAc,QAAQ;AAAA,EACjD,OAAO;AACL,QAAI,CAAC,SAAU,aAAY,MAAM,IAAI,GAAG,WAAW,YAAY,MAAM,SAAS;AAC9E,YAAQ,GAAG,SAAS,IAAI;AAAA,EAC1B;AACF;;;AC3Ge,SAAR,gBAAiB,UAAU,OAAO,MAAM;AAC7C,MAAI,IAAI,IAAI;AACZ,UAAQ,SAAS,OAAO,IAAI,CAAC;AAC7B,IAAE,QAAQ,aAAW;AACnB,MAAE,KAAK;AACP,aAAS,UAAU,KAAK;AAAA,EAC1B,GAAG,OAAO,IAAI;AACd,SAAO;AACT;;;ACRe,SAAR,iBAAiB,UAAU,OAAO,MAAM;AAC7C,MAAI,IAAI,IAAI,SAAO,QAAQ;AAC3B,MAAI,SAAS,KAAM,QAAO,EAAE,QAAQ,UAAU,OAAO,IAAI,GAAG;AAC5D,IAAE,WAAW,EAAE;AACf,IAAE,UAAU,SAASC,WAAUC,QAAOC,OAAM;AAC1C,IAAAD,SAAQ,CAACA,QAAOC,QAAOA,SAAQ,OAAO,IAAI,IAAI,CAACA;AAC/C,MAAE,SAAS,SAAS,KAAK,SAAS;AAChC,iBAAW;AACX,QAAE,SAAS,MAAM,SAASD,QAAOC,KAAI;AACrC,MAAAF,UAAS,OAAO;AAAA,IAClB,GAAGC,QAAOC,KAAI;AAAA,EAChB;AACA,IAAE,QAAQ,UAAU,OAAO,IAAI;AAC/B,SAAO;AACT;;;ACbA,IAAI,UAAU,iBAAS,SAAS,OAAO,UAAU,WAAW;AAC5D,IAAI,aAAa,CAAC;AAEX,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,QAAQ;AAEJ,SAAR,iBAAiB,MAAM,MAAMC,KAAI,OAAO,OAAO,QAAQ;AAC5D,MAAI,YAAY,KAAK;AACrB,MAAI,CAAC,UAAW,MAAK,eAAe,CAAC;AAAA,WAC5BA,OAAM,UAAW;AAC1B,SAAO,MAAMA,KAAI;AAAA,IACf;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,IACA,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM,OAAO;AAAA,IACb,OAAO,OAAO;AAAA,IACd,UAAU,OAAO;AAAA,IACjB,MAAM,OAAO;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,EACT,CAAC;AACH;AAEO,SAAS,KAAK,MAAMA,KAAI;AAC7B,MAAI,WAAWC,KAAI,MAAMD,GAAE;AAC3B,MAAI,SAAS,QAAQ,QAAS,OAAM,IAAI,MAAM,6BAA6B;AAC3E,SAAO;AACT;AAEO,SAASE,KAAI,MAAMF,KAAI;AAC5B,MAAI,WAAWC,KAAI,MAAMD,GAAE;AAC3B,MAAI,SAAS,QAAQ,QAAS,OAAM,IAAI,MAAM,2BAA2B;AACzE,SAAO;AACT;AAEO,SAASC,KAAI,MAAMD,KAAI;AAC5B,MAAI,WAAW,KAAK;AACpB,MAAI,CAAC,YAAY,EAAE,WAAW,SAASA,GAAE,GAAI,OAAM,IAAI,MAAM,sBAAsB;AACnF,SAAO;AACT;AAEA,SAAS,OAAO,MAAMA,KAAI,MAAM;AAC9B,MAAI,YAAY,KAAK,cACjB;AAIJ,YAAUA,GAAE,IAAI;AAChB,OAAK,QAAQ,MAAM,UAAU,GAAG,KAAK,IAAI;AAEzC,WAAS,SAAS,SAAS;AACzB,SAAK,QAAQ;AACb,SAAK,MAAM,QAAQG,QAAO,KAAK,OAAO,KAAK,IAAI;AAG/C,QAAI,KAAK,SAAS,QAAS,CAAAA,OAAM,UAAU,KAAK,KAAK;AAAA,EACvD;AAEA,WAASA,OAAM,SAAS;AACtB,QAAI,GAAG,GAAG,GAAG;AAGb,QAAI,KAAK,UAAU,UAAW,QAAO,KAAK;AAE1C,SAAK,KAAK,WAAW;AACnB,UAAI,UAAU,CAAC;AACf,UAAI,EAAE,SAAS,KAAK,KAAM;AAK1B,UAAI,EAAE,UAAU,QAAS,QAAO,gBAAQA,MAAK;AAG7C,UAAI,EAAE,UAAU,SAAS;AACvB,UAAE,QAAQ;AACV,UAAE,MAAM,KAAK;AACb,UAAE,GAAG,KAAK,aAAa,MAAM,KAAK,UAAU,EAAE,OAAO,EAAE,KAAK;AAC5D,eAAO,UAAU,CAAC;AAAA,MACpB,WAGS,CAAC,IAAIH,KAAI;AAChB,UAAE,QAAQ;AACV,UAAE,MAAM,KAAK;AACb,UAAE,GAAG,KAAK,UAAU,MAAM,KAAK,UAAU,EAAE,OAAO,EAAE,KAAK;AACzD,eAAO,UAAU,CAAC;AAAA,MACpB;AAAA,IACF;AAMA,oBAAQ,WAAW;AACjB,UAAI,KAAK,UAAU,SAAS;AAC1B,aAAK,QAAQ;AACb,aAAK,MAAM,QAAQ,MAAM,KAAK,OAAO,KAAK,IAAI;AAC9C,aAAK,OAAO;AAAA,MACd;AAAA,IACF,CAAC;AAID,SAAK,QAAQ;AACb,SAAK,GAAG,KAAK,SAAS,MAAM,KAAK,UAAU,KAAK,OAAO,KAAK,KAAK;AACjE,QAAI,KAAK,UAAU,SAAU;AAC7B,SAAK,QAAQ;AAGb,YAAQ,IAAI,MAAM,IAAI,KAAK,MAAM,MAAM;AACvC,SAAK,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG;AAC9B,UAAI,IAAI,KAAK,MAAM,CAAC,EAAE,MAAM,KAAK,MAAM,KAAK,UAAU,KAAK,OAAO,KAAK,KAAK,GAAG;AAC7E,cAAM,EAAE,CAAC,IAAI;AAAA,MACf;AAAA,IACF;AACA,UAAM,SAAS,IAAI;AAAA,EACrB;AAEA,WAAS,KAAK,SAAS;AACrB,QAAI,IAAI,UAAU,KAAK,WAAW,KAAK,KAAK,KAAK,MAAM,UAAU,KAAK,QAAQ,KAAK,KAAK,MAAM,QAAQ,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAC9H,IAAI,IACJ,IAAI,MAAM;AAEd,WAAO,EAAE,IAAI,GAAG;AACd,YAAM,CAAC,EAAE,KAAK,MAAM,CAAC;AAAA,IACvB;AAGA,QAAI,KAAK,UAAU,QAAQ;AACzB,WAAK,GAAG,KAAK,OAAO,MAAM,KAAK,UAAU,KAAK,OAAO,KAAK,KAAK;AAC/D,WAAK;AAAA,IACP;AAAA,EACF;AAEA,WAAS,OAAO;AACd,SAAK,QAAQ;AACb,SAAK,MAAM,KAAK;AAChB,WAAO,UAAUA,GAAE;AACnB,aAAS,KAAK,UAAW;AACzB,WAAO,KAAK;AAAA,EACd;AACF;;;ACtJe,SAAR,kBAAiB,MAAM,MAAM;AAClC,MAAI,YAAY,KAAK,cACjB,UACA,QACAI,SAAQ,MACR;AAEJ,MAAI,CAAC,UAAW;AAEhB,SAAO,QAAQ,OAAO,OAAO,OAAO;AAEpC,OAAK,KAAK,WAAW;AACnB,SAAK,WAAW,UAAU,CAAC,GAAG,SAAS,MAAM;AAAE,MAAAA,SAAQ;AAAO;AAAA,IAAU;AACxE,aAAS,SAAS,QAAQ,YAAY,SAAS,QAAQ;AACvD,aAAS,QAAQ;AACjB,aAAS,MAAM,KAAK;AACpB,aAAS,GAAG,KAAK,SAAS,cAAc,UAAU,MAAM,KAAK,UAAU,SAAS,OAAO,SAAS,KAAK;AACrG,WAAO,UAAU,CAAC;AAAA,EACpB;AAEA,MAAIA,OAAO,QAAO,KAAK;AACzB;;;ACrBA,SAAS,YAAYC,KAAI,MAAM;AAC7B,MAAI,QAAQ;AACZ,SAAO,WAAW;AAChB,QAAI,WAAWC,KAAI,MAAMD,GAAE,GACvB,QAAQ,SAAS;AAKrB,QAAI,UAAU,QAAQ;AACpB,eAAS,SAAS;AAClB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC7C,YAAI,OAAO,CAAC,EAAE,SAAS,MAAM;AAC3B,mBAAS,OAAO,MAAM;AACtB,iBAAO,OAAO,GAAG,CAAC;AAClB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,aAAS,QAAQ;AAAA,EACnB;AACF;AAEA,SAAS,cAAcA,KAAI,MAAM,OAAO;AACtC,MAAI,QAAQ;AACZ,MAAI,OAAO,UAAU,WAAY,OAAM,IAAI;AAC3C,SAAO,WAAW;AAChB,QAAI,WAAWC,KAAI,MAAMD,GAAE,GACvB,QAAQ,SAAS;AAKrB,QAAI,UAAU,QAAQ;AACpB,gBAAU,SAAS,OAAO,MAAM;AAChC,eAAS,IAAI,EAAC,MAAY,MAAY,GAAG,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC7E,YAAI,OAAO,CAAC,EAAE,SAAS,MAAM;AAC3B,iBAAO,CAAC,IAAI;AACZ;AAAA,QACF;AAAA,MACF;AACA,UAAI,MAAM,EAAG,QAAO,KAAK,CAAC;AAAA,IAC5B;AAEA,aAAS,QAAQ;AAAA,EACnB;AACF;AAEe,SAAR,cAAiB,MAAM,OAAO;AACnC,MAAIA,MAAK,KAAK;AAEd,UAAQ;AAER,MAAI,UAAU,SAAS,GAAG;AACxB,QAAI,QAAQE,KAAI,KAAK,KAAK,GAAGF,GAAE,EAAE;AACjC,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/C,WAAK,IAAI,MAAM,CAAC,GAAG,SAAS,MAAM;AAChC,eAAO,EAAE;AAAA,MACX;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,SAAO,KAAK,MAAM,SAAS,OAAO,cAAc,eAAeA,KAAI,MAAM,KAAK,CAAC;AACjF;AAEO,SAAS,WAAWG,aAAY,MAAM,OAAO;AAClD,MAAIH,MAAKG,YAAW;AAEpB,EAAAA,YAAW,KAAK,WAAW;AACzB,QAAI,WAAWF,KAAI,MAAMD,GAAE;AAC3B,KAAC,SAAS,UAAU,SAAS,QAAQ,CAAC,IAAI,IAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AAAA,EAC/E,CAAC;AAED,SAAO,SAAS,MAAM;AACpB,WAAOE,KAAI,MAAMF,GAAE,EAAE,MAAM,IAAI;AAAA,EACjC;AACF;;;AC7Ee,SAAR,oBAAiB,GAAG,GAAG;AAC5B,MAAI;AACJ,UAAQ,OAAO,MAAM,WAAW,iBAC1B,aAAa,QAAQ,eACpB,IAAI,MAAM,CAAC,MAAM,IAAI,GAAG,eACzB,gBAAmB,GAAG,CAAC;AAC/B;;;ACJA,SAASI,YAAW,MAAM;AACxB,SAAO,WAAW;AAChB,SAAK,gBAAgB,IAAI;AAAA,EAC3B;AACF;AAEA,SAASC,cAAa,UAAU;AAC9B,SAAO,WAAW;AAChB,SAAK,kBAAkB,SAAS,OAAO,SAAS,KAAK;AAAA,EACvD;AACF;AAEA,SAASC,cAAa,MAAM,aAAa,QAAQ;AAC/C,MAAI,UACA,UAAU,SAAS,IACnB;AACJ,SAAO,WAAW;AAChB,QAAI,UAAU,KAAK,aAAa,IAAI;AACpC,WAAO,YAAY,UAAU,OACvB,YAAY,WAAW,eACvB,eAAe,YAAY,WAAW,SAAS,MAAM;AAAA,EAC7D;AACF;AAEA,SAASC,gBAAe,UAAU,aAAa,QAAQ;AACrD,MAAI,UACA,UAAU,SAAS,IACnB;AACJ,SAAO,WAAW;AAChB,QAAI,UAAU,KAAK,eAAe,SAAS,OAAO,SAAS,KAAK;AAChE,WAAO,YAAY,UAAU,OACvB,YAAY,WAAW,eACvB,eAAe,YAAY,WAAW,SAAS,MAAM;AAAA,EAC7D;AACF;AAEA,SAASC,cAAa,MAAM,aAAa,OAAO;AAC9C,MAAI,UACA,UACA;AACJ,SAAO,WAAW;AAChB,QAAI,SAAS,SAAS,MAAM,IAAI,GAAG;AACnC,QAAI,UAAU,KAAM,QAAO,KAAK,KAAK,gBAAgB,IAAI;AACzD,cAAU,KAAK,aAAa,IAAI;AAChC,cAAU,SAAS;AACnB,WAAO,YAAY,UAAU,OACvB,YAAY,YAAY,YAAY,WAAW,gBAC9C,WAAW,SAAS,eAAe,YAAY,WAAW,SAAS,MAAM;AAAA,EAClF;AACF;AAEA,SAASC,gBAAe,UAAU,aAAa,OAAO;AACpD,MAAI,UACA,UACA;AACJ,SAAO,WAAW;AAChB,QAAI,SAAS,SAAS,MAAM,IAAI,GAAG;AACnC,QAAI,UAAU,KAAM,QAAO,KAAK,KAAK,kBAAkB,SAAS,OAAO,SAAS,KAAK;AACrF,cAAU,KAAK,eAAe,SAAS,OAAO,SAAS,KAAK;AAC5D,cAAU,SAAS;AACnB,WAAO,YAAY,UAAU,OACvB,YAAY,YAAY,YAAY,WAAW,gBAC9C,WAAW,SAAS,eAAe,YAAY,WAAW,SAAS,MAAM;AAAA,EAClF;AACF;AAEe,SAARC,cAAiB,MAAM,OAAO;AACnC,MAAI,WAAW,kBAAU,IAAI,GAAG,IAAI,aAAa,cAAc,0BAAuB;AACtF,SAAO,KAAK,UAAU,MAAM,OAAO,UAAU,cACtC,SAAS,QAAQD,kBAAiBD,eAAc,UAAU,GAAG,WAAW,MAAM,UAAU,MAAM,KAAK,CAAC,IACrG,SAAS,QAAQ,SAAS,QAAQH,gBAAeD,aAAY,QAAQ,KACpE,SAAS,QAAQG,kBAAiBD,eAAc,UAAU,GAAG,KAAK,CAAC;AAC5E;;;AC3EA,SAAS,gBAAgB,MAAM,GAAG;AAChC,SAAO,SAAS,GAAG;AACjB,SAAK,aAAa,MAAM,EAAE,KAAK,MAAM,CAAC,CAAC;AAAA,EACzC;AACF;AAEA,SAAS,kBAAkB,UAAU,GAAG;AACtC,SAAO,SAAS,GAAG;AACjB,SAAK,eAAe,SAAS,OAAO,SAAS,OAAO,EAAE,KAAK,MAAM,CAAC,CAAC;AAAA,EACrE;AACF;AAEA,SAAS,YAAY,UAAU,OAAO;AACpC,MAAI,IAAI;AACR,WAAS,QAAQ;AACf,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,MAAM,GAAI,OAAM,KAAK,MAAM,kBAAkB,UAAU,CAAC;AAC5D,WAAO;AAAA,EACT;AACA,QAAM,SAAS;AACf,SAAO;AACT;AAEA,SAAS,UAAU,MAAM,OAAO;AAC9B,MAAI,IAAI;AACR,WAAS,QAAQ;AACf,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,MAAM,GAAI,OAAM,KAAK,MAAM,gBAAgB,MAAM,CAAC;AACtD,WAAO;AAAA,EACT;AACA,QAAM,SAAS;AACf,SAAO;AACT;AAEe,SAAR,kBAAiB,MAAM,OAAO;AACnC,MAAI,MAAM,UAAU;AACpB,MAAI,UAAU,SAAS,EAAG,SAAQ,MAAM,KAAK,MAAM,GAAG,MAAM,IAAI;AAChE,MAAI,SAAS,KAAM,QAAO,KAAK,MAAM,KAAK,IAAI;AAC9C,MAAI,OAAO,UAAU,WAAY,OAAM,IAAI;AAC3C,MAAI,WAAW,kBAAU,IAAI;AAC7B,SAAO,KAAK,MAAM,MAAM,SAAS,QAAQ,cAAc,WAAW,UAAU,KAAK,CAAC;AACpF;;;ACzCA,SAAS,cAAcK,KAAI,OAAO;AAChC,SAAO,WAAW;AAChB,SAAK,MAAMA,GAAE,EAAE,QAAQ,CAAC,MAAM,MAAM,MAAM,SAAS;AAAA,EACrD;AACF;AAEA,SAAS,cAAcA,KAAI,OAAO;AAChC,SAAO,QAAQ,CAAC,OAAO,WAAW;AAChC,SAAK,MAAMA,GAAE,EAAE,QAAQ;AAAA,EACzB;AACF;AAEe,SAAR,cAAiB,OAAO;AAC7B,MAAIA,MAAK,KAAK;AAEd,SAAO,UAAU,SACX,KAAK,MAAM,OAAO,UAAU,aACxB,gBACA,eAAeA,KAAI,KAAK,CAAC,IAC7BC,KAAI,KAAK,KAAK,GAAGD,GAAE,EAAE;AAC7B;;;ACpBA,SAAS,iBAAiBE,KAAI,OAAO;AACnC,SAAO,WAAW;AAChB,IAAAC,KAAI,MAAMD,GAAE,EAAE,WAAW,CAAC,MAAM,MAAM,MAAM,SAAS;AAAA,EACvD;AACF;AAEA,SAAS,iBAAiBA,KAAI,OAAO;AACnC,SAAO,QAAQ,CAAC,OAAO,WAAW;AAChC,IAAAC,KAAI,MAAMD,GAAE,EAAE,WAAW;AAAA,EAC3B;AACF;AAEe,SAAR,iBAAiB,OAAO;AAC7B,MAAIA,MAAK,KAAK;AAEd,SAAO,UAAU,SACX,KAAK,MAAM,OAAO,UAAU,aACxB,mBACA,kBAAkBA,KAAI,KAAK,CAAC,IAChCE,KAAI,KAAK,KAAK,GAAGF,GAAE,EAAE;AAC7B;;;ACpBA,SAAS,aAAaG,KAAI,OAAO;AAC/B,MAAI,OAAO,UAAU,WAAY,OAAM,IAAI;AAC3C,SAAO,WAAW;AAChB,IAAAC,KAAI,MAAMD,GAAE,EAAE,OAAO;AAAA,EACvB;AACF;AAEe,SAAR,aAAiB,OAAO;AAC7B,MAAIA,MAAK,KAAK;AAEd,SAAO,UAAU,SACX,KAAK,KAAK,aAAaA,KAAI,KAAK,CAAC,IACjCE,KAAI,KAAK,KAAK,GAAGF,GAAE,EAAE;AAC7B;;;ACbA,SAAS,YAAYG,KAAI,OAAO;AAC9B,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,OAAO,MAAM,WAAY,OAAM,IAAI;AACvC,IAAAC,KAAI,MAAMD,GAAE,EAAE,OAAO;AAAA,EACvB;AACF;AAEe,SAAR,oBAAiB,OAAO;AAC7B,MAAI,OAAO,UAAU,WAAY,OAAM,IAAI;AAC3C,SAAO,KAAK,KAAK,YAAY,KAAK,KAAK,KAAK,CAAC;AAC/C;;;ACVe,SAARE,gBAAiB,OAAO;AAC7B,MAAI,OAAO,UAAU,WAAY,SAAQ,gBAAQ,KAAK;AAEtD,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9F,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,WAAW,UAAU,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACnG,WAAK,OAAO,MAAM,CAAC,MAAM,MAAM,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,GAAG;AAClE,iBAAS,KAAK,IAAI;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,WAAW,WAAW,KAAK,UAAU,KAAK,OAAO,KAAK,GAAG;AACtE;;;ACbe,SAARC,eAAiBC,aAAY;AAClC,MAAIA,YAAW,QAAQ,KAAK,IAAK,OAAM,IAAI;AAE3C,WAAS,UAAU,KAAK,SAAS,UAAUA,YAAW,SAAS,KAAK,QAAQ,QAAQ,KAAK,QAAQ,QAAQ,IAAI,KAAK,IAAI,IAAI,EAAE,GAAG,SAAS,IAAI,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACxK,aAAS,SAAS,QAAQ,CAAC,GAAG,SAAS,QAAQ,CAAC,GAAG,IAAI,OAAO,QAAQ,QAAQ,OAAO,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/H,UAAI,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,GAAG;AACjC,cAAM,CAAC,IAAI;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,IAAI,EAAE,GAAG;AAClB,WAAO,CAAC,IAAI,QAAQ,CAAC;AAAA,EACvB;AAEA,SAAO,IAAI,WAAW,QAAQ,KAAK,UAAU,KAAK,OAAO,KAAK,GAAG;AACnE;;;AChBA,SAAS,MAAM,MAAM;AACnB,UAAQ,OAAO,IAAI,KAAK,EAAE,MAAM,OAAO,EAAE,MAAM,SAAS,GAAG;AACzD,QAAI,IAAI,EAAE,QAAQ,GAAG;AACrB,QAAI,KAAK,EAAG,KAAI,EAAE,MAAM,GAAG,CAAC;AAC5B,WAAO,CAAC,KAAK,MAAM;AAAA,EACrB,CAAC;AACH;AAEA,SAAS,WAAWC,KAAI,MAAM,UAAU;AACtC,MAAI,KAAK,KAAK,MAAM,MAAM,IAAI,IAAI,OAAOC;AACzC,SAAO,WAAW;AAChB,QAAI,WAAW,IAAI,MAAMD,GAAE,GACvB,KAAK,SAAS;AAKlB,QAAI,OAAO,IAAK,EAAC,OAAO,MAAM,IAAI,KAAK,GAAG,GAAG,MAAM,QAAQ;AAE3D,aAAS,KAAK;AAAA,EAChB;AACF;AAEe,SAARE,YAAiB,MAAM,UAAU;AACtC,MAAIF,MAAK,KAAK;AAEd,SAAO,UAAU,SAAS,IACpBG,KAAI,KAAK,KAAK,GAAGH,GAAE,EAAE,GAAG,GAAG,IAAI,IAC/B,KAAK,KAAK,WAAWA,KAAI,MAAM,QAAQ,CAAC;AAChD;;;AC/BA,SAAS,eAAeI,KAAI;AAC1B,SAAO,WAAW;AAChB,QAAI,SAAS,KAAK;AAClB,aAAS,KAAK,KAAK,aAAc,KAAI,CAAC,MAAMA,IAAI;AAChD,QAAI,OAAQ,QAAO,YAAY,IAAI;AAAA,EACrC;AACF;AAEe,SAARC,kBAAmB;AACxB,SAAO,KAAK,GAAG,cAAc,eAAe,KAAK,GAAG,CAAC;AACvD;;;ACNe,SAARC,gBAAiB,QAAQ;AAC9B,MAAI,OAAO,KAAK,OACZC,MAAK,KAAK;AAEd,MAAI,OAAO,WAAW,WAAY,UAAS,iBAAS,MAAM;AAE1D,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9F,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,WAAW,UAAU,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,SAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtH,WAAK,OAAO,MAAM,CAAC,OAAO,UAAU,OAAO,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,IAAI;AAC/E,YAAI,cAAc,KAAM,SAAQ,WAAW,KAAK;AAChD,iBAAS,CAAC,IAAI;AACd,yBAAS,SAAS,CAAC,GAAG,MAAMA,KAAI,GAAG,UAAUC,KAAI,MAAMD,GAAE,CAAC;AAAA,MAC5D;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,WAAW,WAAW,KAAK,UAAU,MAAMA,GAAE;AAC1D;;;ACjBe,SAARE,mBAAiB,QAAQ;AAC9B,MAAI,OAAO,KAAK,OACZC,MAAK,KAAK;AAEd,MAAI,OAAO,WAAW,WAAY,UAAS,oBAAY,MAAM;AAE7D,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,CAAC,GAAG,UAAU,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAClG,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,iBAASC,YAAW,OAAO,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,GAAG,OAAOC,WAAUC,KAAI,MAAMH,GAAE,GAAG,IAAI,GAAG,IAAIC,UAAS,QAAQ,IAAI,GAAG,EAAE,GAAG;AACtI,cAAI,QAAQA,UAAS,CAAC,GAAG;AACvB,6BAAS,OAAO,MAAMD,KAAI,GAAGC,WAAUC,QAAO;AAAA,UAChD;AAAA,QACF;AACA,kBAAU,KAAKD,SAAQ;AACvB,gBAAQ,KAAK,IAAI;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,WAAW,WAAW,SAAS,MAAMD,GAAE;AACpD;;;ACvBA,IAAII,aAAY,kBAAU,UAAU;AAErB,SAARC,qBAAmB;AACxB,SAAO,IAAID,WAAU,KAAK,SAAS,KAAK,QAAQ;AAClD;;;ACAA,SAAS,UAAU,MAAM,aAAa;AACpC,MAAI,UACA,UACA;AACJ,SAAO,WAAW;AAChB,QAAI,UAAU,WAAM,MAAM,IAAI,GAC1B,WAAW,KAAK,MAAM,eAAe,IAAI,GAAG,WAAM,MAAM,IAAI;AAChE,WAAO,YAAY,UAAU,OACvB,YAAY,YAAY,YAAY,WAAW,eAC/C,eAAe,YAAY,WAAW,SAAS,WAAW,OAAO;AAAA,EACzE;AACF;AAEA,SAASE,aAAY,MAAM;AACzB,SAAO,WAAW;AAChB,SAAK,MAAM,eAAe,IAAI;AAAA,EAChC;AACF;AAEA,SAASC,eAAc,MAAM,aAAa,QAAQ;AAChD,MAAI,UACA,UAAU,SAAS,IACnB;AACJ,SAAO,WAAW;AAChB,QAAI,UAAU,WAAM,MAAM,IAAI;AAC9B,WAAO,YAAY,UAAU,OACvB,YAAY,WAAW,eACvB,eAAe,YAAY,WAAW,SAAS,MAAM;AAAA,EAC7D;AACF;AAEA,SAASC,eAAc,MAAM,aAAa,OAAO;AAC/C,MAAI,UACA,UACA;AACJ,SAAO,WAAW;AAChB,QAAI,UAAU,WAAM,MAAM,IAAI,GAC1B,SAAS,MAAM,IAAI,GACnB,UAAU,SAAS;AACvB,QAAI,UAAU,KAAM,WAAU,UAAU,KAAK,MAAM,eAAe,IAAI,GAAG,WAAM,MAAM,IAAI;AACzF,WAAO,YAAY,UAAU,OACvB,YAAY,YAAY,YAAY,WAAW,gBAC9C,WAAW,SAAS,eAAe,YAAY,WAAW,SAAS,MAAM;AAAA,EAClF;AACF;AAEA,SAAS,iBAAiBC,KAAI,MAAM;AAClC,MAAI,KAAK,KAAK,WAAW,MAAM,WAAW,MAAM,QAAQ,SAAS,KAAKC;AACtE,SAAO,WAAW;AAChB,QAAI,WAAWC,KAAI,MAAMF,GAAE,GACvB,KAAK,SAAS,IACd,WAAW,SAAS,MAAM,GAAG,KAAK,OAAOC,YAAWA,UAASJ,aAAY,IAAI,KAAK;AAKtF,QAAI,OAAO,OAAO,cAAc,SAAU,EAAC,OAAO,MAAM,IAAI,KAAK,GAAG,GAAG,OAAO,YAAY,QAAQ;AAElG,aAAS,KAAK;AAAA,EAChB;AACF;AAEe,SAARM,eAAiB,MAAM,OAAO,UAAU;AAC7C,MAAI,KAAK,QAAQ,QAAQ,cAAc,0BAAuB;AAC9D,SAAO,SAAS,OAAO,KAClB,WAAW,MAAM,UAAU,MAAM,CAAC,CAAC,EACnC,GAAG,eAAe,MAAMN,aAAY,IAAI,CAAC,IAC1C,OAAO,UAAU,aAAa,KAC7B,WAAW,MAAME,eAAc,MAAM,GAAG,WAAW,MAAM,WAAW,MAAM,KAAK,CAAC,CAAC,EACjF,KAAK,iBAAiB,KAAK,KAAK,IAAI,CAAC,IACtC,KACC,WAAW,MAAMD,eAAc,MAAM,GAAG,KAAK,GAAG,QAAQ,EACxD,GAAG,eAAe,MAAM,IAAI;AACnC;;;AC/EA,SAAS,iBAAiB,MAAM,GAAG,UAAU;AAC3C,SAAO,SAAS,GAAG;AACjB,SAAK,MAAM,YAAY,MAAM,EAAE,KAAK,MAAM,CAAC,GAAG,QAAQ;AAAA,EACxD;AACF;AAEA,SAAS,WAAW,MAAM,OAAO,UAAU;AACzC,MAAI,GAAG;AACP,WAAS,QAAQ;AACf,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,MAAM,GAAI,MAAK,KAAK,MAAM,iBAAiB,MAAM,GAAG,QAAQ;AAChE,WAAO;AAAA,EACT;AACA,QAAM,SAAS;AACf,SAAO;AACT;AAEe,SAAR,mBAAiB,MAAM,OAAO,UAAU;AAC7C,MAAI,MAAM,YAAY,QAAQ;AAC9B,MAAI,UAAU,SAAS,EAAG,SAAQ,MAAM,KAAK,MAAM,GAAG,MAAM,IAAI;AAChE,MAAI,SAAS,KAAM,QAAO,KAAK,MAAM,KAAK,IAAI;AAC9C,MAAI,OAAO,UAAU,WAAY,OAAM,IAAI;AAC3C,SAAO,KAAK,MAAM,KAAK,WAAW,MAAM,OAAO,YAAY,OAAO,KAAK,QAAQ,CAAC;AAClF;;;ACrBA,SAASM,cAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,SAAK,cAAc;AAAA,EACrB;AACF;AAEA,SAASC,cAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,QAAI,SAAS,MAAM,IAAI;AACvB,SAAK,cAAc,UAAU,OAAO,KAAK;AAAA,EAC3C;AACF;AAEe,SAARC,cAAiB,OAAO;AAC7B,SAAO,KAAK,MAAM,QAAQ,OAAO,UAAU,aACrCD,cAAa,WAAW,MAAM,QAAQ,KAAK,CAAC,IAC5CD,cAAa,SAAS,OAAO,KAAK,QAAQ,EAAE,CAAC;AACrD;;;ACnBA,SAAS,gBAAgB,GAAG;AAC1B,SAAO,SAAS,GAAG;AACjB,SAAK,cAAc,EAAE,KAAK,MAAM,CAAC;AAAA,EACnC;AACF;AAEA,SAAS,UAAU,OAAO;AACxB,MAAI,IAAI;AACR,WAAS,QAAQ;AACf,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,MAAM,GAAI,OAAM,KAAK,MAAM,gBAAgB,CAAC;AAChD,WAAO;AAAA,EACT;AACA,QAAM,SAAS;AACf,SAAO;AACT;AAEe,SAAR,kBAAiB,OAAO;AAC7B,MAAI,MAAM;AACV,MAAI,UAAU,SAAS,EAAG,SAAQ,MAAM,KAAK,MAAM,GAAG,MAAM,IAAI;AAChE,MAAI,SAAS,KAAM,QAAO,KAAK,MAAM,KAAK,IAAI;AAC9C,MAAI,OAAO,UAAU,WAAY,OAAM,IAAI;AAC3C,SAAO,KAAK,MAAM,KAAK,UAAU,KAAK,CAAC;AACzC;;;ACpBe,SAAR,qBAAmB;AACxB,MAAI,OAAO,KAAK,OACZ,MAAM,KAAK,KACX,MAAM,MAAM;AAEhB,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,YAAIG,WAAUC,KAAI,MAAM,GAAG;AAC3B,yBAAS,MAAM,MAAM,KAAK,GAAG,OAAO;AAAA,UAClC,MAAMD,SAAQ,OAAOA,SAAQ,QAAQA,SAAQ;AAAA,UAC7C,OAAO;AAAA,UACP,UAAUA,SAAQ;AAAA,UAClB,MAAMA,SAAQ;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,WAAW,QAAQ,KAAK,UAAU,MAAM,GAAG;AACxD;;;ACrBe,SAAR,cAAmB;AACxB,MAAI,KAAK,KAAK,OAAO,MAAME,MAAK,KAAK,KAAK,OAAO,KAAK,KAAK;AAC3D,SAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,QAAI,SAAS,EAAC,OAAO,OAAM,GACvB,MAAM,EAAC,OAAO,WAAW;AAAE,UAAI,EAAE,SAAS,EAAG,SAAQ;AAAA,IAAG,EAAC;AAE7D,SAAK,KAAK,WAAW;AACnB,UAAI,WAAWC,KAAI,MAAMD,GAAE,GACvB,KAAK,SAAS;AAKlB,UAAI,OAAO,KAAK;AACd,eAAO,MAAM,IAAI,KAAK;AACtB,YAAI,EAAE,OAAO,KAAK,MAAM;AACxB,YAAI,EAAE,UAAU,KAAK,MAAM;AAC3B,YAAI,EAAE,IAAI,KAAK,GAAG;AAAA,MACpB;AAEA,eAAS,KAAK;AAAA,IAChB,CAAC;AAGD,QAAI,SAAS,EAAG,SAAQ;AAAA,EAC1B,CAAC;AACH;;;ACNA,IAAI,KAAK;AAEF,SAAS,WAAW,QAAQ,SAAS,MAAME,KAAI;AACpD,OAAK,UAAU;AACf,OAAK,WAAW;AAChB,OAAK,QAAQ;AACb,OAAK,MAAMA;AACb;AAEe,SAAR,WAA4B,MAAM;AACvC,SAAO,kBAAU,EAAE,WAAW,IAAI;AACpC;AAEO,SAAS,QAAQ;AACtB,SAAO,EAAE;AACX;AAEA,IAAI,sBAAsB,kBAAU;AAEpC,WAAW,YAAY,WAAW,YAAY;AAAA,EAC5C,aAAa;AAAA,EACb,QAAQC;AAAA,EACR,WAAWC;AAAA,EACX,aAAa,oBAAoB;AAAA,EACjC,gBAAgB,oBAAoB;AAAA,EACpC,QAAQC;AAAA,EACR,OAAOC;AAAA,EACP,WAAWC;AAAA,EACX,YAAY;AAAA,EACZ,MAAM,oBAAoB;AAAA,EAC1B,OAAO,oBAAoB;AAAA,EAC3B,MAAM,oBAAoB;AAAA,EAC1B,MAAM,oBAAoB;AAAA,EAC1B,OAAO,oBAAoB;AAAA,EAC3B,MAAM,oBAAoB;AAAA,EAC1B,IAAIC;AAAA,EACJ,MAAMC;AAAA,EACN,WAAW;AAAA,EACX,OAAOC;AAAA,EACP,YAAY;AAAA,EACZ,MAAMC;AAAA,EACN,WAAW;AAAA,EACX,QAAQC;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,MAAM;AAAA,EACN,aAAa;AAAA,EACb,KAAK;AAAA,EACL,CAAC,OAAO,QAAQ,GAAG,oBAAoB,OAAO,QAAQ;AACxD;;;ACxEO,IAAM,SAAS,OAAK,CAAC;;;ACArB,SAAS,OAAO,GAAG;AACxB,SAAO,IAAI;AACb;AAEO,SAAS,QAAQ,GAAG;AACzB,SAAO,KAAK,IAAI;AAClB;AAEO,SAAS,UAAU,GAAG;AAC3B,WAAS,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE,KAAK,IAAI,KAAK,KAAK;AACvD;;;ACVO,SAAS,QAAQ,GAAG;AACzB,SAAO,IAAI,IAAI;AACjB;AAEO,SAAS,SAAS,GAAG;AAC1B,SAAO,EAAE,IAAI,IAAI,IAAI;AACvB;AAEO,SAAS,WAAW,GAAG;AAC5B,WAAS,KAAK,MAAM,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK;AAC9D;;;ACVA,IAAI,WAAW;AAER,IAAI,SAAU,SAAS,OAAO,GAAG;AACtC,MAAI,CAAC;AAEL,WAASC,QAAO,GAAG;AACjB,WAAO,KAAK,IAAI,GAAG,CAAC;AAAA,EACtB;AAEA,EAAAA,QAAO,WAAW;AAElB,SAAOA;AACT,EAAG,QAAQ;AAEJ,IAAI,UAAW,SAASC,QAAO,GAAG;AACvC,MAAI,CAAC;AAEL,WAASC,SAAQ,GAAG;AAClB,WAAO,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC;AAAA,EAC9B;AAEA,EAAAA,SAAQ,WAAWD;AAEnB,SAAOC;AACT,EAAG,QAAQ;AAEJ,IAAI,YAAa,SAASD,QAAO,GAAG;AACzC,MAAI,CAAC;AAEL,WAASE,WAAU,GAAG;AACpB,aAAS,KAAK,MAAM,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC,KAAK;AAAA,EACrE;AAEA,EAAAA,WAAU,WAAWF;AAErB,SAAOE;AACT,EAAG,QAAQ;;;ACpCX,IAAI,KAAK,KAAK;AAAd,IACI,SAAS,KAAK;AAEX,SAAS,MAAM,GAAG;AACvB,SAAQ,CAAC,MAAM,IAAK,IAAI,IAAI,KAAK,IAAI,IAAI,MAAM;AACjD;AAEO,SAAS,OAAO,GAAG;AACxB,SAAO,KAAK,IAAI,IAAI,MAAM;AAC5B;AAEO,SAAS,SAAS,GAAG;AAC1B,UAAQ,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK;AAClC;;;ACZO,SAAS,KAAK,GAAG;AACtB,UAAQ,KAAK,IAAI,GAAG,MAAM,CAAC,IAAI,eAAgB;AACjD;;;ACDO,SAAS,MAAM,GAAG;AACvB,SAAO,KAAK,IAAI,CAAC,CAAC;AACpB;AAEO,SAAS,OAAO,GAAG;AACxB,SAAO,IAAI,KAAK,CAAC;AACnB;AAEO,SAAS,SAAS,GAAG;AAC1B,WAAS,KAAK,MAAM,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK;AAC3D;;;ACZO,SAAS,SAAS,GAAG;AAC1B,SAAO,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC;AAChC;AAEO,SAAS,UAAU,GAAG;AAC3B,SAAO,KAAK,KAAK,IAAI,EAAE,IAAI,CAAC;AAC9B;AAEO,SAAS,YAAY,GAAG;AAC7B,WAAS,KAAK,MAAM,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,CAAC,IAAI,KAAK;AACxF;;;ACVA,IAAI,KAAK,IAAI;AAAb,IACI,KAAK,IAAI;AADb,IAEI,KAAK,IAAI;AAFb,IAGI,KAAK,IAAI;AAHb,IAII,KAAK,IAAI;AAJb,IAKI,KAAK,KAAK;AALd,IAMI,KAAK,KAAK;AANd,IAOI,KAAK,KAAK;AAPd,IAQI,KAAK,KAAK;AARd,IASI,KAAK,IAAI,KAAK;AAEX,SAAS,SAAS,GAAG;AAC1B,SAAO,IAAI,UAAU,IAAI,CAAC;AAC5B;AAEO,SAAS,UAAU,GAAG;AAC3B,UAAQ,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,MAAM,KAAK,MAAM,IAAI,KAAK,MAAM,KAAK,MAAM,IAAI;AACjI;AAEO,SAAS,YAAY,GAAG;AAC7B,WAAS,KAAK,MAAM,IAAI,IAAI,UAAU,IAAI,CAAC,IAAI,UAAU,IAAI,CAAC,IAAI,KAAK;AACzE;;;ACrBA,IAAI,YAAY;AAET,IAAI,SAAU,SAASC,QAAO,GAAG;AACtC,MAAI,CAAC;AAEL,WAASC,QAAO,GAAG;AACjB,YAAQ,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,KAAK;AAAA,EACvC;AAEA,EAAAA,QAAO,YAAYD;AAEnB,SAAOC;AACT,EAAG,SAAS;AAEL,IAAI,UAAW,SAASD,QAAO,GAAG;AACvC,MAAI,CAAC;AAEL,WAASE,SAAQ,GAAG;AAClB,WAAO,EAAE,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK;AAAA,EACvC;AAEA,EAAAA,SAAQ,YAAYF;AAEpB,SAAOE;AACT,EAAG,SAAS;AAEL,IAAI,YAAa,SAASF,QAAO,GAAG;AACzC,MAAI,CAAC;AAEL,WAASG,WAAU,GAAG;AACpB,aAAS,KAAK,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK;AAAA,EAC7F;AAEA,EAAAA,WAAU,YAAYH;AAEtB,SAAOG;AACT,EAAG,SAAS;;;AClCZ,IAAI,MAAM,IAAI,KAAK;AAAnB,IACI,YAAY;AADhB,IAEI,SAAS;AAEN,IAAI,YAAa,SAASC,QAAO,GAAG,GAAG;AAC5C,MAAI,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,EAAE,KAAK,KAAK;AAEpD,WAASC,WAAU,GAAG;AACpB,WAAO,IAAI,KAAK,EAAE,EAAE,CAAE,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,EAChD;AAEA,EAAAA,WAAU,YAAY,SAASC,IAAG;AAAE,WAAOF,QAAOE,IAAG,IAAI,GAAG;AAAA,EAAG;AAC/D,EAAAD,WAAU,SAAS,SAASE,IAAG;AAAE,WAAOH,QAAO,GAAGG,EAAC;AAAA,EAAG;AAEtD,SAAOF;AACT,EAAG,WAAW,MAAM;AAEb,IAAI,aAAc,SAASD,QAAO,GAAG,GAAG;AAC7C,MAAI,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,EAAE,KAAK,KAAK;AAEpD,WAASI,YAAW,GAAG;AACrB,WAAO,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,EACpD;AAEA,EAAAA,YAAW,YAAY,SAASF,IAAG;AAAE,WAAOF,QAAOE,IAAG,IAAI,GAAG;AAAA,EAAG;AAChE,EAAAE,YAAW,SAAS,SAASD,IAAG;AAAE,WAAOH,QAAO,GAAGG,EAAC;AAAA,EAAG;AAEvD,SAAOC;AACT,EAAG,WAAW,MAAM;AAEb,IAAI,eAAgB,SAASJ,QAAO,GAAG,GAAG;AAC/C,MAAI,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,EAAE,KAAK,KAAK;AAEpD,WAASK,cAAa,GAAG;AACvB,aAAS,IAAI,IAAI,IAAI,KAAK,IACpB,IAAI,KAAK,CAAC,CAAC,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC,IACnC,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC,KAAK;AAAA,EACnD;AAEA,EAAAA,cAAa,YAAY,SAASH,IAAG;AAAE,WAAOF,QAAOE,IAAG,IAAI,GAAG;AAAA,EAAG;AAClE,EAAAG,cAAa,SAAS,SAASF,IAAG;AAAE,WAAOH,QAAO,GAAGG,EAAC;AAAA,EAAG;AAEzD,SAAOE;AACT,EAAG,WAAW,MAAM;;;AC1CpB,IAAIC,QAAO,CAAC,IAAI;AAED,SAAR,eAAiB,MAAM,MAAM;AAClC,MAAI,YAAY,KAAK,cACjB,UACA;AAEJ,MAAI,WAAW;AACb,WAAO,QAAQ,OAAO,OAAO,OAAO;AACpC,SAAK,KAAK,WAAW;AACnB,WAAK,WAAW,UAAU,CAAC,GAAG,QAAQ,aAAa,SAAS,SAAS,MAAM;AACzE,eAAO,IAAI,WAAW,CAAC,CAAC,IAAI,CAAC,GAAGA,OAAM,MAAM,CAAC,CAAC;AAAA,MAChD;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;AClBe,SAARC,mBAAiB,MAAM;AAC5B,SAAO,KAAK,KAAK,WAAW;AAC1B,sBAAU,MAAM,IAAI;AAAA,EACtB,CAAC;AACH;;;ACDA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA;AAAA,EACN,OAAO;AAAA,EACP,UAAU;AAAA,EACV,MAAM;AACR;AAEA,SAAS,QAAQ,MAAMC,KAAI;AACzB,MAAI;AACJ,SAAO,EAAE,SAAS,KAAK,iBAAiB,EAAE,SAAS,OAAOA,GAAE,IAAI;AAC9D,QAAI,EAAE,OAAO,KAAK,aAAa;AAC7B,YAAM,IAAI,MAAM,cAAcA,GAAE,YAAY;AAAA,IAC9C;AAAA,EACF;AACA,SAAO;AACT;AAEe,SAARC,oBAAiB,MAAM;AAC5B,MAAID,KACA;AAEJ,MAAI,gBAAgB,YAAY;AAC9B,IAAAA,MAAK,KAAK,KAAK,OAAO,KAAK;AAAA,EAC7B,OAAO;AACL,IAAAA,MAAK,MAAM,IAAI,SAAS,eAAe,OAAO,IAAI,GAAG,OAAO,QAAQ,OAAO,OAAO,OAAO;AAAA,EAC3F;AAEA,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,yBAAS,MAAM,MAAMA,KAAI,GAAG,OAAO,UAAU,QAAQ,MAAMA,GAAE,CAAC;AAAA,MAChE;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,WAAW,QAAQ,KAAK,UAAU,MAAMA,GAAE;AACvD;;;ACrCA,kBAAU,UAAU,YAAYE;AAChC,kBAAU,UAAU,aAAaC;;;ACL1B,SAAS,UAAU,GAAG,GAAG,GAAG;AACjC,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACX;AAEA,UAAU,YAAY;AAAA,EACpB,aAAa;AAAA,EACb,OAAO,SAAS,GAAG;AACjB,WAAO,MAAM,IAAI,OAAO,IAAI,UAAU,KAAK,IAAI,GAAG,KAAK,GAAG,KAAK,CAAC;AAAA,EAClE;AAAA,EACA,WAAW,SAAS,GAAG,GAAG;AACxB,WAAO,MAAM,IAAI,MAAM,IAAI,OAAO,IAAI,UAAU,KAAK,GAAG,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,CAAC;AAAA,EAClG;AAAA,EACA,OAAO,SAAS,OAAO;AACrB,WAAO,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC;AAAA,EAChE;AAAA,EACA,QAAQ,SAAS,GAAG;AAClB,WAAO,IAAI,KAAK,IAAI,KAAK;AAAA,EAC3B;AAAA,EACA,QAAQ,SAAS,GAAG;AAClB,WAAO,IAAI,KAAK,IAAI,KAAK;AAAA,EAC3B;AAAA,EACA,QAAQ,SAAS,UAAU;AACzB,WAAO,EAAE,SAAS,CAAC,IAAI,KAAK,KAAK,KAAK,IAAI,SAAS,CAAC,IAAI,KAAK,KAAK,KAAK,CAAC;AAAA,EAC1E;AAAA,EACA,SAAS,SAAS,GAAG;AACnB,YAAQ,IAAI,KAAK,KAAK,KAAK;AAAA,EAC7B;AAAA,EACA,SAAS,SAAS,GAAG;AACnB,YAAQ,IAAI,KAAK,KAAK,KAAK;AAAA,EAC7B;AAAA,EACA,UAAU,SAAS,GAAG;AACpB,WAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,KAAK,SAAS,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AAAA,EAC3E;AAAA,EACA,UAAU,SAAS,GAAG;AACpB,WAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,KAAK,SAAS,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AAAA,EAC3E;AAAA,EACA,UAAU,WAAW;AACnB,WAAO,eAAe,KAAK,IAAI,MAAM,KAAK,IAAI,aAAa,KAAK,IAAI;AAAA,EACtE;AACF;AAEO,IAAI,WAAW,IAAI,UAAU,GAAG,GAAG,CAAC;AAE3C,UAAU,YAAY,UAAU;AAEjB,SAAR,UAA2B,MAAM;AACtC,SAAO,CAAC,KAAK,OAAQ,KAAI,EAAE,OAAO,KAAK,YAAa,QAAO;AAC3D,SAAO,KAAK;AACd;;;AClDA,IAAOC,oBAAQ,OAAK,MAAM;;;ACAX,SAAR,UAA2B,MAAM;AAAA,EACtC;AAAA,EACA;AAAA,EACA,WAAAC;AAAA,EACA,UAAAC;AACF,GAAG;AACD,SAAO,iBAAiB,MAAM;AAAA,IAC5B,MAAM,EAAC,OAAO,MAAM,YAAY,MAAM,cAAc,KAAI;AAAA,IACxD,aAAa,EAAC,OAAO,aAAa,YAAY,MAAM,cAAc,KAAI;AAAA,IACtE,QAAQ,EAAC,OAAO,QAAQ,YAAY,MAAM,cAAc,KAAI;AAAA,IAC5D,WAAW,EAAC,OAAOD,YAAW,YAAY,MAAM,cAAc,KAAI;AAAA,IAClE,GAAG,EAAC,OAAOC,UAAQ;AAAA,EACrB,CAAC;AACH;;;ACbO,SAASC,eAAc,OAAO;AACnC,QAAM,yBAAyB;AACjC;AAEe,SAARC,iBAAiB,OAAO;AAC7B,QAAM,eAAe;AACrB,QAAM,yBAAyB;AACjC;;;ACKA,SAASC,eAAc,OAAO;AAC5B,UAAQ,CAAC,MAAM,WAAW,MAAM,SAAS,YAAY,CAAC,MAAM;AAC9D;AAEA,SAAS,gBAAgB;AACvB,MAAI,IAAI;AACR,MAAI,aAAa,YAAY;AAC3B,QAAI,EAAE,mBAAmB;AACzB,QAAI,EAAE,aAAa,SAAS,GAAG;AAC7B,UAAI,EAAE,QAAQ;AACd,aAAO,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC;AAAA,IACrD;AACA,WAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,MAAM,QAAQ,OAAO,EAAE,OAAO,QAAQ,KAAK,CAAC;AAAA,EACjE;AACA,SAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,aAAa,EAAE,YAAY,CAAC;AACjD;AAEA,SAAS,mBAAmB;AAC1B,SAAO,KAAK,UAAU;AACxB;AAEA,SAAS,kBAAkB,OAAO;AAChC,SAAO,CAAC,MAAM,UAAU,MAAM,cAAc,IAAI,OAAO,MAAM,YAAY,IAAI,SAAU,MAAM,UAAU,KAAK;AAC9G;AAEA,SAASC,oBAAmB;AAC1B,SAAO,UAAU,kBAAmB,kBAAkB;AACxD;AAEA,SAAS,iBAAiBC,YAAW,QAAQ,iBAAiB;AAC5D,MAAI,MAAMA,WAAU,QAAQ,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,CAAC,EAAE,CAAC,GAC5D,MAAMA,WAAU,QAAQ,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,CAAC,EAAE,CAAC,GAC5D,MAAMA,WAAU,QAAQ,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,CAAC,EAAE,CAAC,GAC5D,MAAMA,WAAU,QAAQ,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,CAAC,EAAE,CAAC;AAChE,SAAOA,WAAU;AAAA,IACf,MAAM,OAAO,MAAM,OAAO,IAAI,KAAK,IAAI,GAAG,GAAG,KAAK,KAAK,IAAI,GAAG,GAAG;AAAA,IACjE,MAAM,OAAO,MAAM,OAAO,IAAI,KAAK,IAAI,GAAG,GAAG,KAAK,KAAK,IAAI,GAAG,GAAG;AAAA,EACnE;AACF;AAEe,SAARC,gBAAmB;AACxB,MAAIC,UAASJ,gBACT,SAAS,eACT,YAAY,kBACZ,aAAa,mBACb,YAAYC,mBACZ,cAAc,CAAC,GAAG,QAAQ,GAC1B,kBAAkB,CAAC,CAAC,WAAW,SAAS,GAAG,CAAC,UAAU,QAAQ,CAAC,GAC/D,WAAW,KACX,cAAc,cACd,YAAY,iBAAS,SAAS,QAAQ,KAAK,GAC3C,eACA,YACA,aACA,aAAa,KACb,aAAa,KACb,iBAAiB,GACjB,cAAc;AAElB,WAAS,KAAKI,YAAW;AACvB,IAAAA,WACK,SAAS,UAAU,gBAAgB,EACnC,GAAG,cAAc,SAAS,EAAC,SAAS,MAAK,CAAC,EAC1C,GAAG,kBAAkB,WAAW,EAChC,GAAG,iBAAiB,UAAU,EAChC,OAAO,SAAS,EACd,GAAG,mBAAmB,YAAY,EAClC,GAAG,kBAAkB,UAAU,EAC/B,GAAG,kCAAkC,UAAU,EAC/C,MAAM,+BAA+B,eAAe;AAAA,EAC3D;AAEA,OAAK,YAAY,SAAS,YAAYH,YAAW,OAAO,OAAO;AAC7D,QAAIG,aAAY,WAAW,YAAY,WAAW,UAAU,IAAI;AAChE,IAAAA,WAAU,SAAS,UAAU,gBAAgB;AAC7C,QAAI,eAAeA,YAAW;AAC5B,eAAS,YAAYH,YAAW,OAAO,KAAK;AAAA,IAC9C,OAAO;AACL,MAAAG,WAAU,UAAU,EAAE,KAAK,WAAW;AACpC,gBAAQ,MAAM,SAAS,EACpB,MAAM,KAAK,EACX,MAAM,EACN,KAAK,MAAM,OAAOH,eAAc,aAAaA,WAAU,MAAM,MAAM,SAAS,IAAIA,UAAS,EACzF,IAAI;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF;AAEA,OAAK,UAAU,SAASG,YAAW,GAAG,GAAG,OAAO;AAC9C,SAAK,QAAQA,YAAW,WAAW;AACjC,UAAI,KAAK,KAAK,OAAO,GACjB,KAAK,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI;AAC9D,aAAO,KAAK;AAAA,IACd,GAAG,GAAG,KAAK;AAAA,EACb;AAEA,OAAK,UAAU,SAASA,YAAW,GAAG,GAAG,OAAO;AAC9C,SAAK,UAAUA,YAAW,WAAW;AACnC,UAAI,IAAI,OAAO,MAAM,MAAM,SAAS,GAChC,KAAK,KAAK,QACV,KAAK,KAAK,OAAO,SAAS,CAAC,IAAI,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI,GACpF,KAAK,GAAG,OAAO,EAAE,GACjB,KAAK,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI;AAC9D,aAAO,UAAU,UAAU,MAAM,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,GAAG,eAAe;AAAA,IACvE,GAAG,GAAG,KAAK;AAAA,EACb;AAEA,OAAK,cAAc,SAASA,YAAW,GAAG,GAAG,OAAO;AAClD,SAAK,UAAUA,YAAW,WAAW;AACnC,aAAO,UAAU,KAAK,OAAO;AAAA,QAC3B,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI;AAAA,QACrD,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI;AAAA,MACvD,GAAG,OAAO,MAAM,MAAM,SAAS,GAAG,eAAe;AAAA,IACnD,GAAG,MAAM,KAAK;AAAA,EAChB;AAEA,OAAK,cAAc,SAASA,YAAW,GAAG,GAAG,GAAG,OAAO;AACrD,SAAK,UAAUA,YAAW,WAAW;AACnC,UAAI,IAAI,OAAO,MAAM,MAAM,SAAS,GAChC,IAAI,KAAK,QACT,KAAK,KAAK,OAAO,SAAS,CAAC,IAAI,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI;AACxF,aAAO,UAAU,SAAS,UAAU,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;AAAA,QAC3D,OAAO,MAAM,aAAa,CAAC,EAAE,MAAM,MAAM,SAAS,IAAI,CAAC;AAAA,QACvD,OAAO,MAAM,aAAa,CAAC,EAAE,MAAM,MAAM,SAAS,IAAI,CAAC;AAAA,MACzD,GAAG,GAAG,eAAe;AAAA,IACvB,GAAG,GAAG,KAAK;AAAA,EACb;AAEA,WAAS,MAAMH,YAAW,GAAG;AAC3B,QAAI,KAAK,IAAI,YAAY,CAAC,GAAG,KAAK,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC;AACxD,WAAO,MAAMA,WAAU,IAAIA,aAAY,IAAI,UAAU,GAAGA,WAAU,GAAGA,WAAU,CAAC;AAAA,EAClF;AAEA,WAAS,UAAUA,YAAW,IAAI,IAAI;AACpC,QAAI,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAIA,WAAU,GAAG,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAIA,WAAU;AACnE,WAAO,MAAMA,WAAU,KAAK,MAAMA,WAAU,IAAIA,aAAY,IAAI,UAAUA,WAAU,GAAG,GAAG,CAAC;AAAA,EAC7F;AAEA,WAAS,SAASI,SAAQ;AACxB,WAAO,EAAE,CAACA,QAAO,CAAC,EAAE,CAAC,IAAI,CAACA,QAAO,CAAC,EAAE,CAAC,KAAK,IAAI,CAACA,QAAO,CAAC,EAAE,CAAC,IAAI,CAACA,QAAO,CAAC,EAAE,CAAC,KAAK,CAAC;AAAA,EAClF;AAEA,WAAS,SAASC,aAAYL,YAAW,OAAO,OAAO;AACrD,IAAAK,YACK,GAAG,cAAc,WAAW;AAAE,cAAQ,MAAM,SAAS,EAAE,MAAM,KAAK,EAAE,MAAM;AAAA,IAAG,CAAC,EAC9E,GAAG,2BAA2B,WAAW;AAAE,cAAQ,MAAM,SAAS,EAAE,MAAM,KAAK,EAAE,IAAI;AAAA,IAAG,CAAC,EACzF,MAAM,QAAQ,WAAW;AACxB,UAAI,OAAO,MACP,OAAO,WACP,IAAI,QAAQ,MAAM,IAAI,EAAE,MAAM,KAAK,GACnC,IAAI,OAAO,MAAM,MAAM,IAAI,GAC3B,IAAI,SAAS,OAAO,SAAS,CAAC,IAAI,OAAO,UAAU,aAAa,MAAM,MAAM,MAAM,IAAI,IAAI,OAC1F,IAAI,KAAK,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,GACjD,IAAI,KAAK,QACT,IAAI,OAAOL,eAAc,aAAaA,WAAU,MAAM,MAAM,IAAI,IAAIA,YACpE,IAAI,YAAY,EAAE,OAAO,CAAC,EAAE,OAAO,IAAI,EAAE,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC;AAC5E,aAAO,SAAS,GAAG;AACjB,YAAI,MAAM,EAAG,KAAI;AAAA,aACZ;AAAE,cAAI,IAAI,EAAE,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;AAAG,cAAI,IAAI,UAAU,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC;AAAA,QAAG;AAC3F,UAAE,KAAK,MAAM,CAAC;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACP;AAEA,WAAS,QAAQ,MAAM,MAAM,OAAO;AAClC,WAAQ,CAAC,SAAS,KAAK,aAAc,IAAI,QAAQ,MAAM,IAAI;AAAA,EAC7D;AAEA,WAAS,QAAQ,MAAM,MAAM;AAC3B,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,SAAS,OAAO,MAAM,MAAM,IAAI;AACrC,SAAK,OAAO;AAAA,EACd;AAEA,UAAQ,YAAY;AAAA,IAClB,OAAO,SAAS,OAAO;AACrB,UAAI,MAAO,MAAK,cAAc;AAC9B,aAAO;AAAA,IACT;AAAA,IACA,OAAO,WAAW;AAChB,UAAI,EAAE,KAAK,WAAW,GAAG;AACvB,aAAK,KAAK,YAAY;AACtB,aAAK,KAAK,OAAO;AAAA,MACnB;AACA,aAAO;AAAA,IACT;AAAA,IACA,MAAM,SAAS,KAAKA,YAAW;AAC7B,UAAI,KAAK,SAAS,QAAQ,QAAS,MAAK,MAAM,CAAC,IAAIA,WAAU,OAAO,KAAK,MAAM,CAAC,CAAC;AACjF,UAAI,KAAK,UAAU,QAAQ,QAAS,MAAK,OAAO,CAAC,IAAIA,WAAU,OAAO,KAAK,OAAO,CAAC,CAAC;AACpF,UAAI,KAAK,UAAU,QAAQ,QAAS,MAAK,OAAO,CAAC,IAAIA,WAAU,OAAO,KAAK,OAAO,CAAC,CAAC;AACpF,WAAK,KAAK,SAASA;AACnB,WAAK,KAAK,MAAM;AAChB,aAAO;AAAA,IACT;AAAA,IACA,KAAK,WAAW;AACd,UAAI,EAAE,KAAK,WAAW,GAAG;AACvB,eAAO,KAAK,KAAK;AACjB,aAAK,KAAK,KAAK;AAAA,MACjB;AACA,aAAO;AAAA,IACT;AAAA,IACA,MAAM,SAAS,MAAM;AACnB,UAAI,IAAIM,gBAAO,KAAK,IAAI,EAAE,MAAM;AAChC,gBAAU;AAAA,QACR;AAAA,QACA,KAAK;AAAA,QACL,IAAI,UAAU,MAAM;AAAA,UAClB,aAAa,KAAK;AAAA,UAClB,QAAQ;AAAA,UACR;AAAA,UACA,WAAW,KAAK,KAAK;AAAA,UACrB,UAAU;AAAA,QACZ,CAAC;AAAA,QACD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,WAAS,QAAQ,UAAU,MAAM;AAC/B,QAAI,CAACJ,QAAO,MAAM,MAAM,SAAS,EAAG;AACpC,QAAI,IAAI,QAAQ,MAAM,IAAI,EAAE,MAAM,KAAK,GACnC,IAAI,KAAK,QACT,IAAI,KAAK,IAAI,YAAY,CAAC,GAAG,KAAK,IAAI,YAAY,CAAC,GAAG,EAAE,IAAI,KAAK,IAAI,GAAG,WAAW,MAAM,MAAM,SAAS,CAAC,CAAC,CAAC,GAC3G,IAAI,gBAAQ,KAAK;AAIrB,QAAI,EAAE,OAAO;AACX,UAAI,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG;AACpD,UAAE,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC;AAAA,MACtC;AACA,mBAAa,EAAE,KAAK;AAAA,IACtB,WAGS,EAAE,MAAM,EAAG;AAAA,SAGf;AACH,QAAE,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AACzB,wBAAU,IAAI;AACd,QAAE,MAAM;AAAA,IACV;AAEA,IAAAK,iBAAQ,KAAK;AACb,MAAE,QAAQ,WAAW,YAAY,UAAU;AAC3C,MAAE,KAAK,SAAS,UAAU,UAAU,MAAM,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,eAAe,CAAC;AAEpG,aAAS,aAAa;AACpB,QAAE,QAAQ;AACV,QAAE,IAAI;AAAA,IACR;AAAA,EACF;AAEA,WAAS,YAAY,UAAU,MAAM;AACnC,QAAI,eAAe,CAACL,QAAO,MAAM,MAAM,SAAS,EAAG;AACnD,QAAI,gBAAgB,MAAM,eACtB,IAAI,QAAQ,MAAM,MAAM,IAAI,EAAE,MAAM,KAAK,GACzC,IAAII,gBAAO,MAAM,IAAI,EAAE,GAAG,kBAAkB,YAAY,IAAI,EAAE,GAAG,gBAAgB,YAAY,IAAI,GACjG,IAAI,gBAAQ,OAAO,aAAa,GAChC,KAAK,MAAM,SACX,KAAK,MAAM;AAEf,mBAAY,MAAM,IAAI;AACtB,IAAAE,eAAc,KAAK;AACnB,MAAE,QAAQ,CAAC,GAAG,KAAK,OAAO,OAAO,CAAC,CAAC;AACnC,sBAAU,IAAI;AACd,MAAE,MAAM;AAER,aAAS,WAAWC,QAAO;AACzB,MAAAF,iBAAQE,MAAK;AACb,UAAI,CAAC,EAAE,OAAO;AACZ,YAAI,KAAKA,OAAM,UAAU,IAAI,KAAKA,OAAM,UAAU;AAClD,UAAE,QAAQ,KAAK,KAAK,KAAK,KAAK;AAAA,MAChC;AACA,QAAE,MAAMA,MAAK,EACX,KAAK,SAAS,UAAU,UAAU,EAAE,KAAK,QAAQ,EAAE,MAAM,CAAC,IAAI,gBAAQA,QAAO,aAAa,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,eAAe,CAAC;AAAA,IACxI;AAEA,aAAS,WAAWA,QAAO;AACzB,QAAE,GAAG,+BAA+B,IAAI;AACxC,cAAWA,OAAM,MAAM,EAAE,KAAK;AAC9B,MAAAF,iBAAQE,MAAK;AACb,QAAE,MAAMA,MAAK,EAAE,IAAI;AAAA,IACrB;AAAA,EACF;AAEA,WAAS,WAAW,UAAU,MAAM;AAClC,QAAI,CAACP,QAAO,MAAM,MAAM,SAAS,EAAG;AACpC,QAAI,KAAK,KAAK,QACV,KAAK,gBAAQ,MAAM,iBAAiB,MAAM,eAAe,CAAC,IAAI,OAAO,IAAI,GACzE,KAAK,GAAG,OAAO,EAAE,GACjB,KAAK,GAAG,KAAK,MAAM,WAAW,MAAM,IACpC,KAAK,UAAU,UAAU,MAAM,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,MAAM,MAAM,IAAI,GAAG,eAAe;AAE9F,IAAAK,iBAAQ,KAAK;AACb,QAAI,WAAW,EAAG,CAAAD,gBAAO,IAAI,EAAE,WAAW,EAAE,SAAS,QAAQ,EAAE,KAAK,UAAU,IAAI,IAAI,KAAK;AAAA,QACtF,CAAAA,gBAAO,IAAI,EAAE,KAAK,KAAK,WAAW,IAAI,IAAI,KAAK;AAAA,EACtD;AAEA,WAAS,aAAa,UAAU,MAAM;AACpC,QAAI,CAACJ,QAAO,MAAM,MAAM,SAAS,EAAG;AACpC,QAAI,UAAU,MAAM,SAChB,IAAI,QAAQ,QACZ,IAAI,QAAQ,MAAM,MAAM,MAAM,eAAe,WAAW,CAAC,EAAE,MAAM,KAAK,GACtE,SAAS,GAAG,GAAG;AAEnB,IAAAM,eAAc,KAAK;AACnB,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,UAAI,QAAQ,CAAC,GAAG,IAAI,gBAAQ,GAAG,IAAI;AACnC,UAAI,CAAC,GAAG,KAAK,OAAO,OAAO,CAAC,GAAG,EAAE,UAAU;AAC3C,UAAI,CAAC,EAAE,OAAQ,GAAE,SAAS,GAAG,UAAU,MAAM,EAAE,OAAO,IAAI,CAAC,CAAC;AAAA,eACnD,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,EAAG,GAAE,SAAS,GAAG,EAAE,OAAO;AAAA,IACrE;AAEA,QAAI,cAAe,iBAAgB,aAAa,aAAa;AAE7D,QAAI,SAAS;AACX,UAAI,EAAE,OAAO,EAAG,cAAa,EAAE,CAAC,GAAG,gBAAgB,WAAW,WAAW;AAAE,wBAAgB;AAAA,MAAM,GAAG,UAAU;AAC9G,wBAAU,IAAI;AACd,QAAE,MAAM;AAAA,IACV;AAAA,EACF;AAEA,WAAS,WAAW,UAAU,MAAM;AAClC,QAAI,CAAC,KAAK,UAAW;AACrB,QAAI,IAAI,QAAQ,MAAM,IAAI,EAAE,MAAM,KAAK,GACnC,UAAU,MAAM,gBAChB,IAAI,QAAQ,QAAQ,GAAG,GAAG,GAAG;AAEjC,IAAAD,iBAAQ,KAAK;AACb,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,UAAI,QAAQ,CAAC,GAAG,IAAI,gBAAQ,GAAG,IAAI;AACnC,UAAI,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,WAAY,GAAE,OAAO,CAAC,IAAI;AAAA,eACnD,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,WAAY,GAAE,OAAO,CAAC,IAAI;AAAA,IACnE;AACA,QAAI,EAAE,KAAK;AACX,QAAI,EAAE,QAAQ;AACZ,UAAI,KAAK,EAAE,OAAO,CAAC,GAAG,KAAK,EAAE,OAAO,CAAC,GACjC,KAAK,EAAE,OAAO,CAAC,GAAG,KAAK,EAAE,OAAO,CAAC,GACjC,MAAM,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,IACxD,MAAM,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK;AAC5D,UAAI,MAAM,GAAG,KAAK,KAAK,KAAK,EAAE,CAAC;AAC/B,UAAI,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC;AAC7C,UAAI,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC;AAAA,IAC/C,WACS,EAAE,OAAQ,KAAI,EAAE,OAAO,CAAC,GAAG,IAAI,EAAE,OAAO,CAAC;AAAA,QAC7C;AAEL,MAAE,KAAK,SAAS,UAAU,UAAU,GAAG,GAAG,CAAC,GAAG,EAAE,QAAQ,eAAe,CAAC;AAAA,EAC1E;AAEA,WAAS,WAAW,UAAU,MAAM;AAClC,QAAI,CAAC,KAAK,UAAW;AACrB,QAAI,IAAI,QAAQ,MAAM,IAAI,EAAE,MAAM,KAAK,GACnC,UAAU,MAAM,gBAChB,IAAI,QAAQ,QAAQ,GAAG;AAE3B,IAAAC,eAAc,KAAK;AACnB,QAAI,YAAa,cAAa,WAAW;AACzC,kBAAc,WAAW,WAAW;AAAE,oBAAc;AAAA,IAAM,GAAG,UAAU;AACvE,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,UAAI,QAAQ,CAAC;AACb,UAAI,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,WAAY,QAAO,EAAE;AAAA,eAC9C,EAAE,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,WAAY,QAAO,EAAE;AAAA,IAC9D;AACA,QAAI,EAAE,UAAU,CAAC,EAAE,OAAQ,GAAE,SAAS,EAAE,QAAQ,OAAO,EAAE;AACzD,QAAI,EAAE,OAAQ,GAAE,OAAO,CAAC,IAAI,KAAK,OAAO,OAAO,EAAE,OAAO,CAAC,CAAC;AAAA,SACrD;AACH,QAAE,IAAI;AAEN,UAAI,EAAE,SAAS,GAAG;AAChB,YAAI,gBAAQ,GAAG,IAAI;AACnB,YAAI,KAAK,MAAM,WAAW,CAAC,IAAI,EAAE,CAAC,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,aAAa;AACxE,cAAI,IAAIF,gBAAO,IAAI,EAAE,GAAG,eAAe;AACvC,cAAI,EAAG,GAAE,MAAM,MAAM,SAAS;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,OAAK,aAAa,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,aAAa,OAAO,MAAM,aAAa,IAAII,kBAAS,CAAC,CAAC,GAAG,QAAQ;AAAA,EAC9F;AAEA,OAAK,SAAS,SAAS,GAAG;AACxB,WAAO,UAAU,UAAUR,UAAS,OAAO,MAAM,aAAa,IAAIQ,kBAAS,CAAC,CAAC,CAAC,GAAG,QAAQR;AAAA,EAC3F;AAEA,OAAK,YAAY,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,YAAY,OAAO,MAAM,aAAa,IAAIQ,kBAAS,CAAC,CAAC,CAAC,GAAG,QAAQ;AAAA,EAC9F;AAEA,OAAK,SAAS,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,SAAS,OAAO,MAAM,aAAa,IAAIA,kBAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ;AAAA,EACpI;AAEA,OAAK,cAAc,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAAA,EACpH;AAEA,OAAK,kBAAkB,SAAS,GAAG;AACjC,WAAO,UAAU,UAAU,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA,EAC5Q;AAEA,OAAK,YAAY,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,YAAY,GAAG,QAAQ;AAAA,EACpD;AAEA,OAAK,WAAW,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,WAAW,CAAC,GAAG,QAAQ;AAAA,EACpD;AAEA,OAAK,cAAc,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,cAAc,GAAG,QAAQ;AAAA,EACtD;AAEA,OAAK,KAAK,WAAW;AACnB,QAAI,QAAQ,UAAU,GAAG,MAAM,WAAW,SAAS;AACnD,WAAO,UAAU,YAAY,OAAO;AAAA,EACtC;AAEA,OAAK,gBAAgB,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,kBAAkB,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK,KAAK,cAAc;AAAA,EAC5F;AAEA,OAAK,cAAc,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,cAAc,CAAC,GAAG,QAAQ;AAAA,EACvD;AAEA,SAAO;AACT;", "names": ["document", "datum", "selection", "create", "create", "parseTypenames", "window", "dispatch_default", "dispatch_default", "select_default", "select_default", "id", "selectAll_default", "root", "selection", "select_default", "constant_default", "dispatch", "filter", "selection", "select_default", "container", "dispatch", "event", "touch", "constant_default", "now", "callback", "delay", "time", "id", "get", "set", "start", "empty", "id", "set", "get", "transition", "attrRemove", "attrRemoveNS", "attrConstant", "attrConstantNS", "attrFunction", "attrFunctionNS", "attr_default", "id", "get", "id", "set", "get", "id", "set", "get", "id", "set", "filter_default", "merge_default", "transition", "id", "set", "on_default", "get", "id", "remove_default", "select_default", "id", "get", "selectAll_default", "id", "children", "inherit", "get", "Selection", "selection_default", "styleRemove", "styleConstant", "styleFunction", "id", "remove", "set", "style_default", "textConstant", "textFunction", "text_default", "inherit", "get", "id", "set", "id", "select_default", "selectAll_default", "filter_default", "merge_default", "selection_default", "on_default", "attr_default", "style_default", "text_default", "remove_default", "polyIn", "custom", "polyOut", "polyInOut", "custom", "backIn", "backOut", "backInOut", "custom", "elasticIn", "a", "p", "elasticOut", "elasticInOut", "root", "interrupt_default", "id", "transition_default", "interrupt_default", "transition_default", "constant_default", "transform", "dispatch", "nopropagation", "noevent_default", "defaultFilter", "defaultTouchable", "transform", "zoom_default", "filter", "selection", "extent", "transition", "select_default", "noevent_default", "nopropagation", "event", "constant_default"]}