/**
 * React hook for MCP server management
 * 
 * Provides comprehensive state management and API integration
 * for MCP server operations.
 */

import { useState, useEffect, useCallback } from 'react';
import { MCPServer, MCPServerCreate, MCPServerAction, MCPServerActionResponse } from '../types/mcp';
import { apiClient } from '../utils/apiClient';

interface UseMCPServersReturn {
  servers: MCPServer[];
  loading: boolean;
  error: string | null;
  createServer: (serverData: MCPServerCreate) => Promise<MCPServer>;
  updateServer: (serverId: string, updateData: Partial<MCPServer>) => Promise<MCPServer>;
  deleteServer: (serverId: string) => Promise<void>;
  performAction: (serverId: string, action: MCPServerAction) => Promise<MCPServerActionResponse>;
  refreshServers: () => Promise<void>;
  getServerById: (serverId: string) => MCPServer | undefined;
}

export const useMCPServers = (businessProfileId?: string): UseMCPServersReturn => {
  const [servers, setServers] = useState<MCPServer[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch servers from API
  const fetchServers = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      if (businessProfileId) {
        params.append('business_profile_id', businessProfileId);
      }

      const response = await apiClient.get(`/mcp-servers?${params.toString()}`);
      
      if (response.data.success !== false) {
        setServers(response.data.servers || []);
      } else {
        throw new Error(response.data.message || 'Failed to fetch servers');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to fetch MCP servers';
      setError(errorMessage);
      console.error('Error fetching MCP servers:', err);
    } finally {
      setLoading(false);
    }
  }, [businessProfileId]);

  // Create new server
  const createServer = useCallback(async (serverData: MCPServerCreate): Promise<MCPServer> => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.post('/mcp-servers', {
        ...serverData,
        business_profile_id: businessProfileId
      });

      if (response.data.success !== false) {
        const newServer = response.data;
        setServers(prev => [...prev, newServer]);
        return newServer;
      } else {
        throw new Error(response.data.message || 'Failed to create server');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to create server';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [businessProfileId]);

  // Update existing server
  const updateServer = useCallback(async (
    serverId: string, 
    updateData: Partial<MCPServer>
  ): Promise<MCPServer> => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.put(`/mcp-servers/${serverId}`, updateData);

      if (response.data.success !== false) {
        const updatedServer = response.data;
        setServers(prev => 
          prev.map(server => 
            server.id === serverId ? updatedServer : server
          )
        );
        return updatedServer;
      } else {
        throw new Error(response.data.message || 'Failed to update server');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to update server';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Delete server
  const deleteServer = useCallback(async (serverId: string): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.delete(`/mcp-servers/${serverId}`);

      if (response.data.success !== false) {
        setServers(prev => prev.filter(server => server.id !== serverId));
      } else {
        throw new Error(response.data.message || 'Failed to delete server');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to delete server';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Perform server action
  const performAction = useCallback(async (
    serverId: string, 
    action: MCPServerAction
  ): Promise<MCPServerActionResponse> => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.post(`/mcp-servers/${serverId}/actions`, {
        action
      });

      const result = response.data;

      // Update server status based on action result
      if (result.success) {
        setServers(prev => 
          prev.map(server => {
            if (server.id === serverId) {
              let newStatus = server.status;
              
              switch (action) {
                case 'start':
                  newStatus = 'active';
                  break;
                case 'stop':
                  newStatus = 'inactive';
                  break;
                case 'restart':
                  newStatus = 'active';
                  break;
                case 'test':
                  // Status doesn't change for test
                  break;
                case 'discover':
                  // Trigger a refresh to get updated capabilities
                  setTimeout(() => fetchServers(), 1000);
                  break;
              }

              return {
                ...server,
                status: newStatus,
                last_connected_at: action === 'start' || action === 'restart' 
                  ? new Date().toISOString() 
                  : server.last_connected_at
              };
            }
            return server;
          })
        );
      }

      return result;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Action failed';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [fetchServers]);

  // Refresh servers
  const refreshServers = useCallback(async () => {
    await fetchServers();
  }, [fetchServers]);

  // Get server by ID
  const getServerById = useCallback((serverId: string): MCPServer | undefined => {
    return servers.find(server => server.id === serverId);
  }, [servers]);

  // Initial fetch
  useEffect(() => {
    fetchServers();
  }, [fetchServers]);

  // Auto-refresh every 30 seconds for status updates
  useEffect(() => {
    const interval = setInterval(() => {
      if (!loading) {
        fetchServers();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [fetchServers, loading]);

  return {
    servers,
    loading,
    error,
    createServer,
    updateServer,
    deleteServer,
    performAction,
    refreshServers,
    getServerById
  };
};

// Hook for managing input variables
export const useMCPInputVariables = () => {
  const [variables, setVariables] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchVariables = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.get('/mcp-servers/input-variables');
      
      if (response.data.success !== false) {
        setVariables(response.data.variables || []);
      } else {
        throw new Error(response.data.message || 'Failed to fetch variables');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to fetch variables';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const createVariable = useCallback(async (variableData: any) => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.post('/mcp-servers/input-variables', variableData);

      if (response.data.success !== false) {
        const newVariable = response.data;
        setVariables(prev => [...prev, newVariable]);
        return newVariable;
      } else {
        throw new Error(response.data.message || 'Failed to create variable');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to create variable';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const updateVariable = useCallback(async (variableId: string, updateData: any) => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.put(`/mcp-servers/input-variables/${variableId}`, updateData);

      if (response.data.success !== false) {
        const updatedVariable = response.data;
        setVariables(prev => 
          prev.map(variable => 
            variable.id === variableId ? updatedVariable : variable
          )
        );
        return updatedVariable;
      } else {
        throw new Error(response.data.message || 'Failed to update variable');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to update variable';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteVariable = useCallback(async (variableId: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.delete(`/mcp-servers/input-variables/${variableId}`);

      if (response.data.success !== false) {
        setVariables(prev => prev.filter(variable => variable.id !== variableId));
      } else {
        throw new Error(response.data.message || 'Failed to delete variable');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to delete variable';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchVariables();
  }, [fetchVariables]);

  return {
    variables,
    loading,
    error,
    createVariable,
    updateVariable,
    deleteVariable,
    refreshVariables: fetchVariables
  };
};
