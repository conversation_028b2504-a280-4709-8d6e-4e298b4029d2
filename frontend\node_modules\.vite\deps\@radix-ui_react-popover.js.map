{"version": 3, "sources": ["../../@radix-ui/react-popover/src/Popover.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { Slot } from '@radix-ui/react-slot';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Popover\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPOVER_NAME = 'Popover';\n\ntype ScopedProps<P> = P & { __scopePopover?: Scope };\nconst [createPopoverContext, createPopoverScope] = createContextScope(POPOVER_NAME, [\n  createPopperScope,\n]);\nconst usePopperScope = createPopperScope();\n\ntype PopoverContextValue = {\n  triggerRef: React.RefObject<HTMLButtonElement>;\n  contentId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  hasCustomAnchor: boolean;\n  onCustomAnchorAdd(): void;\n  onCustomAnchorRemove(): void;\n  modal: boolean;\n};\n\nconst [PopoverProvider, usePopoverContext] =\n  createPopoverContext<PopoverContextValue>(POPOVER_NAME);\n\ninterface PopoverProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?: (open: boolean) => void;\n  modal?: boolean;\n}\n\nconst Popover: React.FC<PopoverProps> = (props: ScopedProps<PopoverProps>) => {\n  const {\n    __scopePopover,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = false,\n  } = props;\n  const popperScope = usePopperScope(__scopePopover);\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const [hasCustomAnchor, setHasCustomAnchor] = React.useState(false);\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: onOpenChange,\n  });\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <PopoverProvider\n        scope={__scopePopover}\n        contentId={useId()}\n        triggerRef={triggerRef}\n        open={open}\n        onOpenChange={setOpen}\n        onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n        hasCustomAnchor={hasCustomAnchor}\n        onCustomAnchorAdd={React.useCallback(() => setHasCustomAnchor(true), [])}\n        onCustomAnchorRemove={React.useCallback(() => setHasCustomAnchor(false), [])}\n        modal={modal}\n      >\n        {children}\n      </PopoverProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nPopover.displayName = POPOVER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopoverAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'PopoverAnchor';\n\ntype PopoverAnchorElement = React.ElementRef<typeof PopperPrimitive.Anchor>;\ntype PopperAnchorProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Anchor>;\ninterface PopoverAnchorProps extends PopperAnchorProps {}\n\nconst PopoverAnchor = React.forwardRef<PopoverAnchorElement, PopoverAnchorProps>(\n  (props: ScopedProps<PopoverAnchorProps>, forwardedRef) => {\n    const { __scopePopover, ...anchorProps } = props;\n    const context = usePopoverContext(ANCHOR_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    const { onCustomAnchorAdd, onCustomAnchorRemove } = context;\n\n    React.useEffect(() => {\n      onCustomAnchorAdd();\n      return () => onCustomAnchorRemove();\n    }, [onCustomAnchorAdd, onCustomAnchorRemove]);\n\n    return <PopperPrimitive.Anchor {...popperScope} {...anchorProps} ref={forwardedRef} />;\n  }\n);\n\nPopoverAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopoverTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'PopoverTrigger';\n\ntype PopoverTriggerElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface PopoverTriggerProps extends PrimitiveButtonProps {}\n\nconst PopoverTrigger = React.forwardRef<PopoverTriggerElement, PopoverTriggerProps>(\n  (props: ScopedProps<PopoverTriggerProps>, forwardedRef) => {\n    const { __scopePopover, ...triggerProps } = props;\n    const context = usePopoverContext(TRIGGER_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    const composedTriggerRef = useComposedRefs(forwardedRef, context.triggerRef);\n\n    const trigger = (\n      <Primitive.button\n        type=\"button\"\n        aria-haspopup=\"dialog\"\n        aria-expanded={context.open}\n        aria-controls={context.contentId}\n        data-state={getState(context.open)}\n        {...triggerProps}\n        ref={composedTriggerRef}\n        onClick={composeEventHandlers(props.onClick, context.onOpenToggle)}\n      />\n    );\n\n    return context.hasCustomAnchor ? (\n      trigger\n    ) : (\n      <PopperPrimitive.Anchor asChild {...popperScope}>\n        {trigger}\n      </PopperPrimitive.Anchor>\n    );\n  }\n);\n\nPopoverTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopoverPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'PopoverPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createPopoverContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface PopoverPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst PopoverPortal: React.FC<PopoverPortalProps> = (props: ScopedProps<PopoverPortalProps>) => {\n  const { __scopePopover, forceMount, children, container } = props;\n  const context = usePopoverContext(PORTAL_NAME, __scopePopover);\n  return (\n    <PortalProvider scope={__scopePopover} forceMount={forceMount}>\n      <Presence present={forceMount || context.open}>\n        <PortalPrimitive asChild container={container}>\n          {children}\n        </PortalPrimitive>\n      </Presence>\n    </PortalProvider>\n  );\n};\n\nPopoverPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopoverContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'PopoverContent';\n\ninterface PopoverContentProps extends PopoverContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst PopoverContent = React.forwardRef<PopoverContentTypeElement, PopoverContentProps>(\n  (props: ScopedProps<PopoverContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopePopover);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    return (\n      <Presence present={forceMount || context.open}>\n        {context.modal ? (\n          <PopoverContentModal {...contentProps} ref={forwardedRef} />\n        ) : (\n          <PopoverContentNonModal {...contentProps} ref={forwardedRef} />\n        )}\n      </Presence>\n    );\n  }\n);\n\nPopoverContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype PopoverContentTypeElement = PopoverContentImplElement;\ninterface PopoverContentTypeProps\n  extends Omit<PopoverContentImplProps, 'trapFocus' | 'disableOutsidePointerEvents'> {}\n\nconst PopoverContentModal = React.forwardRef<PopoverContentTypeElement, PopoverContentTypeProps>(\n  (props: ScopedProps<PopoverContentTypeProps>, forwardedRef) => {\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n    const isRightClickOutsideRef = React.useRef(false);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      const content = contentRef.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <RemoveScroll as={Slot} allowPinchZoom>\n        <PopoverContentImpl\n          {...props}\n          ref={composedRefs}\n          // we make sure we're not trapping once it's been closed\n          // (closed !== unmounted when animating out)\n          trapFocus={context.open}\n          disableOutsidePointerEvents\n          onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n            event.preventDefault();\n            if (!isRightClickOutsideRef.current) context.triggerRef.current?.focus();\n          })}\n          onPointerDownOutside={composeEventHandlers(\n            props.onPointerDownOutside,\n            (event) => {\n              const originalEvent = event.detail.originalEvent;\n              const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n              const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n\n              isRightClickOutsideRef.current = isRightClick;\n            },\n            { checkForDefaultPrevented: false }\n          )}\n          // When focus is trapped, a `focusout` event may still happen.\n          // We make sure we don't trigger our `onDismiss` in such case.\n          onFocusOutside={composeEventHandlers(\n            props.onFocusOutside,\n            (event) => event.preventDefault(),\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </RemoveScroll>\n    );\n  }\n);\n\nconst PopoverContentNonModal = React.forwardRef<PopoverContentTypeElement, PopoverContentTypeProps>(\n  (props: ScopedProps<PopoverContentTypeProps>, forwardedRef) => {\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    const hasInteractedOutsideRef = React.useRef(false);\n    const hasPointerDownOutsideRef = React.useRef(false);\n\n    return (\n      <PopoverContentImpl\n        {...props}\n        ref={forwardedRef}\n        trapFocus={false}\n        disableOutsidePointerEvents={false}\n        onCloseAutoFocus={(event) => {\n          props.onCloseAutoFocus?.(event);\n\n          if (!event.defaultPrevented) {\n            if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n            // Always prevent auto focus because we either focus manually or want user agent focus\n            event.preventDefault();\n          }\n\n          hasInteractedOutsideRef.current = false;\n          hasPointerDownOutsideRef.current = false;\n        }}\n        onInteractOutside={(event) => {\n          props.onInteractOutside?.(event);\n\n          if (!event.defaultPrevented) {\n            hasInteractedOutsideRef.current = true;\n            if (event.detail.originalEvent.type === 'pointerdown') {\n              hasPointerDownOutsideRef.current = true;\n            }\n          }\n\n          // Prevent dismissing when clicking the trigger.\n          // As the trigger is already setup to close, without doing so would\n          // cause it to close and immediately open.\n          const target = event.target as HTMLElement;\n          const targetIsTrigger = context.triggerRef.current?.contains(target);\n          if (targetIsTrigger) event.preventDefault();\n\n          // On Safari if the trigger is inside a container with tabIndex={0}, when clicked\n          // we will get the pointer down outside event on the trigger, but then a subsequent\n          // focus outside event on the container, we ignore any focus outside event when we've\n          // already had a pointer down outside event.\n          if (event.detail.originalEvent.type === 'focusin' && hasPointerDownOutsideRef.current) {\n            event.preventDefault();\n          }\n        }}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype PopoverContentImplElement = React.ElementRef<typeof PopperPrimitive.Content>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ninterface PopoverContentImplProps\n  extends Omit<PopperContentProps, 'onPlaced'>,\n    Omit<DismissableLayerProps, 'onDismiss'> {\n  /**\n   * Whether focus should be trapped within the `Popover`\n   * (default: false)\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n\n  /**\n   * Event handler called when auto-focusing on open.\n   * Can be prevented.\n   */\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n}\n\nconst PopoverContentImpl = React.forwardRef<PopoverContentImplElement, PopoverContentImplProps>(\n  (props: ScopedProps<PopoverContentImplProps>, forwardedRef) => {\n    const {\n      __scopePopover,\n      trapFocus,\n      onOpenAutoFocus,\n      onCloseAutoFocus,\n      disableOutsidePointerEvents,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      ...contentProps\n    } = props;\n    const context = usePopoverContext(CONTENT_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n\n    // Make sure the whole tree has focus guards as our `Popover` may be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    return (\n      <FocusScope\n        asChild\n        loop\n        trapped={trapFocus}\n        onMountAutoFocus={onOpenAutoFocus}\n        onUnmountAutoFocus={onCloseAutoFocus}\n      >\n        <DismissableLayer\n          asChild\n          disableOutsidePointerEvents={disableOutsidePointerEvents}\n          onInteractOutside={onInteractOutside}\n          onEscapeKeyDown={onEscapeKeyDown}\n          onPointerDownOutside={onPointerDownOutside}\n          onFocusOutside={onFocusOutside}\n          onDismiss={() => context.onOpenChange(false)}\n        >\n          <PopperPrimitive.Content\n            data-state={getState(context.open)}\n            role=\"dialog\"\n            id={context.contentId}\n            {...popperScope}\n            {...contentProps}\n            ref={forwardedRef}\n            style={{\n              ...contentProps.style,\n              // re-namespace exposed content custom properties\n              ...{\n                '--radix-popover-content-transform-origin': 'var(--radix-popper-transform-origin)',\n                '--radix-popover-content-available-width': 'var(--radix-popper-available-width)',\n                '--radix-popover-content-available-height': 'var(--radix-popper-available-height)',\n                '--radix-popover-trigger-width': 'var(--radix-popper-anchor-width)',\n                '--radix-popover-trigger-height': 'var(--radix-popper-anchor-height)',\n              },\n            }}\n          />\n        </DismissableLayer>\n      </FocusScope>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * PopoverClose\n * -----------------------------------------------------------------------------------------------*/\n\nconst CLOSE_NAME = 'PopoverClose';\n\ntype PopoverCloseElement = React.ElementRef<typeof Primitive.button>;\ninterface PopoverCloseProps extends PrimitiveButtonProps {}\n\nconst PopoverClose = React.forwardRef<PopoverCloseElement, PopoverCloseProps>(\n  (props: ScopedProps<PopoverCloseProps>, forwardedRef) => {\n    const { __scopePopover, ...closeProps } = props;\n    const context = usePopoverContext(CLOSE_NAME, __scopePopover);\n    return (\n      <Primitive.button\n        type=\"button\"\n        {...closeProps}\n        ref={forwardedRef}\n        onClick={composeEventHandlers(props.onClick, () => context.onOpenChange(false))}\n      />\n    );\n  }\n);\n\nPopoverClose.displayName = CLOSE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopoverArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'PopoverArrow';\n\ntype PopoverArrowElement = React.ElementRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface PopoverArrowProps extends PopperArrowProps {}\n\nconst PopoverArrow = React.forwardRef<PopoverArrowElement, PopoverArrowProps>(\n  (props: ScopedProps<PopoverArrowProps>, forwardedRef) => {\n    const { __scopePopover, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopePopover);\n    return <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nPopoverArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst Root = Popover;\nconst Anchor = PopoverAnchor;\nconst Trigger = PopoverTrigger;\nconst Portal = PopoverPortal;\nconst Content = PopoverContent;\nconst Close = PopoverClose;\nconst Arrow = PopoverArrow;\n\nexport {\n  createPopoverScope,\n  //\n  Popover,\n  PopoverAnchor,\n  PopoverTrigger,\n  PopoverPortal,\n  PopoverContent,\n  PopoverClose,\n  PopoverArrow,\n  //\n  Root,\n  Anchor,\n  Trigger,\n  Portal,\n  Content,\n  Close,\n  Arrow,\n};\nexport type {\n  PopoverProps,\n  PopoverAnchorProps,\n  PopoverTriggerProps,\n  PopoverPortalProps,\n  PopoverContentProps,\n  PopoverCloseProps,\n  PopoverArrowProps,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAuB;AA2EjB,yBAAA;AAnDN,IAAM,eAAe;AAGrB,IAAM,CAAC,sBAAsB,kBAAkB,IAAI,mBAAmB,cAAc;EAClF;AACF,CAAC;AACD,IAAM,iBAAiB,kBAAkB;AAczC,IAAM,CAAC,iBAAiB,iBAAiB,IACvC,qBAA0C,YAAY;AAUxD,IAAM,UAAkC,CAAC,UAAqC;AAC5E,QAAM;IACJ;IACA;IACA,MAAM;IACN;IACA;IACA,QAAQ;EACV,IAAI;AACJ,QAAM,cAAc,eAAe,cAAc;AACjD,QAAM,aAAmB,aAA0B,IAAI;AACvD,QAAM,CAAC,iBAAiB,kBAAkB,IAAU,eAAS,KAAK;AAClE,QAAM,CAAC,OAAO,OAAO,OAAO,IAAI,qBAAqB;IACnD,MAAM;IACN,aAAa;IACb,UAAU;EACZ,CAAC;AAED,aACE,wBAAiB,OAAhB,EAAsB,GAAG,aACxB,cAAA;IAAC;IAAA;MACC,OAAO;MACP,WAAW,MAAM;MACjB;MACA;MACA,cAAc;MACd,cAAoB,kBAAY,MAAM,QAAQ,CAAC,aAAa,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC;MACjF;MACA,mBAAyB,kBAAY,MAAM,mBAAmB,IAAI,GAAG,CAAC,CAAC;MACvE,sBAA4B,kBAAY,MAAM,mBAAmB,KAAK,GAAG,CAAC,CAAC;MAC3E;MAEC;IAAA;EACH,EAAA,CACF;AAEJ;AAEA,QAAQ,cAAc;AAMtB,IAAM,cAAc;AAMpB,IAAM,gBAAsB;EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM,EAAE,gBAAgB,GAAG,YAAY,IAAI;AAC3C,UAAM,UAAU,kBAAkB,aAAa,cAAc;AAC7D,UAAM,cAAc,eAAe,cAAc;AACjD,UAAM,EAAE,mBAAmB,qBAAqB,IAAI;AAE9C,IAAA,gBAAU,MAAM;AACpB,wBAAkB;AAClB,aAAO,MAAM,qBAAqB;IACpC,GAAG,CAAC,mBAAmB,oBAAoB,CAAC;AAE5C,eAAO,wBAAiB,QAAhB,EAAwB,GAAG,aAAc,GAAG,aAAa,KAAK,aAAA,CAAc;EACtF;AACF;AAEA,cAAc,cAAc;AAM5B,IAAM,eAAe;AAMrB,IAAM,iBAAuB;EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,EAAE,gBAAgB,GAAG,aAAa,IAAI;AAC5C,UAAM,UAAU,kBAAkB,cAAc,cAAc;AAC9D,UAAM,cAAc,eAAe,cAAc;AACjD,UAAM,qBAAqB,gBAAgB,cAAc,QAAQ,UAAU;AAE3E,UAAM,cACJ;MAAC,UAAU;MAAV;QACC,MAAK;QACL,iBAAc;QACd,iBAAe,QAAQ;QACvB,iBAAe,QAAQ;QACvB,cAAY,SAAS,QAAQ,IAAI;QAChC,GAAG;QACJ,KAAK;QACL,SAAS,qBAAqB,MAAM,SAAS,QAAQ,YAAY;MAAA;IACnE;AAGF,WAAO,QAAQ,kBACb,cAEA,wBAAiB,QAAhB,EAAuB,SAAO,MAAE,GAAG,aACjC,UAAA,QAAA,CACH;EAEJ;AACF;AAEA,eAAe,cAAc;AAM7B,IAAM,cAAc;AAGpB,IAAM,CAAC,gBAAgB,gBAAgB,IAAI,qBAAyC,aAAa;EAC/F,YAAY;AACd,CAAC;AAgBD,IAAM,gBAA8C,CAAC,UAA2C;AAC9F,QAAM,EAAE,gBAAgB,YAAY,UAAU,UAAU,IAAI;AAC5D,QAAM,UAAU,kBAAkB,aAAa,cAAc;AAC7D,aACE,wBAAC,gBAAA,EAAe,OAAO,gBAAgB,YACrC,cAAA,wBAAC,UAAA,EAAS,SAAS,cAAc,QAAQ,MACvC,cAAA,wBAAC,QAAA,EAAgB,SAAO,MAAC,WACtB,SAAA,CACH,EAAA,CACF,EAAA,CACF;AAEJ;AAEA,cAAc,cAAc;AAM5B,IAAM,eAAe;AAUrB,IAAM,iBAAuB;EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,gBAAgB,iBAAiB,cAAc,MAAM,cAAc;AACzE,UAAM,EAAE,aAAa,cAAc,YAAY,GAAG,aAAa,IAAI;AACnE,UAAM,UAAU,kBAAkB,cAAc,MAAM,cAAc;AACpE,eACE,wBAAC,UAAA,EAAS,SAAS,cAAc,QAAQ,MACtC,UAAA,QAAQ,YACP,wBAAC,qBAAA,EAAqB,GAAG,cAAc,KAAK,aAAA,CAAc,QAE1D,wBAAC,wBAAA,EAAwB,GAAG,cAAc,KAAK,aAAA,CAAc,EAAA,CAEjE;EAEJ;AACF;AAEA,eAAe,cAAc;AAQ7B,IAAM,sBAA4B;EAChC,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,UAAU,kBAAkB,cAAc,MAAM,cAAc;AACpE,UAAM,aAAmB,aAAuB,IAAI;AACpD,UAAM,eAAe,gBAAgB,cAAc,UAAU;AAC7D,UAAM,yBAA+B,aAAO,KAAK;AAG3C,IAAA,gBAAU,MAAM;AACpB,YAAM,UAAU,WAAW;AAC3B,UAAI,QAAS,QAAO,WAAW,OAAO;IACxC,GAAG,CAAC,CAAC;AAEL,eACE,wBAAC,qBAAA,EAAa,IAAI,MAAM,gBAAc,MACpC,cAAA;MAAC;MAAA;QACE,GAAG;QACJ,KAAK;QAGL,WAAW,QAAQ;QACnB,6BAA2B;QAC3B,kBAAkB,qBAAqB,MAAM,kBAAkB,CAAC,UAAU;;AACxE,gBAAM,eAAe;AACrB,cAAI,CAAC,uBAAuB,QAAS,eAAQ,WAAW,YAAnB,mBAA4B;QACnE,CAAC;QACD,sBAAsB;UACpB,MAAM;UACN,CAAC,UAAU;AACT,kBAAM,gBAAgB,MAAM,OAAO;AACnC,kBAAM,gBAAgB,cAAc,WAAW,KAAK,cAAc,YAAY;AAC9E,kBAAM,eAAe,cAAc,WAAW,KAAK;AAEnD,mCAAuB,UAAU;UACnC;UACA,EAAE,0BAA0B,MAAM;QACpC;QAGA,gBAAgB;UACd,MAAM;UACN,CAAC,UAAU,MAAM,eAAe;UAChC,EAAE,0BAA0B,MAAM;QACpC;MAAA;IACF,EAAA,CACF;EAEJ;AACF;AAEA,IAAM,yBAA+B;EACnC,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,UAAU,kBAAkB,cAAc,MAAM,cAAc;AACpE,UAAM,0BAAgC,aAAO,KAAK;AAClD,UAAM,2BAAiC,aAAO,KAAK;AAEnD,eACE;MAAC;MAAA;QACE,GAAG;QACJ,KAAK;QACL,WAAW;QACX,6BAA6B;QAC7B,kBAAkB,CAAC,UAAU;;AAC3B,sBAAM,qBAAN,+BAAyB;AAEzB,cAAI,CAAC,MAAM,kBAAkB;AAC3B,gBAAI,CAAC,wBAAwB,QAAS,eAAQ,WAAW,YAAnB,mBAA4B;AAElE,kBAAM,eAAe;UACvB;AAEA,kCAAwB,UAAU;AAClC,mCAAyB,UAAU;QACrC;QACA,mBAAmB,CAAC,UAAU;;AAC5B,sBAAM,sBAAN,+BAA0B;AAE1B,cAAI,CAAC,MAAM,kBAAkB;AAC3B,oCAAwB,UAAU;AAClC,gBAAI,MAAM,OAAO,cAAc,SAAS,eAAe;AACrD,uCAAyB,UAAU;YACrC;UACF;AAKA,gBAAM,SAAS,MAAM;AACrB,gBAAM,mBAAkB,aAAQ,WAAW,YAAnB,mBAA4B,SAAS;AAC7D,cAAI,gBAAiB,OAAM,eAAe;AAM1C,cAAI,MAAM,OAAO,cAAc,SAAS,aAAa,yBAAyB,SAAS;AACrF,kBAAM,eAAe;UACvB;QACF;MAAA;IACF;EAEJ;AACF;AA8BA,IAAM,qBAA2B;EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,GAAG;IACL,IAAI;AACJ,UAAM,UAAU,kBAAkB,cAAc,cAAc;AAC9D,UAAM,cAAc,eAAe,cAAc;AAIjD,mBAAe;AAEf,eACE;MAAC;MAAA;QACC,SAAO;QACP,MAAI;QACJ,SAAS;QACT,kBAAkB;QAClB,oBAAoB;QAEpB,cAAA;UAAC;UAAA;YACC,SAAO;YACP;YACA;YACA;YACA;YACA;YACA,WAAW,MAAM,QAAQ,aAAa,KAAK;YAE3C,cAAA;cAAiB;cAAhB;gBACC,cAAY,SAAS,QAAQ,IAAI;gBACjC,MAAK;gBACL,IAAI,QAAQ;gBACX,GAAG;gBACH,GAAG;gBACJ,KAAK;gBACL,OAAO;kBACL,GAAG,aAAa;;kBAEhB,GAAG;oBACD,4CAA4C;oBAC5C,2CAA2C;oBAC3C,4CAA4C;oBAC5C,iCAAiC;oBACjC,kCAAkC;kBACpC;gBACF;cAAA;YACF;UAAA;QACF;MAAA;IACF;EAEJ;AACF;AAMA,IAAM,aAAa;AAKnB,IAAM,eAAqB;EACzB,CAAC,OAAuC,iBAAiB;AACvD,UAAM,EAAE,gBAAgB,GAAG,WAAW,IAAI;AAC1C,UAAM,UAAU,kBAAkB,YAAY,cAAc;AAC5D,eACE;MAAC,UAAU;MAAV;QACC,MAAK;QACJ,GAAG;QACJ,KAAK;QACL,SAAS,qBAAqB,MAAM,SAAS,MAAM,QAAQ,aAAa,KAAK,CAAC;MAAA;IAChF;EAEJ;AACF;AAEA,aAAa,cAAc;AAM3B,IAAM,aAAa;AAMnB,IAAM,eAAqB;EACzB,CAAC,OAAuC,iBAAiB;AACvD,UAAM,EAAE,gBAAgB,GAAG,WAAW,IAAI;AAC1C,UAAM,cAAc,eAAe,cAAc;AACjD,eAAO,wBAAiB,OAAhB,EAAuB,GAAG,aAAc,GAAG,YAAY,KAAK,aAAA,CAAc;EACpF;AACF;AAEA,aAAa,cAAc;AAI3B,SAAS,SAAS,MAAe;AAC/B,SAAO,OAAO,SAAS;AACzB;AAEA,IAAMA,SAAO;AACb,IAAMC,UAAS;AACf,IAAM,UAAU;AAChB,IAAMC,UAAS;AACf,IAAMC,WAAU;AAChB,IAAM,QAAQ;AACd,IAAMC,SAAQ;", "names": ["Root", "<PERSON><PERSON>", "Portal", "Content", "Arrow"]}