"""
Enhanced Marketplace Service for Datagenius Backend.

This module provides service functions for interacting with the enhanced
marketplace database models including persona configurations, agent plugins,
hierarchical messaging, and workflow adaptations.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
import uuid

from ..models.enhanced_marketplace import (
    PersonaConfiguration,
    AgentPlugin,
    MessageThread,
    MessageEditHistory,
    ConversationResubmission,
    IndustryTemplate,
    WorkflowAdaptation,
    WorkflowExperiment,
    ExperimentParticipation,
    WorkflowPerformanceMetrics
)

logger = logging.getLogger(__name__)


class PersonaConfigurationService:
    """Service for managing persona configurations."""
    
    @staticmethod
    def create_persona_configuration(
        db: Session,
        persona_id: str,
        configuration: Dict[str, Any],
        industry_specialization: Optional[str] = None,
        methodology_framework: Optional[str] = None,
        **kwargs
    ) -> PersonaConfiguration:
        """Create a new persona configuration."""
        try:
            config = PersonaConfiguration(
                persona_id=persona_id,
                configuration=configuration,
                industry_specialization=industry_specialization,
                methodology_framework=methodology_framework,
                **kwargs
            )
            db.add(config)
            db.commit()
            db.refresh(config)
            logger.info(f"Created persona configuration for {persona_id}")
            return config
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating persona configuration: {e}")
            raise
    
    @staticmethod
    def get_persona_configuration(
        db: Session,
        persona_id: str
    ) -> Optional[PersonaConfiguration]:
        """Get persona configuration by persona ID."""
        return db.query(PersonaConfiguration).filter(
            PersonaConfiguration.persona_id == persona_id
        ).first()
    
    @staticmethod
    def update_persona_configuration(
        db: Session,
        persona_id: str,
        updates: Dict[str, Any]
    ) -> Optional[PersonaConfiguration]:
        """Update persona configuration."""
        try:
            config = db.query(PersonaConfiguration).filter(
                PersonaConfiguration.persona_id == persona_id
            ).first()
            
            if not config:
                return None
            
            for key, value in updates.items():
                if hasattr(config, key):
                    setattr(config, key, value)
            
            config.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(config)
            logger.info(f"Updated persona configuration for {persona_id}")
            return config
        except Exception as e:
            db.rollback()
            logger.error(f"Error updating persona configuration: {e}")
            raise


class AgentPluginService:
    """Service for managing agent plugins."""
    
    @staticmethod
    def register_plugin(
        db: Session,
        plugin_id: str,
        name: str,
        version: str,
        plugin_package: Dict[str, Any],
        **kwargs
    ) -> AgentPlugin:
        """Register a new agent plugin."""
        try:
            plugin = AgentPlugin(
                plugin_id=plugin_id,
                name=name,
                version=version,
                plugin_package=plugin_package,
                **kwargs
            )
            db.add(plugin)
            db.commit()
            db.refresh(plugin)
            logger.info(f"Registered plugin {plugin_id} v{version}")
            return plugin
        except Exception as e:
            db.rollback()
            logger.error(f"Error registering plugin: {e}")
            raise
    
    @staticmethod
    def get_plugin(db: Session, plugin_id: str) -> Optional[AgentPlugin]:
        """Get plugin by plugin ID."""
        return db.query(AgentPlugin).filter(
            AgentPlugin.plugin_id == plugin_id
        ).first()
    
    @staticmethod
    def get_plugins_by_status(
        db: Session,
        status: str,
        limit: int = 100
    ) -> List[AgentPlugin]:
        """Get plugins by status."""
        return db.query(AgentPlugin).filter(
            AgentPlugin.status == status
        ).limit(limit).all()
    
    @staticmethod
    def update_plugin_status(
        db: Session,
        plugin_id: str,
        status: str
    ) -> Optional[AgentPlugin]:
        """Update plugin status."""
        try:
            plugin = db.query(AgentPlugin).filter(
                AgentPlugin.plugin_id == plugin_id
            ).first()
            
            if not plugin:
                return None
            
            plugin.status = status
            plugin.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(plugin)
            logger.info(f"Updated plugin {plugin_id} status to {status}")
            return plugin
        except Exception as e:
            db.rollback()
            logger.error(f"Error updating plugin status: {e}")
            raise


class HierarchicalMessageService:
    """Service for managing hierarchical messages."""
    
    @staticmethod
    def create_message_thread(
        db: Session,
        conversation_id: str,
        message_content: Dict[str, Any],
        message_type: str,
        created_by: int,
        parent_message_id: Optional[str] = None,
        **kwargs
    ) -> MessageThread:
        """Create a new message thread."""
        try:
            message = MessageThread(
                conversation_id=conversation_id,
                parent_message_id=parent_message_id,
                message_content=message_content,
                message_type=message_type,
                created_by=created_by,
                **kwargs
            )
            db.add(message)
            db.commit()
            db.refresh(message)
            logger.info(f"Created message thread {message.id}")
            return message
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating message thread: {e}")
            raise
    
    @staticmethod
    def get_conversation_threads(
        db: Session,
        conversation_id: str,
        limit: int = 100
    ) -> List[MessageThread]:
        """Get message threads for a conversation."""
        return db.query(MessageThread).filter(
            MessageThread.conversation_id == conversation_id
        ).order_by(MessageThread.created_at).limit(limit).all()
    
    @staticmethod
    def edit_message(
        db: Session,
        message_id: str,
        new_content: Dict[str, Any],
        edited_by: int,
        edit_reason: Optional[str] = None
    ) -> Tuple[MessageThread, MessageEditHistory]:
        """Edit a message and create edit history."""
        try:
            # Get original message
            original_message = db.query(MessageThread).filter(
                MessageThread.id == message_id
            ).first()
            
            if not original_message:
                raise ValueError(f"Message {message_id} not found")
            
            # Create new message with edited content
            edited_message = MessageThread(
                conversation_id=original_message.conversation_id,
                parent_message_id=original_message.parent_message_id,
                message_content=new_content,
                message_type=original_message.message_type,
                created_by=edited_by,
                message_status='active'
            )
            db.add(edited_message)
            db.flush()  # Get the ID
            
            # Create edit history
            edit_history = MessageEditHistory(
                edit_id=str(uuid.uuid4()),
                original_message_id=original_message.id,
                edited_message_id=edited_message.id,
                edit_type='content_edit',
                edited_by=edited_by,
                edit_reason=edit_reason,
                diff_data={
                    'original_content': original_message.message_content,
                    'new_content': new_content
                }
            )
            db.add(edit_history)
            
            # Mark original message as edited
            original_message.message_status = 'edited'
            original_message.updated_at = datetime.utcnow()
            
            db.commit()
            db.refresh(edited_message)
            db.refresh(edit_history)
            
            logger.info(f"Edited message {message_id}")
            return edited_message, edit_history
        except Exception as e:
            db.rollback()
            logger.error(f"Error editing message: {e}")
            raise


class WorkflowAdaptationService:
    """Service for managing workflow adaptations."""
    
    @staticmethod
    def create_adaptation(
        db: Session,
        workflow_id: str,
        persona_id: str,
        user_id: int,
        adaptation_type: str,
        **kwargs
    ) -> WorkflowAdaptation:
        """Create a new workflow adaptation."""
        try:
            adaptation = WorkflowAdaptation(
                adaptation_id=str(uuid.uuid4()),
                workflow_id=workflow_id,
                persona_id=persona_id,
                user_id=user_id,
                adaptation_type=adaptation_type,
                **kwargs
            )
            db.add(adaptation)
            db.commit()
            db.refresh(adaptation)
            logger.info(f"Created workflow adaptation {adaptation.adaptation_id}")
            return adaptation
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating workflow adaptation: {e}")
            raise
    
    @staticmethod
    def get_user_adaptations(
        db: Session,
        user_id: int,
        persona_id: Optional[str] = None,
        limit: int = 50
    ) -> List[WorkflowAdaptation]:
        """Get workflow adaptations for a user."""
        query = db.query(WorkflowAdaptation).filter(
            WorkflowAdaptation.user_id == user_id
        )
        
        if persona_id:
            query = query.filter(WorkflowAdaptation.persona_id == persona_id)
        
        return query.order_by(desc(WorkflowAdaptation.created_at)).limit(limit).all()


# Create service instances
persona_config_service = PersonaConfigurationService()
agent_plugin_service = AgentPluginService()
hierarchical_message_service = HierarchicalMessageService()
workflow_adaptation_service = WorkflowAdaptationService()
