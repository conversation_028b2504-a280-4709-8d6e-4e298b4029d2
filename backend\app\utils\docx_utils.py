"""
DOCX conversion utilities for the Datagenius backend.

This module provides utilities for converting markdown content to DOCX format,
replacing the legacy marketing agent utils functionality.
"""

import logging
import re
from io import Bytes<PERSON>
from typing import Optional, Union
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE

logger = logging.getLogger(__name__)


def convert_to_docx(content: str, title: Optional[str] = "Generated Content") -> bytes:
    """
    Convert markdown content to DOCX format.
    
    Args:
        content: Markdown content to convert
        title: Optional title for the document
        
    Returns:
        bytes: DOCX document as bytes
        
    Raises:
        Exception: If conversion fails
    """
    try:
        # Create a new document
        doc = Document()
        
        # Set document title
        if title:
            doc.core_properties.title = title
            
        # Add title as heading
        if title:
            title_paragraph = doc.add_heading(title, level=1)
            title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
        # Process the markdown content
        _process_markdown_content(doc, content)
        
        # Save to BytesIO
        doc_buffer = BytesIO()
        doc.save(doc_buffer)
        doc_buffer.seek(0)
        
        return doc_buffer.getvalue()
        
    except Exception as e:
        logger.error(f"Error converting content to DOCX: {str(e)}", exc_info=True)
        raise


def _process_markdown_content(doc: Document, content: str) -> None:
    """
    Process markdown content and add it to the document.
    
    Args:
        doc: Document object to add content to
        content: Markdown content to process
    """
    lines = content.split('\n')
    current_list = None
    
    for line in lines:
        line = line.strip()
        
        if not line:
            # Add empty paragraph for spacing
            doc.add_paragraph()
            current_list = None
            continue
            
        # Handle headers
        if line.startswith('#'):
            current_list = None
            level = len(line) - len(line.lstrip('#'))
            header_text = line.lstrip('#').strip()
            if level <= 6:
                doc.add_heading(header_text, level=level)
            else:
                # Treat as regular paragraph if too many #
                doc.add_paragraph(header_text)
                
        # Handle bullet points
        elif line.startswith('- ') or line.startswith('* '):
            current_list = None
            bullet_text = line[2:].strip()
            paragraph = doc.add_paragraph(bullet_text, style='List Bullet')
            
        # Handle numbered lists
        elif re.match(r'^\d+\.\s', line):
            current_list = None
            numbered_text = re.sub(r'^\d+\.\s', '', line)
            paragraph = doc.add_paragraph(numbered_text, style='List Number')
            
        # Handle bold text
        elif '**' in line:
            current_list = None
            paragraph = doc.add_paragraph()
            _add_formatted_text(paragraph, line)
            
        # Handle italic text
        elif '*' in line and '**' not in line:
            current_list = None
            paragraph = doc.add_paragraph()
            _add_formatted_text(paragraph, line)
            
        # Handle code blocks
        elif line.startswith('```'):
            current_list = None
            # Skip code block markers for now
            continue
            
        # Handle regular paragraphs
        else:
            current_list = None
            if line:
                paragraph = doc.add_paragraph()
                _add_formatted_text(paragraph, line)


def _add_formatted_text(paragraph, text: str) -> None:
    """
    Add formatted text to a paragraph, handling bold and italic formatting.
    
    Args:
        paragraph: Paragraph object to add text to
        text: Text with markdown formatting
    """
    # Handle bold text (**text**)
    parts = re.split(r'(\*\*.*?\*\*)', text)
    
    for part in parts:
        if part.startswith('**') and part.endswith('**'):
            # Bold text
            bold_text = part[2:-2]
            run = paragraph.add_run(bold_text)
            run.bold = True
        elif '*' in part and not part.startswith('**'):
            # Handle italic text (*text*)
            italic_parts = re.split(r'(\*.*?\*)', part)
            for italic_part in italic_parts:
                if italic_part.startswith('*') and italic_part.endswith('*') and not italic_part.startswith('**'):
                    italic_text = italic_part[1:-1]
                    run = paragraph.add_run(italic_text)
                    run.italic = True
                else:
                    paragraph.add_run(italic_part)
        else:
            paragraph.add_run(part)


def create_marketing_document(content: str, title: str = "Marketing Content") -> bytes:
    """
    Create a formatted marketing document from content.
    
    Args:
        content: Marketing content to format
        title: Document title
        
    Returns:
        bytes: Formatted DOCX document
    """
    try:
        doc = Document()
        
        # Set document properties
        doc.core_properties.title = title
        doc.core_properties.author = "Datagenius Marketing AI"
        
        # Add title with custom formatting
        title_paragraph = doc.add_heading(title, level=1)
        title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add a subtitle or date
        subtitle = doc.add_paragraph("Generated by Datagenius Marketing AI")
        subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
        subtitle_run = subtitle.runs[0]
        subtitle_run.italic = True
        subtitle_run.font.size = Pt(12)
        
        # Add spacing
        doc.add_paragraph()
        
        # Process the content
        _process_markdown_content(doc, content)
        
        # Save to bytes
        doc_buffer = BytesIO()
        doc.save(doc_buffer)
        doc_buffer.seek(0)
        
        return doc_buffer.getvalue()
        
    except Exception as e:
        logger.error(f"Error creating marketing document: {str(e)}", exc_info=True)
        raise


def add_table_to_document(doc: Document, data: list, headers: Optional[list] = None) -> None:
    """
    Add a table to the document.
    
    Args:
        doc: Document object
        data: List of lists containing table data
        headers: Optional list of column headers
    """
    if not data:
        return
        
    # Determine table dimensions
    rows = len(data) + (1 if headers else 0)
    cols = len(data[0]) if data else 0
    
    if cols == 0:
        return
        
    # Create table
    table = doc.add_table(rows=rows, cols=cols)
    table.style = 'Table Grid'
    
    # Add headers if provided
    if headers:
        header_row = table.rows[0]
        for i, header in enumerate(headers[:cols]):
            header_row.cells[i].text = str(header)
            # Make header bold
            for paragraph in header_row.cells[i].paragraphs:
                for run in paragraph.runs:
                    run.bold = True
        
        # Add data starting from row 1
        for i, row_data in enumerate(data):
            table_row = table.rows[i + 1]
            for j, cell_data in enumerate(row_data[:cols]):
                table_row.cells[j].text = str(cell_data)
    else:
        # Add data starting from row 0
        for i, row_data in enumerate(data):
            table_row = table.rows[i]
            for j, cell_data in enumerate(row_data[:cols]):
                table_row.cells[j].text = str(cell_data)
