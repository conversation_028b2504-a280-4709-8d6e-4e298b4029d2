"""
Phase 4 Error Handling Module

Comprehensive error handling, validation, and monitoring for Phase 4 components.
"""

import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum
from datetime import datetime
import traceback


class Phase4ErrorCode(Enum):
    """Error codes for Phase 4 components."""
    
    # General errors
    INITIALIZATION_FAILED = "P4_INIT_001"
    CONFIGURATION_ERROR = "P4_CONFIG_002"
    DATABASE_ERROR = "P4_DB_003"
    VALIDATION_ERROR = "P4_VALID_004"
    
    # Marketplace errors
    MARKETPLACE_LISTING_FAILED = "P4_MKT_001"
    MARKETPLACE_REQUEST_FAILED = "P4_MKT_002"
    MARKETPLACE_DISCOVERY_FAILED = "P4_MKT_003"
    MARKETPLACE_PERFORMANCE_ERROR = "P4_MKT_004"
    
    # Trading engine errors
    TRADING_INITIATION_FAILED = "P4_TRADE_001"
    TRADING_BID_FAILED = "P4_TRADE_002"
    TRADING_SELECTION_FAILED = "P4_TRADE_003"
    TRADING_EXECUTION_FAILED = "P4_TRADE_004"
    
    # Certification errors
    CERTIFICATION_REQUEST_FAILED = "P4_CERT_001"
    CERTIFICATION_PROCESS_FAILED = "P4_CERT_002"
    CERTIFICATION_VALIDATION_FAILED = "P4_CERT_003"
    CERTIFICATION_EXPIRY_ERROR = "P4_CERT_004"
    
    # AI Composer errors
    WORKFLOW_COMPOSITION_FAILED = "P4_COMP_001"
    WORKFLOW_COMPILATION_FAILED = "P4_COMP_002"
    WORKFLOW_OPTIMIZATION_FAILED = "P4_COMP_003"
    WORKFLOW_PATTERN_ERROR = "P4_COMP_004"
    
    # Pattern Recognition errors
    PATTERN_LEARNING_FAILED = "P4_PATT_001"
    PATTERN_RECOGNITION_FAILED = "P4_PATT_002"
    PATTERN_SIMILARITY_ERROR = "P4_PATT_003"
    PATTERN_OPTIMIZATION_FAILED = "P4_PATT_004"


@dataclass
class Phase4Error:
    """Structured error information for Phase 4 components."""
    
    error_code: Phase4ErrorCode
    message: str
    component: str
    operation: str
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = None
    traceback_info: Optional[str] = None
    severity: str = "error"  # error, warning, critical
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        
        if self.traceback_info is None:
            self.traceback_info = traceback.format_exc()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary for logging/storage."""
        return {
            "error_code": self.error_code.value,
            "message": self.message,
            "component": self.component,
            "operation": self.operation,
            "details": self.details or {},
            "timestamp": self.timestamp.isoformat(),
            "traceback": self.traceback_info,
            "severity": self.severity
        }


class Phase4ErrorHandler:
    """Centralized error handling for Phase 4 components."""
    
    def __init__(self, component_name: str):
        self.component_name = component_name
        self.logger = logging.getLogger(f"phase4.{component_name}")
        self.error_history: List[Phase4Error] = []
        self.max_history = 1000  # Keep last 1000 errors
    
    def handle_error(self, error_code: Phase4ErrorCode, message: str, 
                    operation: str, details: Optional[Dict[str, Any]] = None,
                    severity: str = "error", raise_exception: bool = True) -> Phase4Error:
        """Handle and log an error."""
        
        error = Phase4Error(
            error_code=error_code,
            message=message,
            component=self.component_name,
            operation=operation,
            details=details,
            severity=severity
        )
        
        # Log the error
        log_level = getattr(logging, severity.upper(), logging.ERROR)
        self.logger.log(log_level, f"[{error_code.value}] {message}", extra={
            "error_details": error.to_dict()
        })
        
        # Store in history
        self.error_history.append(error)
        if len(self.error_history) > self.max_history:
            self.error_history = self.error_history[-self.max_history:]
        
        # Raise exception if requested
        if raise_exception and severity == "error":
            raise Phase4Exception(error)
        
        return error
    
    def handle_validation_error(self, field: str, value: Any, expected: str,
                              operation: str = "validation") -> Phase4Error:
        """Handle validation errors with standardized format."""
        return self.handle_error(
            error_code=Phase4ErrorCode.VALIDATION_ERROR,
            message=f"Validation failed for field '{field}': expected {expected}, got {type(value).__name__}",
            operation=operation,
            details={
                "field": field,
                "value": str(value),
                "expected": expected,
                "actual_type": type(value).__name__
            }
        )
    
    def handle_database_error(self, operation: str, query: str = None, 
                            error: Exception = None) -> Phase4Error:
        """Handle database errors with context."""
        return self.handle_error(
            error_code=Phase4ErrorCode.DATABASE_ERROR,
            message=f"Database operation failed: {operation}",
            operation=operation,
            details={
                "query": query,
                "original_error": str(error) if error else None,
                "error_type": type(error).__name__ if error else None
            }
        )
    
    def get_error_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get error summary for the last N hours."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_errors = [
            error for error in self.error_history
            if error.timestamp >= cutoff_time
        ]
        
        # Group by error code
        error_counts = {}
        severity_counts = {"error": 0, "warning": 0, "critical": 0}
        
        for error in recent_errors:
            error_counts[error.error_code.value] = error_counts.get(error.error_code.value, 0) + 1
            severity_counts[error.severity] = severity_counts.get(error.severity, 0) + 1
        
        return {
            "total_errors": len(recent_errors),
            "error_counts": error_counts,
            "severity_counts": severity_counts,
            "most_common_errors": sorted(error_counts.items(), key=lambda x: x[1], reverse=True)[:5],
            "time_period_hours": hours
        }


class Phase4Exception(Exception):
    """Custom exception for Phase 4 errors."""
    
    def __init__(self, error: Phase4Error):
        self.error = error
        super().__init__(f"[{error.error_code.value}] {error.message}")


class Phase4Validator:
    """Validation utilities for Phase 4 components."""
    
    @staticmethod
    def validate_capability_data(data: Dict[str, Any], error_handler: Phase4ErrorHandler) -> bool:
        """Validate capability listing data."""
        required_fields = ["capability_id", "agent_id", "name", "category"]
        
        for field in required_fields:
            if field not in data or not data[field]:
                error_handler.handle_validation_error(
                    field=field,
                    value=data.get(field),
                    expected="non-empty string",
                    operation="capability_validation"
                )
                return False
        
        # Validate price
        if "price" in data:
            price = data["price"]
            if not isinstance(price, (int, float)) or price < 0:
                error_handler.handle_validation_error(
                    field="price",
                    value=price,
                    expected="non-negative number",
                    operation="capability_validation"
                )
                return False
        
        # Validate performance score
        if "performance_score" in data:
            score = data["performance_score"]
            if not isinstance(score, (int, float)) or not (0 <= score <= 1):
                error_handler.handle_validation_error(
                    field="performance_score",
                    value=score,
                    expected="number between 0 and 1",
                    operation="capability_validation"
                )
                return False
        
        return True
    
    @staticmethod
    def validate_trade_data(data: Dict[str, Any], error_handler: Phase4ErrorHandler) -> bool:
        """Validate trade data."""
        required_fields = ["trade_id", "requester_id", "budget"]
        
        for field in required_fields:
            if field not in data or data[field] is None:
                error_handler.handle_validation_error(
                    field=field,
                    value=data.get(field),
                    expected="non-null value",
                    operation="trade_validation"
                )
                return False
        
        # Validate budget
        budget = data["budget"]
        if not isinstance(budget, (int, float)) or budget <= 0:
            error_handler.handle_validation_error(
                field="budget",
                value=budget,
                expected="positive number",
                operation="trade_validation"
            )
            return False
        
        return True
    
    @staticmethod
    def validate_workflow_structure(structure: Dict[str, Any], error_handler: Phase4ErrorHandler) -> bool:
        """Validate workflow structure."""
        if not isinstance(structure, dict):
            error_handler.handle_validation_error(
                field="workflow_structure",
                value=structure,
                expected="dictionary",
                operation="workflow_validation"
            )
            return False
        
        # Check for required fields
        if "nodes" not in structure or not isinstance(structure["nodes"], list):
            error_handler.handle_validation_error(
                field="nodes",
                value=structure.get("nodes"),
                expected="list of nodes",
                operation="workflow_validation"
            )
            return False
        
        if "edges" not in structure or not isinstance(structure["edges"], list):
            error_handler.handle_validation_error(
                field="edges",
                value=structure.get("edges"),
                expected="list of edges",
                operation="workflow_validation"
            )
            return False
        
        # Validate nodes have required fields
        for i, node in enumerate(structure["nodes"]):
            if not isinstance(node, dict) or "id" not in node:
                error_handler.handle_validation_error(
                    field=f"nodes[{i}]",
                    value=node,
                    expected="dictionary with 'id' field",
                    operation="workflow_validation"
                )
                return False
        
        return True


# Global error handlers for each component
marketplace_error_handler = Phase4ErrorHandler("marketplace")
trading_error_handler = Phase4ErrorHandler("trading_engine")
certification_error_handler = Phase4ErrorHandler("certification_system")
composer_error_handler = Phase4ErrorHandler("workflow_composer")
pattern_error_handler = Phase4ErrorHandler("pattern_recognition")


def get_error_handler(component: str) -> Phase4ErrorHandler:
    """Get error handler for a specific component."""
    handlers = {
        "marketplace": marketplace_error_handler,
        "trading_engine": trading_error_handler,
        "certification_system": certification_error_handler,
        "workflow_composer": composer_error_handler,
        "pattern_recognition": pattern_error_handler
    }
    
    return handlers.get(component, Phase4ErrorHandler(component))


def get_phase4_error_summary() -> Dict[str, Any]:
    """Get comprehensive error summary across all Phase 4 components."""
    components = ["marketplace", "trading_engine", "certification_system", 
                 "workflow_composer", "pattern_recognition"]
    
    summary = {
        "total_errors": 0,
        "component_summaries": {},
        "overall_health": "healthy"
    }
    
    for component in components:
        handler = get_error_handler(component)
        component_summary = handler.get_error_summary()
        summary["component_summaries"][component] = component_summary
        summary["total_errors"] += component_summary["total_errors"]
    
    # Determine overall health
    if summary["total_errors"] > 100:
        summary["overall_health"] = "critical"
    elif summary["total_errors"] > 50:
        summary["overall_health"] = "degraded"
    elif summary["total_errors"] > 10:
        summary["overall_health"] = "warning"
    
    return summary
