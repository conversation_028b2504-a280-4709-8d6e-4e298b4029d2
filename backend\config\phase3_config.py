"""
Phase 3 AI-powered systems configuration for Datagenius.

This module provides configuration for Phase 3 AI enhancements including
predictive optimization, self-healing systems, and advanced collaboration patterns.
"""

import os
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class OptimizationLevel(Enum):
    """Optimization levels for Phase 3 systems."""
    BASIC = "basic"
    STANDARD = "standard"
    ADVANCED = "advanced"
    MAXIMUM = "maximum"


class HealingStrategy(Enum):
    """Self-healing strategies."""
    CONSERVATIVE = "conservative"
    BALANCED = "balanced"
    AGGRESSIVE = "aggressive"


@dataclass
class Phase3Config:
    """Configuration for Phase 3 AI-powered systems."""
    
    # Global Phase 3 Settings
    enable_phase3: bool = True
    optimization_level: OptimizationLevel = OptimizationLevel.STANDARD
    debug_mode: bool = False
    
    # Predictive Optimization
    enable_predictive_optimization: bool = True
    prediction_confidence_threshold: float = 0.7
    optimization_suggestion_limit: int = 5
    pattern_learning_enabled: bool = True
    ab_testing_enabled: bool = True
    model_training_interval_hours: int = 24
    
    # Self-Healing System
    enable_self_healing: bool = False  # Temporarily disabled to reduce noise
    healing_strategy: HealingStrategy = HealingStrategy.BALANCED
    failure_prediction_enabled: bool = True
    automatic_remediation: bool = True
    healing_retry_attempts: int = 3
    health_check_interval_seconds: int = 30
    
    # Advanced Collaboration
    enable_advanced_collaboration: bool = True
    team_formation_enabled: bool = True
    skill_matching_enabled: bool = True
    collaborative_learning_enabled: bool = True
    consensus_building_enabled: bool = True
    max_team_size: int = 5
    collaboration_timeout_seconds: int = 300
    
    # Performance Monitoring
    enable_performance_monitoring: bool = True
    metrics_collection_interval: int = 60
    performance_history_retention_days: int = 30
    alert_thresholds: Dict[str, float] = None
    
    # Machine Learning Models
    model_storage_path: str = "model_artifacts/phase3"
    enable_model_persistence: bool = True
    model_update_frequency_hours: int = 6
    training_data_retention_days: int = 90
    
    # Event System
    enable_event_driven_optimization: bool = True
    event_processing_batch_size: int = 100
    event_queue_max_size: int = 10000
    
    # Resource Management
    max_concurrent_optimizations: int = 10
    optimization_timeout_seconds: int = 120
    memory_limit_mb: int = 1024
    cpu_usage_limit_percent: float = 80.0
    
    def __post_init__(self):
        """Initialize default alert thresholds if not provided."""
        if self.alert_thresholds is None:
            self.alert_thresholds = {
                "workflow_failure_rate": 0.1,  # 10%
                "average_execution_time_increase": 0.2,  # 20%
                "system_memory_usage": 0.85,  # 85%
                "prediction_accuracy": 0.7,  # 70%
                "healing_success_rate": 0.8  # 80%
            }


def load_phase3_config() -> Phase3Config:
    """
    Load Phase 3 configuration from environment variables.
    
    Returns:
        Phase3Config: Configuration object
    """
    config = Phase3Config()
    
    # Global Settings
    config.enable_phase3 = os.getenv("ENABLE_PHASE3", "true").lower() == "true"
    config.optimization_level = OptimizationLevel(os.getenv("PHASE3_OPTIMIZATION_LEVEL", "standard"))
    config.debug_mode = os.getenv("PHASE3_DEBUG_MODE", "false").lower() == "true"
    
    # Predictive Optimization
    config.enable_predictive_optimization = os.getenv("ENABLE_PREDICTIVE_OPTIMIZATION", "true").lower() == "true"
    config.prediction_confidence_threshold = float(os.getenv("PREDICTION_CONFIDENCE_THRESHOLD", "0.7"))
    config.optimization_suggestion_limit = int(os.getenv("OPTIMIZATION_SUGGESTION_LIMIT", "5"))
    config.pattern_learning_enabled = os.getenv("PATTERN_LEARNING_ENABLED", "true").lower() == "true"
    config.ab_testing_enabled = os.getenv("AB_TESTING_ENABLED", "true").lower() == "true"
    config.model_training_interval_hours = int(os.getenv("MODEL_TRAINING_INTERVAL_HOURS", "24"))
    
    # Self-Healing System
    config.enable_self_healing = os.getenv("ENABLE_SELF_HEALING", "false").lower() == "true"
    config.healing_strategy = HealingStrategy(os.getenv("HEALING_STRATEGY", "balanced"))
    config.failure_prediction_enabled = os.getenv("FAILURE_PREDICTION_ENABLED", "true").lower() == "true"
    config.automatic_remediation = os.getenv("AUTOMATIC_REMEDIATION", "true").lower() == "true"
    config.healing_retry_attempts = int(os.getenv("HEALING_RETRY_ATTEMPTS", "3"))
    config.health_check_interval_seconds = int(os.getenv("HEALTH_CHECK_INTERVAL_SECONDS", "30"))
    
    # Advanced Collaboration
    config.enable_advanced_collaboration = os.getenv("ENABLE_ADVANCED_COLLABORATION", "true").lower() == "true"
    config.team_formation_enabled = os.getenv("TEAM_FORMATION_ENABLED", "true").lower() == "true"
    config.skill_matching_enabled = os.getenv("SKILL_MATCHING_ENABLED", "true").lower() == "true"
    config.collaborative_learning_enabled = os.getenv("COLLABORATIVE_LEARNING_ENABLED", "true").lower() == "true"
    config.consensus_building_enabled = os.getenv("CONSENSUS_BUILDING_ENABLED", "true").lower() == "true"
    config.max_team_size = int(os.getenv("MAX_TEAM_SIZE", "5"))
    config.collaboration_timeout_seconds = int(os.getenv("COLLABORATION_TIMEOUT_SECONDS", "300"))
    
    # Performance Monitoring
    config.enable_performance_monitoring = os.getenv("ENABLE_PERFORMANCE_MONITORING", "true").lower() == "true"
    config.metrics_collection_interval = int(os.getenv("METRICS_COLLECTION_INTERVAL", "60"))
    config.performance_history_retention_days = int(os.getenv("PERFORMANCE_HISTORY_RETENTION_DAYS", "30"))
    
    # Machine Learning Models
    config.model_storage_path = os.getenv("MODEL_STORAGE_PATH", "model_artifacts/phase3")
    config.enable_model_persistence = os.getenv("ENABLE_MODEL_PERSISTENCE", "true").lower() == "true"
    config.model_update_frequency_hours = int(os.getenv("MODEL_UPDATE_FREQUENCY_HOURS", "6"))
    config.training_data_retention_days = int(os.getenv("TRAINING_DATA_RETENTION_DAYS", "90"))
    
    # Event System
    config.enable_event_driven_optimization = os.getenv("ENABLE_EVENT_DRIVEN_OPTIMIZATION", "true").lower() == "true"
    config.event_processing_batch_size = int(os.getenv("EVENT_PROCESSING_BATCH_SIZE", "100"))
    config.event_queue_max_size = int(os.getenv("EVENT_QUEUE_MAX_SIZE", "10000"))
    
    # Resource Management
    config.max_concurrent_optimizations = int(os.getenv("MAX_CONCURRENT_OPTIMIZATIONS", "10"))
    config.optimization_timeout_seconds = int(os.getenv("OPTIMIZATION_TIMEOUT_SECONDS", "120"))
    config.memory_limit_mb = int(os.getenv("MEMORY_LIMIT_MB", "1024"))
    config.cpu_usage_limit_percent = float(os.getenv("CPU_USAGE_LIMIT_PERCENT", "80.0"))
    
    logger.info("Phase 3 configuration loaded")
    logger.info(f"Phase 3 Enabled: {config.enable_phase3}")
    logger.info(f"Optimization Level: {config.optimization_level.value}")
    logger.info(f"Predictive Optimization: {config.enable_predictive_optimization}")
    logger.info(f"Self-Healing: {config.enable_self_healing}")
    logger.info(f"Advanced Collaboration: {config.enable_advanced_collaboration}")
    
    return config


def get_optimization_config() -> Dict[str, Any]:
    """
    Get predictive optimization configuration.
    
    Returns:
        Dict containing optimization configuration
    """
    config = load_phase3_config()
    
    return {
        "enabled": config.enable_predictive_optimization,
        "confidence_threshold": config.prediction_confidence_threshold,
        "suggestion_limit": config.optimization_suggestion_limit,
        "pattern_learning": config.pattern_learning_enabled,
        "ab_testing": config.ab_testing_enabled,
        "training_interval": config.model_training_interval_hours,
        "model_storage": config.model_storage_path,
        "timeout": config.optimization_timeout_seconds
    }


def get_healing_config() -> Dict[str, Any]:
    """
    Get self-healing system configuration.
    
    Returns:
        Dict containing healing configuration
    """
    config = load_phase3_config()
    
    return {
        "enabled": config.enable_self_healing,
        "strategy": config.healing_strategy.value,
        "failure_prediction": config.failure_prediction_enabled,
        "automatic_remediation": config.automatic_remediation,
        "retry_attempts": config.healing_retry_attempts,
        "health_check_interval": config.health_check_interval_seconds,
        "alert_thresholds": config.alert_thresholds
    }


def get_collaboration_config() -> Dict[str, Any]:
    """
    Get advanced collaboration configuration.
    
    Returns:
        Dict containing collaboration configuration
    """
    config = load_phase3_config()
    
    return {
        "enabled": config.enable_advanced_collaboration,
        "team_formation": config.team_formation_enabled,
        "skill_matching": config.skill_matching_enabled,
        "collaborative_learning": config.collaborative_learning_enabled,
        "consensus_building": config.consensus_building_enabled,
        "max_team_size": config.max_team_size,
        "timeout": config.collaboration_timeout_seconds
    }


def get_monitoring_config() -> Dict[str, Any]:
    """
    Get performance monitoring configuration.
    
    Returns:
        Dict containing monitoring configuration
    """
    config = load_phase3_config()
    
    return {
        "enabled": config.enable_performance_monitoring,
        "collection_interval": config.metrics_collection_interval,
        "retention_days": config.performance_history_retention_days,
        "alert_thresholds": config.alert_thresholds,
        "debug_mode": config.debug_mode
    }


def get_resource_limits() -> Dict[str, Any]:
    """
    Get resource management limits.
    
    Returns:
        Dict containing resource limits
    """
    config = load_phase3_config()
    
    return {
        "max_concurrent_optimizations": config.max_concurrent_optimizations,
        "optimization_timeout": config.optimization_timeout_seconds,
        "memory_limit_mb": config.memory_limit_mb,
        "cpu_limit_percent": config.cpu_usage_limit_percent,
        "event_queue_size": config.event_queue_max_size
    }


# Global configuration instance
_phase3_config: Optional[Phase3Config] = None


def get_phase3_config() -> Phase3Config:
    """
    Get the global Phase 3 configuration.
    
    Returns:
        Phase3Config: Global configuration instance
    """
    global _phase3_config
    if _phase3_config is None:
        _phase3_config = load_phase3_config()
    return _phase3_config


def reload_phase3_config() -> Phase3Config:
    """
    Reload the Phase 3 configuration.
    
    Returns:
        Phase3Config: Reloaded configuration instance
    """
    global _phase3_config
    _phase3_config = load_phase3_config()
    return _phase3_config


def is_phase3_enabled() -> bool:
    """
    Check if Phase 3 AI systems are enabled.
    
    Returns:
        bool: True if Phase 3 is enabled, False otherwise
    """
    config = get_phase3_config()
    return config.enable_phase3


def is_component_enabled(component_name: str) -> bool:
    """
    Check if a specific Phase 3 component is enabled.
    
    Args:
        component_name: Name of the component to check
        
    Returns:
        bool: True if component is enabled, False otherwise
    """
    config = get_phase3_config()
    component_map = {
        "predictive_optimization": config.enable_predictive_optimization,
        "self_healing": config.enable_self_healing,
        "advanced_collaboration": config.enable_advanced_collaboration,
        "performance_monitoring": config.enable_performance_monitoring,
        "event_driven_optimization": config.enable_event_driven_optimization
    }
    return component_map.get(component_name, False)


# Export commonly used configurations
__all__ = [
    "Phase3Config",
    "OptimizationLevel",
    "HealingStrategy",
    "load_phase3_config",
    "get_phase3_config",
    "reload_phase3_config",
    "is_phase3_enabled",
    "is_component_enabled",
    "get_optimization_config",
    "get_healing_config",
    "get_collaboration_config",
    "get_monitoring_config",
    "get_resource_limits"
]
