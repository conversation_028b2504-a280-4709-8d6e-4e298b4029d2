#!/usr/bin/env python3
"""
Simple validation script for user-centric LangGraph routing architecture changes.

This script validates the core routing logic changes without requiring
full application initialization.
"""

import sys
import os
import logging
from typing import Dict, Any, Optional
from datetime import datetime
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

# Mock the required enums and types
class ConversationMode(Enum):
    INITIAL = "initial"
    CONVERSATION = "conversation"
    AGENT_SWITCH = "agent_switch"
    HANDOFF = "handoff"

# Mock UnifiedDatageniusState type
UnifiedDatageniusState = Dict[str, Any]

def create_unified_state(
    user_id: str,
    conversation_id: str,
    selected_agent: Optional[str] = None,
    conversation_mode: ConversationMode = ConversationMode.INITIAL
) -> UnifiedDatageniusState:
    """Create a mock unified state for testing."""
    return {
        "user_id": user_id,
        "conversation_id": conversation_id,
        "selected_agent": selected_agent,
        "conversation_mode": conversation_mode,
        "current_agent": None,
        "agent_command": None,
        "routing_analysis": None,
        "messages": [],
        "updated_at": datetime.now().isoformat()
    }

def get_routing_target(state: UnifiedDatageniusState) -> Optional[str]:
    """
    Simplified version of the updated routing logic for testing.
    This mirrors the changes made to unified_state.py.
    """
    # Validate state parameter
    if state is None or not isinstance(state, dict):
        logger.warning("🚨 get_routing_target: Invalid state parameter")
        return None

    # Priority 1: ALWAYS respect user's selected agent
    selected_agent = state.get("selected_agent")
    if selected_agent:
        logger.info(f"✅ get_routing_target: Using selected agent: {selected_agent}")
        return selected_agent
    
    # Priority 2: Only use Command pattern for explicit handoffs
    agent_command = state.get("agent_command")
    if agent_command and hasattr(agent_command, 'goto'):
        logger.info(f"🎯 get_routing_target: Using Command pattern routing to: {agent_command.goto}")
        return agent_command.goto
    
    # Remove automatic message-based routing
    # No fallback to routing analysis - user selection takes precedence
    logger.info("🎯 get_routing_target: No routing target found (user-centric architecture)")
    return None

class MockWorkflowManager:
    """Mock workflow manager for testing routing logic."""
    
    def __init__(self):
        self.agent_nodes = {
            "concierge": "MockConciergeAgent",
            "marketing": "MockMarketingAgent",
            "analysis": "MockAnalysisAgent"
        }
        self.tool_nodes = {
            "data_query": "MockDataQueryTool",
            "web_search": "MockWebSearchTool"
        }
    
    def _get_entry_point(self, state: UnifiedDatageniusState) -> str:
        """
        Mock version of the updated entry point logic.
        This mirrors the changes made to workflow_manager.py.
        """
        logger.info("🎯 Determining entry point based on selected agent")
        
        # Priority 1: Use selected agent if available
        selected_agent = state.get("selected_agent")
        if selected_agent and selected_agent in self.agent_nodes:
            logger.info(f"✅ Entry point: agent_{selected_agent} (user selected)")
            return f"agent_{selected_agent}"
        
        # Priority 2: Use current agent if no selected agent
        current_agent = state.get("current_agent")
        if current_agent and current_agent in self.agent_nodes:
            logger.info(f"✅ Entry point: agent_{current_agent} (current agent)")
            return f"agent_{current_agent}"
        
        # Priority 3: Fallback to concierge or default agent
        for agent_id in self.agent_nodes.keys():
            if "concierge" in agent_id.lower():
                logger.info(f"✅ Entry point: agent_{agent_id} (concierge fallback)")
                return f"agent_{agent_id}"
        
        # Priority 4: Use first available agent
        if self.agent_nodes:
            fallback_agent = next(iter(self.agent_nodes.keys()))
            logger.info(f"✅ Entry point: agent_{fallback_agent} (first available)")
            return f"agent_{fallback_agent}"
        
        # Emergency fallback to routing (should not happen in normal operation)
        logger.warning("⚠️ No agents available, falling back to routing node")
        return "routing"
    
    def _route_from_tool(self, state: UnifiedDatageniusState) -> str:
        """
        Mock version of the updated tool routing logic.
        This mirrors the changes made to workflow_manager.py.
        """
        logger.info("🔧 Routing from tool back to selected agent")
        
        # Priority 1: Route back to selected agent
        selected_agent = state.get("selected_agent")
        if selected_agent and selected_agent in self.agent_nodes:
            logger.info(f"✅ Tool routing to selected agent: {selected_agent}")
            return f"agent_{selected_agent}"
        
        # Priority 2: Route back to current agent
        current_agent = state.get("current_agent")
        if current_agent and current_agent in self.agent_nodes:
            logger.info(f"✅ Tool routing to current agent: {current_agent}")
            return f"agent_{current_agent}"
        
        # Priority 3: Fallback to concierge
        for agent_id in self.agent_nodes.keys():
            if "concierge" in agent_id.lower():
                logger.info(f"✅ Tool routing to concierge fallback: {agent_id}")
                return f"agent_{agent_id}"
        
        # Priority 4: Use first available agent
        if self.agent_nodes:
            fallback_agent = next(iter(self.agent_nodes.keys()))
            logger.info(f"✅ Tool routing to first available agent: {fallback_agent}")
            return f"agent_{fallback_agent}"
        
        # This should not happen in normal operation
        logger.error("❌ No agents available for tool routing")
        raise ValueError("No agents available for tool routing")

def test_user_centric_routing():
    """Test all user-centric routing changes."""
    logger.info("🚀 Starting user-centric routing validation...")
    
    manager = MockWorkflowManager()
    tests_passed = 0
    tests_failed = 0
    
    # Test 1: Entry point with selected agent
    logger.info("\n🧪 Test 1: Entry point with selected agent")
    try:
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation",
            selected_agent="marketing"
        )
        entry_point = manager._get_entry_point(state)
        assert entry_point == "agent_marketing", f"Expected agent_marketing, got {entry_point}"
        logger.info("✅ PASSED: Entry point correctly uses selected agent")
        tests_passed += 1
    except Exception as e:
        logger.error(f"❌ FAILED: {e}")
        tests_failed += 1
    
    # Test 2: Entry point fallback to concierge
    logger.info("\n🧪 Test 2: Entry point fallback to concierge")
    try:
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation"
        )
        entry_point = manager._get_entry_point(state)
        assert entry_point == "agent_concierge", f"Expected agent_concierge, got {entry_point}"
        logger.info("✅ PASSED: Entry point correctly falls back to concierge")
        tests_passed += 1
    except Exception as e:
        logger.error(f"❌ FAILED: {e}")
        tests_failed += 1
    
    # Test 3: Tool routing to selected agent
    logger.info("\n🧪 Test 3: Tool routing to selected agent")
    try:
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation",
            selected_agent="marketing"
        )
        route_target = manager._route_from_tool(state)
        assert route_target == "agent_marketing", f"Expected agent_marketing, got {route_target}"
        logger.info("✅ PASSED: Tool routing correctly returns to selected agent")
        tests_passed += 1
    except Exception as e:
        logger.error(f"❌ FAILED: {e}")
        tests_failed += 1
    
    # Test 4: Simplified routing logic prioritizes selected agent
    logger.info("\n🧪 Test 4: Simplified routing logic prioritizes selected agent")
    try:
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation",
            selected_agent="marketing"
        )
        target = get_routing_target(state)
        assert target == "marketing", f"Expected marketing, got {target}"
        logger.info("✅ PASSED: Routing logic correctly prioritizes selected agent")
        tests_passed += 1
    except Exception as e:
        logger.error(f"❌ FAILED: {e}")
        tests_failed += 1
    
    # Test 5: No fallback to automatic analysis
    logger.info("\n🧪 Test 5: No fallback to automatic analysis")
    try:
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation"
        )
        # Add routing analysis that should be ignored
        state["routing_analysis"] = {
            "target_agent": "analysis",
            "confidence": 0.9
        }
        target = get_routing_target(state)
        assert target is None, f"Expected None, got {target}"
        logger.info("✅ PASSED: Routing logic correctly ignores automatic analysis")
        tests_passed += 1
    except Exception as e:
        logger.error(f"❌ FAILED: {e}")
        tests_failed += 1
    
    # Test 6: User selection overrides command
    logger.info("\n🧪 Test 6: User selection overrides command")
    try:
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation",
            selected_agent="marketing"
        )
        # Mock agent command
        class MockCommand:
            def __init__(self, goto):
                self.goto = goto
        
        state["agent_command"] = MockCommand("analysis")
        target = get_routing_target(state)
        assert target == "marketing", f"Expected marketing, got {target}"
        logger.info("✅ PASSED: User selection correctly overrides command routing")
        tests_passed += 1
    except Exception as e:
        logger.error(f"❌ FAILED: {e}")
        tests_failed += 1
    
    # Summary
    logger.info(f"\n📊 Validation Results: {tests_passed} passed, {tests_failed} failed")
    
    if tests_failed == 0:
        logger.info("🎉 All user-centric routing changes validated successfully!")
        return True
    else:
        logger.error("💥 Some validation tests failed!")
        return False

if __name__ == "__main__":
    success = test_user_centric_routing()
    sys.exit(0 if success else 1)
