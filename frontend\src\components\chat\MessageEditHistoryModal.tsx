import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { chatApi, type Message, type MessageEditHistory } from '@/lib/api';
import { 
  History, 
  Clock, 
  User, 
  Bot,
  Copy,
  Edit3,
  AlertCircle,
  Loader2
} from 'lucide-react';

interface MessageEditHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  message: Message;
  onEditVersion?: (message: Message) => void;
}

export const MessageEditHistoryModal: React.FC<MessageEditHistoryModalProps> = ({
  isOpen,
  onClose,
  message,
  onEditVersion
}) => {
  const [editHistory, setEditHistory] = useState<MessageEditHistory | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    if (isOpen && message.id) {
      fetchEditHistory();
    }
  }, [isOpen, message.id]);

  const fetchEditHistory = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const history = await chatApi.getMessageEditHistory(message.id);
      setEditHistory(history);
    } catch (error) {
      console.error('Error fetching edit history:', error);
      setError(error instanceof Error ? error.message : 'Failed to load edit history');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyContent = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      toast({
        title: 'Copied to clipboard',
        description: 'Message content has been copied.',
      });
    } catch (error) {
      toast({
        title: 'Copy failed',
        description: 'Could not copy message content.',
        variant: 'destructive'
      });
    }
  };

  const handleEditVersion = (versionMessage: Message) => {
    onEditVersion?.(versionMessage);
    onClose();
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  const isUser = message.sender === 'user';
  const allVersions = editHistory ? [message, ...editHistory.edits] : [message];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Message Edit History
          </DialogTitle>
          <DialogDescription className="flex items-center gap-2">
            <div className="flex items-center gap-2">
              {isUser ? (
                <User className="h-4 w-4 text-blue-500" />
              ) : (
                <Bot className="h-4 w-4 text-green-500" />
              )}
              <span>{isUser ? 'User' : 'AI'} message</span>
              <Badge variant="outline" className="text-xs">
                {allVersions.length} version{allVersions.length !== 1 ? 's' : ''}
              </Badge>
            </div>
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="flex items-center gap-2 text-gray-500">
                <Loader2 className="h-5 w-5 animate-spin" />
                <span>Loading edit history...</span>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-64">
              <div className="flex items-center gap-2 text-red-500">
                <AlertCircle className="h-5 w-5" />
                <span>{error}</span>
              </div>
            </div>
          ) : (
            <ScrollArea className="h-full pr-4">
              <div className="space-y-4">
                {allVersions.map((version, index) => {
                  const isOriginal = index === 0;
                  const timestamp = formatTimestamp(version.created_at);
                  
                  return (
                    <motion.div
                      key={version.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Card className={`${isOriginal ? 'border-blue-200 bg-blue-50/50' : 'border-gray-200'}`}>
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Badge variant={isOriginal ? 'default' : 'secondary'} className="text-xs">
                                {isOriginal ? 'Original' : `Edit ${index}`}
                              </Badge>
                              <div className="flex items-center gap-1 text-xs text-gray-500">
                                <Clock className="h-3 w-3" />
                                <span>{timestamp.date} at {timestamp.time}</span>
                              </div>
                            </div>
                            <div className="flex items-center gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleCopyContent(version.content)}
                                className="h-7 px-2"
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                              {!isOriginal && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEditVersion(version)}
                                  className="h-7 px-2"
                                >
                                  <Edit3 className="h-3 w-3" />
                                </Button>
                              )}
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <div className="prose prose-sm max-w-none">
                            <div className="whitespace-pre-wrap text-sm text-gray-700 bg-white p-3 rounded border">
                              {version.content}
                            </div>
                          </div>
                          <div className="flex justify-between items-center mt-3 text-xs text-gray-500">
                            <span>{version.content.length} characters</span>
                            {version.metadata?.edited_at && (
                              <Badge variant="outline" className="text-xs">
                                Edited
                              </Badge>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  );
                })}
              </div>
            </ScrollArea>
          )}
        </div>

        <div className="flex justify-end pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
