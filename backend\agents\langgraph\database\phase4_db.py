"""
Phase 4 Database Integration Module

Provides database operations for Phase 4 Platform Evolution components
including capability marketplace, trading engine, certification system,
and pattern recognition.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import json
import asyncpg
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)


class Phase4DatabaseManager:
    """Database manager for Phase 4 components."""
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.pool: Optional[asyncpg.Pool] = None
        self._initialized = False
    
    async def initialize(self):
        """Initialize database connection pool."""
        try:
            self.pool = await asyncpg.create_pool(
                self.database_url,
                min_size=5,
                max_size=20,
                command_timeout=60
            )
            
            # Run schema initialization
            await self._initialize_schema()
            
            self._initialized = True
            logger.info("Phase 4 database manager initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Phase 4 database: {e}")
            raise
    
    async def close(self):
        """Close database connections."""
        if self.pool:
            await self.pool.close()
            self._initialized = False
            logger.info("Phase 4 database connections closed")
    
    @asynccontextmanager
    async def get_connection(self):
        """Get database connection from pool."""
        if not self._initialized or not self.pool:
            raise RuntimeError("Database not initialized")
        
        async with self.pool.acquire() as connection:
            yield connection
    
    async def _initialize_schema(self):
        """Initialize database schema if needed."""
        try:
            # Read and execute schema file
            schema_path = "backend/agents/langgraph/database/phase4_schema.sql"
            try:
                with open(schema_path, 'r') as f:
                    schema_sql = f.read()
                
                async with self.get_connection() as conn:
                    await conn.execute(schema_sql)
                    
                logger.info("Phase 4 database schema initialized")
                
            except FileNotFoundError:
                logger.warning(f"Schema file not found: {schema_path}")
                # Continue without schema initialization
                
        except Exception as e:
            logger.error(f"Error initializing schema: {e}")
            # Don't raise - allow system to continue
    
    # Capability Marketplace Operations
    async def save_capability_listing(self, listing_data: Dict[str, Any]) -> str:
        """Save capability listing to database."""
        try:
            async with self.get_connection() as conn:
                query = """
                INSERT INTO capability_listings (
                    capability_id, agent_id, name, description, category, version,
                    price, status, trading_mode, performance_score, success_rate,
                    usage_count, certification_level, is_public, estimated_execution_time,
                    complexity_level
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
                ON CONFLICT (capability_id) DO UPDATE SET
                    name = EXCLUDED.name,
                    description = EXCLUDED.description,
                    price = EXCLUDED.price,
                    status = EXCLUDED.status,
                    performance_score = EXCLUDED.performance_score,
                    success_rate = EXCLUDED.success_rate,
                    updated_at = CURRENT_TIMESTAMP
                RETURNING capability_id
                """
                
                result = await conn.fetchval(
                    query,
                    listing_data.get('capability_id'),
                    listing_data.get('agent_id'),
                    listing_data.get('name'),
                    listing_data.get('description'),
                    listing_data.get('category'),
                    listing_data.get('version', '1.0.0'),
                    listing_data.get('price', 0.0),
                    listing_data.get('status', 'available'),
                    listing_data.get('trading_mode', 'fixed_price'),
                    listing_data.get('performance_score', 0.8),
                    listing_data.get('success_rate', 0.8),
                    listing_data.get('usage_count', 0),
                    listing_data.get('certification_level'),
                    listing_data.get('is_public', True),
                    listing_data.get('estimated_execution_time', 3600),
                    listing_data.get('complexity_level', 'medium')
                )
                
                return result
                
        except Exception as e:
            logger.error(f"Error saving capability listing: {e}")
            raise
    
    async def get_capability_listings(self, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Get capability listings with optional filters."""
        try:
            async with self.get_connection() as conn:
                base_query = "SELECT * FROM capability_listings WHERE 1=1"
                params = []
                param_count = 0
                
                if filters:
                    if 'category' in filters:
                        param_count += 1
                        base_query += f" AND category = ${param_count}"
                        params.append(filters['category'])
                    
                    if 'status' in filters:
                        param_count += 1
                        base_query += f" AND status = ${param_count}"
                        params.append(filters['status'])
                    
                    if 'agent_id' in filters:
                        param_count += 1
                        base_query += f" AND agent_id = ${param_count}"
                        params.append(filters['agent_id'])
                    
                    if 'min_performance' in filters:
                        param_count += 1
                        base_query += f" AND performance_score >= ${param_count}"
                        params.append(filters['min_performance'])
                
                base_query += " ORDER BY performance_score DESC, created_at DESC"
                
                if filters and 'limit' in filters:
                    param_count += 1
                    base_query += f" LIMIT ${param_count}"
                    params.append(filters['limit'])
                
                rows = await conn.fetch(base_query, *params)
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Error getting capability listings: {e}")
            return []
    
    async def save_capability_request(self, request_data: Dict[str, Any]) -> str:
        """Save capability request to database."""
        try:
            async with self.get_connection() as conn:
                query = """
                INSERT INTO capability_requests (
                    request_id, requester_id, requirements, budget, deadline, status
                ) VALUES ($1, $2, $3, $4, $5, $6)
                ON CONFLICT (request_id) DO UPDATE SET
                    status = EXCLUDED.status,
                    selected_capability_id = EXCLUDED.selected_capability_id,
                    selected_agent = EXCLUDED.selected_agent,
                    final_price = EXCLUDED.final_price,
                    quality_score = EXCLUDED.quality_score,
                    updated_at = CURRENT_TIMESTAMP
                RETURNING request_id
                """
                
                result = await conn.fetchval(
                    query,
                    request_data.get('request_id'),
                    request_data.get('requester_id'),
                    json.dumps(request_data.get('requirements', {})),
                    request_data.get('budget', 0.0),
                    request_data.get('deadline'),
                    request_data.get('status', 'pending')
                )
                
                return result
                
        except Exception as e:
            logger.error(f"Error saving capability request: {e}")
            raise
    
    # Trading Engine Operations
    async def save_trade(self, trade_data: Dict[str, Any]) -> str:
        """Save trade to database."""
        try:
            async with self.get_connection() as conn:
                query = """
                INSERT INTO trades (
                    trade_id, request_id, requester_id, capability_requirements,
                    budget, deadline, status, trading_mode, min_quality_score,
                    max_execution_time
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                ON CONFLICT (trade_id) DO UPDATE SET
                    status = EXCLUDED.status,
                    selected_agent = EXCLUDED.selected_agent,
                    winning_bid_id = EXCLUDED.winning_bid_id,
                    final_price = EXCLUDED.final_price,
                    updated_at = CURRENT_TIMESTAMP
                RETURNING trade_id
                """
                
                result = await conn.fetchval(
                    query,
                    trade_data.get('trade_id'),
                    trade_data.get('request_id'),
                    trade_data.get('requester_id'),
                    json.dumps(trade_data.get('capability_requirements', {})),
                    trade_data.get('budget'),
                    trade_data.get('deadline'),
                    trade_data.get('status', 'pending'),
                    trade_data.get('trading_mode', 'auction'),
                    trade_data.get('min_quality_score', 0.7),
                    trade_data.get('max_execution_time', 3600)
                )
                
                return result
                
        except Exception as e:
            logger.error(f"Error saving trade: {e}")
            raise
    
    async def save_bid(self, bid_data: Dict[str, Any]) -> str:
        """Save bid to database."""
        try:
            async with self.get_connection() as conn:
                query = """
                INSERT INTO bids (
                    bid_id, trade_id, agent_id, capability_id, price,
                    estimated_execution_time, quality_score, status, bid_details, expires_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                ON CONFLICT (bid_id) DO UPDATE SET
                    status = EXCLUDED.status,
                    accepted_at = EXCLUDED.accepted_at,
                    rejected_at = EXCLUDED.rejected_at
                RETURNING bid_id
                """
                
                result = await conn.fetchval(
                    query,
                    bid_data.get('bid_id'),
                    bid_data.get('trade_id'),
                    bid_data.get('agent_id'),
                    bid_data.get('capability_id'),
                    bid_data.get('price'),
                    bid_data.get('estimated_execution_time'),
                    bid_data.get('quality_score'),
                    bid_data.get('status', 'pending'),
                    json.dumps(bid_data.get('bid_details', {})),
                    bid_data.get('expires_at')
                )
                
                return result
                
        except Exception as e:
            logger.error(f"Error saving bid: {e}")
            raise
    
    # Execution History Operations
    async def save_execution_record(self, execution_data: Dict[str, Any]) -> str:
        """Save execution record to database."""
        try:
            async with self.get_connection() as conn:
                query = """
                INSERT INTO execution_history (
                    execution_id, workflow_id, capability_id, agent_id,
                    execution_time, success, quality_score, error_message, performance_data
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                RETURNING execution_id
                """
                
                result = await conn.fetchval(
                    query,
                    execution_data.get('execution_id'),
                    execution_data.get('workflow_id'),
                    execution_data.get('capability_id'),
                    execution_data.get('agent_id'),
                    execution_data.get('execution_time'),
                    execution_data.get('success'),
                    execution_data.get('quality_score'),
                    execution_data.get('error_message'),
                    json.dumps(execution_data.get('performance_data', {}))
                )
                
                return result
                
        except Exception as e:
            logger.error(f"Error saving execution record: {e}")
            raise
    
    async def get_execution_history(self, capability_id: str, days: int = 30) -> List[Dict[str, Any]]:
        """Get execution history for a capability."""
        try:
            async with self.get_connection() as conn:
                query = """
                SELECT * FROM execution_history 
                WHERE capability_id = $1 AND executed_at >= $2
                ORDER BY executed_at DESC
                """
                
                cutoff_date = datetime.now() - timedelta(days=days)
                rows = await conn.fetch(query, capability_id, cutoff_date)
                
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Error getting execution history: {e}")
            return []


# Global database manager instance
db_manager: Optional[Phase4DatabaseManager] = None


async def initialize_phase4_database(database_url: str):
    """Initialize the global Phase 4 database manager."""
    global db_manager
    db_manager = Phase4DatabaseManager(database_url)
    await db_manager.initialize()


async def get_phase4_db() -> Phase4DatabaseManager:
    """Get the global Phase 4 database manager."""
    if not db_manager or not db_manager._initialized:
        raise RuntimeError("Phase 4 database not initialized")
    return db_manager


async def close_phase4_database():
    """Close the global Phase 4 database manager."""
    global db_manager
    if db_manager:
        await db_manager.close()
        db_manager = None
