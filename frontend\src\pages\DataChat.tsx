import { useState, useRef, useEffect, useMemo } from "react";
import React from 'react'; // Import React for JSX
import ReactMarkdown from 'react-markdown';
import type { Components } from 'react-markdown';
import { motion, AnimatePresence } from "framer-motion";
import { DashboardLayout } from "@/components/DashboardLayout";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { MessageCircle, Send, BarChart4, Mic, MicOff, Upload, Loader2, Menu, X, Database, Link, User, ShoppingCart, FileText, RefreshCw, Settings, Info, ArrowLeft, Compass, ChevronLeft, ChevronRight, Paperclip } from "lucide-react"; // Added Paperclip, ChevronLeft, ChevronRight
import { useToast } from "@/hooks/use-toast";
import { useChat } from "@/hooks/use-chat"; // Removed Message type import from here
import { useProviderAvailability } from "@/hooks/use-provider-availability";
import { personaApi, chatApi, fileApi, Persona, Message, type EditMessageResponse } from "@/lib/api"; // Added Message and EditMessageResponse type imports
import { aiPersonas } from "@/data/aiPersonas";
import { generateWelcomeMessageId } from "@/utils/idUtils";
import { createReactSyncApp } from "@/utils/createReactSyncApp";
import { ConversationList } from "@/components/chat/ConversationList";
import { DataSourceSelector } from "@/components/chat/DataSourceSelector";
import { PersonaSelector } from "@/components/chat/PersonaSelector";
import { SpecializedComponentRenderer } from "@/components/chat/SpecializedComponentRenderer";
import {
  ConversationRestorationLoader,
  InitializationLoader,
  TypingIndicator,
  ChatSkeletonLoader
} from "@/components/chat/ChatLoadingStates";
import { EnhancedChatInput } from "@/components/chat/EnhancedChatInput";
import { EnhancedMessage } from "@/components/chat/EnhancedMessage";
import {
  ConversationStateIndicator,
  ConversationRecoveryBanner
} from "@/components/chat/ConversationStateIndicator";
import { useDataSourceRefresh, useAutoRefreshOnUpload } from "@/hooks/use-data-source-refresh";
import { VisualizationRenderer } from "@/components/visualizations/VisualizationRenderer";
import { InteractiveChartSystem } from "@/components/visualizations/InteractiveChartSystem";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { DataSource, dataSourceApi } from "@/lib/dataSourceApi";
import { useNavigate } from "react-router-dom";
import { ClassificationConfig } from "@/components/classification/ClassificationConfigForm";
import { ClassificationResult } from "@/components/classification/ClassificationResultsVisualization";
import { MarketingContentForm, MarketingContentFormData } from "@/components/marketing/MarketingContentForm"; // Import the component too
import { useProviders } from '@/hooks/useProviders'; // Import useProviders
import { WorkflowStageIndicator, WorkflowStage } from "@/components/chat/WorkflowStageIndicator";
import { WorkflowVisualization, createWorkflowSteps } from "@/components/chat/WorkflowVisualization"; // Added WorkflowVisualization and createWorkflowSteps
import { ReturnToConcierge } from "@/components/chat/ReturnToConcierge";
import { ConciergePanel } from "@/components/chat/ConciergePanel";
import { ConciergeHelpModal } from "@/components/chat/ConciergeHelpModal";
import { ConciergeOnboarding } from "@/components/chat/ConciergeOnboarding";
import { useConcierge, extractConciergeState } from "@/contexts/ConciergeContext";

// Import page state management
import { useDataChatState } from "@/hooks/use-page-state";

// Convert backend persona to frontend format
const convertPersona = (backendPersona: any): Persona => {
  return {
    id: backendPersona.id,
    name: backendPersona.name,
    description: backendPersona.description,
    industry: backendPersona.industry,
    skills: backendPersona.skills,
    rating: backendPersona.rating,
    reviewCount: backendPersona.review_count || backendPersona.reviewCount,
    imageUrl: backendPersona.image_url || backendPersona.imageUrl,
    avatarUrl: backendPersona.avatar_url || backendPersona.avatarUrl,
    isAvailable: backendPersona.is_available !== undefined ? backendPersona.is_available : backendPersona.isAvailable,
    isPurchased: backendPersona.is_purchased !== undefined ? backendPersona.is_purchased : backendPersona.isPurchased,
    capabilities: backendPersona.capabilities
  };
};

// Convert backend message to frontend format
const convertMessage = (message: any) => {
  let visualization = null;

  // Check if message has visualization data in metadata
  if (message.metadata) {
    if (message.metadata.visualization === "chart") {
      visualization = "chart";
    } else if (message.metadata.visualization === "table") {
      visualization = "table";
    }

    // If metadata contains sample_results, it's a classification result
    if (message.metadata.sample_results) {
      visualization = "classification";
    }

    // If metadata contains generated_content and task_type, it's a marketing content result
    if (message.metadata.generated_content && message.metadata.task_type) {
      visualization = "marketing";
    }
  }

  // Ensure we handle both message.metadata and message.message_metadata
  const metadata = message.metadata || message.message_metadata;

  // Debug logging for message conversion
  console.log(`Converting message ${message.id}: sender=${message.sender}, content_length=${message.content?.length || 0}`);

  return {
    id: message.id,
    content: message.content || "", // Ensure content is never null/undefined
    sender: message.sender,
    timestamp: new Date(message.created_at),
    visualization,
    metadata,
    // Add missing required fields from the original message
    conversation_id: message.conversation_id,
    created_at: message.created_at,
  };
};

type MessageType = {
  id: string;
  content: string;
  sender: "user" | "ai";
  timestamp: Date;
  visualization?: "chart" | "table" | "classification" | "marketing" | null; // Added classification and marketing
  metadata?: any;
  attachment?: {
    name: string;
    type: string;
    size: number;
  } | null; // Added for file attachments
  // Align with Message type from @/lib/api
  conversation_id: string; // Make required
  created_at: string; // Should be string based on use-chat usage
};

// No longer need assertMessageTypeCompatible as types should align now

const DataChat = () => {
  const { toast } = useToast();
  const navigate = useNavigate();

  // Page state management
  const { state: pageState, setState: setPageState } = useDataChatState();

  // Initialize state - always start with blank input message
  const [inputMessage, setInputMessage] = useState("");
  const [attachedFiles, setAttachedFiles] = useState<File[]>([]); // State for multiple attached files
  const [persistentAttachments, setPersistentAttachments] = useState<boolean>(false); // Control attachment persistence
  const [isRecording, setIsRecording] = useState(false);
  const [selectedPersona, setSelectedPersona] = useState<Persona | null>(null);
  const [availablePersonas, setAvailablePersonas] = useState<Persona[]>([]);
  const [isLoadingPersonas, setIsLoadingPersonas] = useState(false);
  const [conversationsModalOpen, setConversationsModalOpen] = useState(false);
  const [dataSourceModalOpen, setDataSourceModalOpen] = useState(false);
  const [personaModalOpen, setPersonaModalOpen] = useState(false);
  const [isRestoringConversation, setIsRestoringConversation] = useState(false);
  const [attachmentPopoverOpen, setAttachmentPopoverOpen] = useState(false);
  const [selectedDataSources, setSelectedDataSources] = useState<DataSource[]>([]); // State for multiple data sources
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const creationAttemptedForPersonaRef = useRef<string | null>(null); // Ref to track creation attempt
  const fileInputRef = useRef<HTMLInputElement>(null); // Ref for file input


  // Specialized component states
  const [showSpecializedComponent, setShowSpecializedComponent] = useState(false);
  const [isProcessingSpecializedRequest, setIsProcessingSpecializedRequest] = useState(false);

  // Classification states
  const [classificationConfig, setClassificationConfig] = useState<ClassificationConfig | null>(null);
  const [classificationResults, setClassificationResults] = useState<ClassificationResult[]>([]);

  // Marketing content states
  const [generatedMarketingContent, setGeneratedMarketingContent] = useState("");
  const [showMarketingForm, setShowMarketingForm] = useState(false); // State for marketing form visibility
  const [marketingFormDefaultValues, setMarketingFormDefaultValues] = useState<Partial<MarketingContentFormData>>({});
  const [shouldExtractFormData, setShouldExtractFormData] = useState(false); // Flag to trigger form data extraction
  const { providers } = useProviders(); // Get providers

  // Concierge-related states
  const {
    isConciergeActive,
    conciergeState,
    setConciergeActive,
    updateConciergeState,
    resetConciergeState,
    returnToConcierge,
    setOnReturnToConcierge
  } = useConcierge();

  // Helper functions for action button form pre-population
  const getDefaultToneForContentType = (contentType: string): string => {
    const toneMapping: Record<string, string> = {
      'marketing_strategy': 'Professional',
      'campaign_strategy': 'Enthusiastic',
      'social_media_content': 'Friendly',
      'seo_optimization': 'Professional',
      'blog_content': 'Friendly',
      'email_marketing': 'Friendly',
      'ad_copy': 'Persuasive',
      'press_release': 'Professional',
      'competitor_analysis': 'Professional',
      'audience_research': 'Professional',
      'market_analysis': 'Professional',
      'data_driven_analysis': 'Professional',
      'data_driven_campaigns': 'Enthusiastic'
    };
    return toneMapping[contentType] || 'Professional';
  };

  const getDefaultFormatForContentType = (contentType: string): string => {
    const formatMapping: Record<string, string> = {
      'marketing_strategy': 'report',
      'campaign_strategy': 'brief',
      'social_media_content': 'template',
      'seo_optimization': 'checklist',
      'blog_content': 'guide',
      'email_marketing': 'template',
      'ad_copy': 'template',
      'press_release': 'template',
      'competitor_analysis': 'analysis',
      'audience_research': 'analysis',
      'market_analysis': 'analysis',
      'data_driven_analysis': 'analysis',
      'data_driven_campaigns': 'brief'
    };
    return formatMapping[contentType] || 'report';
  };
  const [showConciergePanel, setShowConciergePanel] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);

  // Create workflow steps for visualization
  const workflowSteps = useMemo(() => {
    console.log("DataChat: conciergeState inside workflowSteps useMemo:", conciergeState); // Added log for debugging
    if (!conciergeState) return [];
    return createWorkflowSteps(conciergeState);
  }, [conciergeState]);

  // We'll fetch models from the API instead of using mock data

  // Use our custom chat hook
  const {
    messages: chatMessages,
    isLoading,
    isTyping,
    connectionStatus,
    messageDeliveryStatus,
    toolExecutionStatus,
    sendMessage,
    createConversation,
    loadConversation,
    resetConversation, // ✅ NEW: Import safe reset function
    refreshConversation: hookRefreshConversation,
    isAutoRefreshing,
  } = useChat();

  // State for manual refresh loading
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Data source refresh hooks
  const { refreshTrigger } = useDataSourceRefresh();
  const { handleFileUploaded, handleDataSourceCreated } = useAutoRefreshOnUpload();

  // Function to manually refresh the conversation
  const refreshConversation = async () => {
    if (!chatMessages.length) return;

    setIsRefreshing(true);
    try {
      await hookRefreshConversation();
    } catch (error) {
      console.error("Failed to refresh conversation:", error);
      toast({
        title: "Refresh Failed",
        description: "Could not refresh the conversation. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // Convert backend messages to frontend format with deduplication
  const messages: MessageType[] = useMemo(() => {
    if (chatMessages.length === 0) {
      console.log("No chat messages to process");
      return [];
    }

    console.log(`Processing ${chatMessages.length} chat messages from backend`);

    const seenMessageIds = new Set<string>();
    const uniqueMessages: MessageType[] = [];

    chatMessages.forEach((message, index) => {
      console.log(`Processing message ${index + 1}/${chatMessages.length}: ID=${message.id}, sender=${message.sender}, content_preview="${message.content?.substring(0, 50)}..."`);

      if (!seenMessageIds.has(message.id)) {
        seenMessageIds.add(message.id);
        try {
          const convertedMessage = convertMessage(message);
          uniqueMessages.push(convertedMessage);
          console.log(`✅ Successfully converted message ${message.id}`);
        } catch (error) {
          console.error(`❌ Error converting message ${message.id}:`, error);
        }
      } else {
        console.log(`⚠️ Skipping duplicate message with ID ${message.id}`);
      }
    });

    console.log(`✅ Processed ${chatMessages.length} messages, ${uniqueMessages.length} unique messages`);

    // Ensure messages are sorted by timestamp
    uniqueMessages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    // Log final message summary
    const userMessages = uniqueMessages.filter(m => m.sender === 'user').length;
    const aiMessages = uniqueMessages.filter(m => m.sender === 'ai').length;
    console.log(`📊 Final message summary: ${userMessages} user messages, ${aiMessages} AI messages`);

    return uniqueMessages;
  }, [chatMessages]);

  // Check if there are any available personas
  const hasAvailablePersonas = availablePersonas.some(p => p.isAvailable);

  // Scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Get provider availability
  const { combinedAvailability, isLoading: isLoadingProviders } = useProviderAvailability();

  // Helper function to select default persona
  const selectDefaultPersona = (personas: Persona[]) => {
    console.log('selectDefaultPersona called with personas:', personas.map(p => ({ id: p.id, isAvailable: p.isAvailable })));
    const conciergePersona = personas.find(p => p.id === 'concierge' && p.isAvailable);
    if (conciergePersona) {
      // Set concierge as the default first agent
      console.log('Setting concierge as default persona');
      setSelectedPersona(conciergePersona);
    } else {
      // Fall back to any available persona if concierge is not available
      const firstAvailable = personas.find(p => p.isAvailable);
      if (firstAvailable) {
        console.log('Setting first available persona as default:', firstAvailable.id);
        setSelectedPersona(firstAvailable);
      } else if (personas.length > 0 && !personas.some(p => p.isAvailable)) {
        console.log('No available personas found');
        setSelectedPersona(null);
        toast({ title: "No Available Personas", description: "Purchase personas from the marketplace.", variant: "destructive" });
      }
    }
  };

  // Load available personas from the backend
  useEffect(() => {
    const loadPersonas = async () => {
      setIsLoadingPersonas(true);
      try {
        const response = await personaApi.getPersonas();
        let purchasedPersonas: string[] = [];
        try {
          purchasedPersonas = await personaApi.getPurchasedPersonas();
        } catch (purchaseError) { console.error('Error fetching purchased personas:', purchaseError); }

        // Debug: Log the raw API response
        console.log('Raw API response:', response);
        console.log('Raw personas from API:', response.personas);

        const updatedPersonas = await Promise.all(response.personas.map(async persona => {
          // Debug: Log each persona before conversion
          console.log('Raw persona from API:', persona);

          let requiredProvider = "groq"; // Default
          // Add specific provider logic if needed based on persona.id
          const isProviderAvailable = combinedAvailability[requiredProvider] || false;
          const isPurchased = Array.isArray(purchasedPersonas) && purchasedPersonas.includes(persona.id);

          // Convert backend persona format to frontend format
          const convertedPersona = convertPersona(persona);

          // Debug: Log the converted persona
          console.log('Converted persona:', convertedPersona);

          return { ...convertedPersona, isAvailable: isProviderAvailable && isPurchased, isPurchased };
        }));

        setAvailablePersonas(updatedPersonas);

        // Check for stored conversation state first
        const storedConversationId = localStorage.getItem('currentConversationId');
        const storedPersonaId = localStorage.getItem('currentPersonaId');
        console.log(`🔍 DataChat: Checking stored conversation state: conversationId=${storedConversationId}, personaId=${storedPersonaId}`);

        if (storedConversationId && storedPersonaId) {
          // Try to restore the previous conversation
          // First try to find the persona with availability check, then without
          let storedPersona = updatedPersonas.find(p => p.id === storedPersonaId && p.isAvailable);
          if (!storedPersona) {
            // If not available due to provider issues, try to find it without availability check
            storedPersona = updatedPersonas.find(p => p.id === storedPersonaId);
            console.log(`Persona ${storedPersonaId} found but not available, attempting restoration anyway`);
          }

          if (storedPersona) {
            console.log(`✅ DataChat: Restoring previous conversation: ${storedConversationId} with persona: ${storedPersonaId}`);
            setIsRestoringConversation(true);
            setSelectedPersona(storedPersona);

            // Update concierge state based on persona
            if (storedPersonaId === 'concierge') {
              setConciergeActive(true);
            } else {
              setConciergeActive(false);
            }

            // Load the stored conversation with enhanced error handling
            loadConversation(storedConversationId).then(() => {
              setIsRestoringConversation(false);
              console.log(`✅ DataChat: Successfully restored conversation: ${storedConversationId}`);

              // Verify that messages were actually loaded
              setTimeout(() => {
                if (chatMessages.length === 0) {
                  console.warn(`⚠️ DataChat: No messages found after restoration, attempting refresh`);
                  // Try to refresh the conversation to ensure all messages are loaded
                  hookRefreshConversation().catch(refreshError => {
                    console.error("❌ DataChat: Failed to refresh after restoration:", refreshError);
                  });
                }
              }, 1000); // Give some time for messages to load

            }).catch(error => {
              console.error("❌ DataChat: Failed to restore conversation:", error);
              setIsRestoringConversation(false);
              // If restoration fails, clear storage and fall back to default
              localStorage.removeItem('currentConversationId');
              localStorage.removeItem('currentPersonaId');
              console.log(`🧹 DataChat: Cleared failed conversation state, falling back to default`);
              // Fall back to default selection
              selectDefaultPersona(updatedPersonas);
            });
            return; // Exit early if we're restoring a conversation
          } else {
            // Stored persona not found at all, clear storage
            console.log(`Stored persona ${storedPersonaId} not found, clearing storage`);
            localStorage.removeItem('currentConversationId');
            localStorage.removeItem('currentPersonaId');
          }
        }

        // Default selection logic - prioritize concierge agent (only if no stored conversation)
        console.log('No stored conversation found, selecting default persona');
        selectDefaultPersona(updatedPersonas);

      } catch (error) {
        console.error("Failed to load personas:", error);
        toast({ title: "Error", description: "Failed to load AI personas.", variant: "destructive" });
        // Handle fallback if necessary
      } finally {
        setIsLoadingPersonas(false);
      }
    };
    if (!isLoadingProviders) loadPersonas();
  }, [toast, combinedAvailability, isLoadingProviders]); // Removed navigate dependency

  // Effect for initial conversation creation when a default persona is loaded
  useEffect(() => {
    // Check if there's a stored conversation that should be restored instead
    const storedConversationId = localStorage.getItem('currentConversationId');
    const storedPersonaId = localStorage.getItem('currentPersonaId');

    // Only run if:
    // 1. A persona is selected
    // 2. No messages exist
    // 3. Creation hasn't been attempted for this persona yet
    // 4. We're not restoring a conversation
    // 5. There's no stored conversation that should be restored
    if (selectedPersona &&
        messages.length === 0 &&
        !isLoading &&
        !isRestoringConversation &&
        creationAttemptedForPersonaRef.current !== selectedPersona.id &&
        !storedConversationId) {

      console.log(`Initial Load Effect: Conditions met for persona ${selectedPersona.id}. Attempting creation.`);
      creationAttemptedForPersonaRef.current = selectedPersona.id; // Mark attempt

      // Check if this is the concierge persona
      if (selectedPersona.id === 'concierge') {
        setConciergeActive(true);
      } else {
        setConciergeActive(false);
      }

      createConversation(selectedPersona.id, `Conversation with ${selectedPersona.name}`)
        .catch(error => {
          console.error("Failed to create initial conversation:", error);
          // Optionally reset the ref if creation fails, allowing another attempt?
          // creationAttemptedForPersonaRef.current = null;
        });
    } else if (storedConversationId) {
      console.log(`Initial Load Effect: Skipping conversation creation - stored conversation exists: ${storedConversationId}`);
    }
  // Run only when selectedPersona changes (initially) or if createConversation function reference changes (unlikely but safe)
  // Exclude messages.length and isLoading to prevent re-triggering on message updates or loading states
  }, [selectedPersona, createConversation, setConciergeActive, isRestoringConversation]);

  // Effect to update concierge state based on messages
  useEffect(() => {
    if (isConciergeActive && messages.length > 0) {
      // Extract concierge state from messages
      const extractedState = extractConciergeState(messages);
      if (extractedState) {
        updateConciergeState(extractedState);
      }
    }
  }, [messages, isConciergeActive, updateConciergeState]);

  // Effect to show onboarding when concierge is activated
  useEffect(() => {
    if (isConciergeActive && conciergeState) {
      // Show onboarding after a short delay
      const timer = setTimeout(() => {
        setShowOnboarding(true);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [isConciergeActive, conciergeState]);

  // Effect to persist current conversation state
  useEffect(() => {
    if (chatMessages.length > 0 && selectedPersona) {
      const conversationId = chatMessages[0]?.conversation_id;
      if (conversationId) {
        localStorage.setItem('currentConversationId', conversationId);
        localStorage.setItem('currentPersonaId', selectedPersona.id);
        console.log(`💾 DataChat: Persisted conversation state: ${conversationId} with persona: ${selectedPersona.id}`);
      }
    }
  }, [chatMessages, selectedPersona]);

  // Effect to clear stored state when conversation is explicitly reset (but not during restoration or component unmount)
  useEffect(() => {
    // Only clear localStorage if:
    // 1. No messages exist AND
    // 2. Not currently restoring a conversation AND
    // 3. A persona is selected (meaning we're in an active session, not just unmounting)
    if (chatMessages.length === 0 && !isRestoringConversation && selectedPersona) {
      // Additional check: only clear if we explicitly reset, not on navigation
      // We can detect this by checking if the conversation was intentionally cleared
      const wasIntentionallyCleared = !pageState?.conversationId || pageState.conversationId === '';

      if (wasIntentionallyCleared) {
        localStorage.removeItem('currentConversationId');
        localStorage.removeItem('currentPersonaId');
        console.log('🧹 DataChat: Cleared stored conversation state (intentional reset)');
      } else {
        console.log('🔒 DataChat: Skipping localStorage clear - appears to be navigation, not intentional reset');
      }
    }
  }, [chatMessages, isRestoringConversation, selectedPersona, pageState?.conversationId]);

  // Effect to save page state when key values change (with debouncing)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (selectedPersona || chatMessages.length > 0) {
        const conversationId = chatMessages.length > 0 ? chatMessages[0]?.conversation_id : undefined;

        setPageState({
          selectedPersona: selectedPersona ? {
            id: selectedPersona.id,
            name: selectedPersona.name,
          } : undefined,
          conversationId,
          // Don't persist inputMessage to prevent it from being restored after refresh/navigation
          showSpecializedComponent,
        });
      }
    }, 100); // Debounce to prevent infinite loops

    return () => clearTimeout(timeoutId);
  }, [selectedPersona?.id, selectedPersona?.name, chatMessages.length, showSpecializedComponent]); // Removed inputMessage from dependencies

  // Effect to restore state from page state on mount
  useEffect(() => {
    if (pageState && !isRestoringConversation) {
      // Restore specialized component state
      if (pageState.showSpecializedComponent !== undefined) {
        setShowSpecializedComponent(pageState.showSpecializedComponent);
      }

      // Don't restore input message - let it start blank

      // If page state has conversation info but localStorage doesn't, restore it
      if (pageState.conversationId && pageState.selectedPersona) {
        const storedConversationId = localStorage.getItem('currentConversationId');
        const storedPersonaId = localStorage.getItem('currentPersonaId');

        // If localStorage is missing but page state has the info, restore to localStorage
        if (!storedConversationId && !storedPersonaId) {
          console.log(`🔄 DataChat: Restoring conversation state from page state to localStorage: ${pageState.conversationId} with ${pageState.selectedPersona.id}`);
          localStorage.setItem('currentConversationId', pageState.conversationId);
          localStorage.setItem('currentPersonaId', pageState.selectedPersona.id);
        }
      }
    }
  }, [pageState, isRestoringConversation]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() && attachedFiles.length === 0 && selectedDataSources.length === 0) return;

    let messageContent = inputMessage.trim();
    let context: any = {};

    // Handle multiple file uploads - attach to message metadata instead of context
    let messageAttachments: any[] = [];
    if (attachedFiles.length > 0) {
      try {
        const fileNames = attachedFiles.map(f => f.name).join(", ");
        toast({
          title: "Uploading Files",
          description: `Uploading ${attachedFiles.length} file(s): ${fileNames}...`,
        });

        // Upload all files
        const uploadedFiles = await Promise.all(
          attachedFiles.map(file => fileApi.uploadFile(file))
        );

        // Trigger data source refresh for each file
        uploadedFiles.forEach(uploadedFile => {
          handleFileUploaded(uploadedFile);
        });

        // Store file attachments for message metadata
        messageAttachments = uploadedFiles.map(uploadedFile => ({
          id: uploadedFile.id,
          name: uploadedFile.filename,
          type: uploadedFile.filename.split('.').pop()?.toLowerCase() || 'unknown',
          size: uploadedFile.file_size,
          file_path: uploadedFile.file_path,
          num_rows: uploadedFile.num_rows,
          columns: uploadedFile.columns,
          uploaded_at: new Date().toISOString()
        }));

        // Create proper data source records in the database for uploaded files
        const newDataSources: DataSource[] = [];

        for (const uploadedFile of uploadedFiles) {
          try {
            // Create a data source record in the database
            const dataSourceCreate = {
              name: uploadedFile.filename,
              type: 'file' as const,
              description: `Uploaded file: ${uploadedFile.filename}`,
              is_active: true,
              file_id: uploadedFile.id
            };

            const createdDataSource = await dataSourceApi.createFileDataSource(dataSourceCreate);

            newDataSources.push(createdDataSource);

            console.log(`Created data source for file ${uploadedFile.filename}:`, createdDataSource);
          } catch (error) {
            console.error(`Error creating data source for file ${uploadedFile.filename}:`, error);
            // Fallback to creating a temporary data source object
            const fallbackDataSource: DataSource = {
              id: uploadedFile.id,
              name: uploadedFile.filename,
              type: 'file',
              user_id: uploadedFile.user_id,
              created_at: uploadedFile.created_at,
              updated_at: uploadedFile.updated_at,
              source_metadata: {
                file_id: uploadedFile.id,
                file_name: uploadedFile.filename,
                file_size: uploadedFile.file_size,
                num_rows: uploadedFile.num_rows,
                columns: uploadedFile.columns,
              },
              metadata: {
                user_id: uploadedFile.user_id,
                upload_date: uploadedFile.created_at,
                updated_at: uploadedFile.updated_at,
              },
              description: uploadedFile.description || `Uploaded file: ${uploadedFile.filename}`,
              is_active: true,
            };
            newDataSources.push(fallbackDataSource);
          }
        }

        // Add to existing selected data sources
        setSelectedDataSources(prev => [...prev, ...newDataSources]);

        // Set context with the created data sources
        context.data_sources = newDataSources.map(dataSource => ({
          id: dataSource.id,
          name: dataSource.name,
          type: dataSource.type,
          source_metadata: dataSource.source_metadata,
          metadata: dataSource.metadata
        }));

        // Only send a message if the user provided instructions
        if (!messageContent) {
          // If no message content, just upload the files and show they're attached
          setAttachedFiles([]); // Clear the attached files
          if (fileInputRef.current) {
            fileInputRef.current.value = ""; // Reset file input
          }

          toast({
            title: "Files Uploaded & Attached",
            description: `${attachedFiles.length} file(s) uploaded and attached. Type your instructions for the AI.`,
          });
          return; // Don't send a message, just attach the files
        } else {
          // If user provided instructions, include file attachment info
          const fileNames = attachedFiles.map(f => f.name).join(", ");
          messageContent = `${messageContent}\n\n(Attached files: ${fileNames})`;
        }

        toast({
          title: "Files Uploaded",
          description: `${attachedFiles.length} file(s) uploaded successfully.`,
        });
      } catch (error) {
        console.error("Error uploading files:", error);
        toast({
          title: "Upload Failed",
          description: `Failed to upload files. Please try again.`,
          variant: "destructive",
        });
        return; // Don't send the message if upload failed
      }
    }

    // Handle selected data sources (existing ones)
    if (selectedDataSources.length > 0) {
      // If there are selected data sources, add them to context
      const existingDataSources = selectedDataSources.map(dataSource => ({
        id: dataSource.id,
        name: dataSource.name,
        type: dataSource.type,
        source_metadata: dataSource.source_metadata,
        metadata: dataSource.metadata
      }));

      // Combine with uploaded files if any
      if (context.data_sources) {
        context.data_sources = [...context.data_sources, ...existingDataSources];
      } else {
        context.data_sources = existingDataSources;
      }
    }

    // Add persistent attachments setting to context
    context.persistent_attachments = persistentAttachments;

    // Send message with attachments
    sendMessage(
      messageContent,
      Object.keys(context).length > 0 ? context : undefined,
      messageAttachments.length > 0 ? messageAttachments : undefined,
      selectedPersona
    );
    setInputMessage("");

    // Clear attached files unless persistent attachments is enabled
    if (!persistentAttachments) {
      setAttachedFiles([]); // Clear the attached files
      if (fileInputRef.current) {
        fileInputRef.current.value = ""; // Reset file input
      }
    }
  };

  const handleAttachmentClick = () => {
    setAttachmentPopoverOpen(true);
  };

  const handleUploadFileClick = () => {
    setAttachmentPopoverOpen(false);
    fileInputRef.current?.click();
  };

  const handleAttachDataSourceClick = () => {
    setAttachmentPopoverOpen(false);
    setDataSourceModalOpen(true);
  };

  const handleFileSelected = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length > 0) {
      setAttachedFiles(prev => [...prev, ...files]);
      const fileNames = files.map(f => f.name).join(", ");
      toast({
        title: "Files Ready to Attach",
        description: `${files.length} file(s) selected: ${fileNames}. They will be uploaded when you send a message.`,
      });
      // Don't reset fileInputRef.current.value here, so the user sees the selected files
    }
  };

  const toggleRecording = () => {
    setIsRecording(!isRecording);
    if (!isRecording) {
      toast({ title: "Voice recording started", description: "Speak clearly" });
    } else {
      toast({ title: "Voice recording stopped", description: "Processing..." });
      setTimeout(() => { setInputMessage("What were my top selling products last month?"); }, 1500); // Use renamed state variable
    }
  };

  const handleSelectConversation = async (conversationId: string) => {
    try {
      // Load the conversation first to get the persona_id
      const conversation = await chatApi.getConversation(conversationId);

      // Find and set the persona associated with this conversation
      let conversationPersona = availablePersonas.find(p => p.id === conversation.persona_id && p.isAvailable);
      if (!conversationPersona) {
        // If not available due to provider issues, try to find it without availability check
        conversationPersona = availablePersonas.find(p => p.id === conversation.persona_id);
      }

      if (conversationPersona) {
        setSelectedPersona(conversationPersona);

        // Update concierge state based on persona
        if (conversation.persona_id === 'concierge') {
          setConciergeActive(true);
        } else {
          setConciergeActive(false);
        }
      }

      // Now load the conversation messages
      await loadConversation(conversationId);
      setConversationsModalOpen(false);
    } catch (error) {
      console.error("Failed to load conversation:", error);
      toast({ title: "Error", description: "Failed to load conversation.", variant: "destructive" });
    }
  };

  const handleCreateNewConversation = async (conversationId: string) => {
    try {
      // Load the conversation first to get the persona_id
      const conversation = await chatApi.getConversation(conversationId);

      // Find and set the persona associated with this conversation
      let conversationPersona = availablePersonas.find(p => p.id === conversation.persona_id && p.isAvailable);
      if (!conversationPersona) {
        // If not available due to provider issues, try to find it without availability check
        conversationPersona = availablePersonas.find(p => p.id === conversation.persona_id);
      }

      if (conversationPersona) {
        setSelectedPersona(conversationPersona);

        // Update concierge state based on persona
        if (conversation.persona_id === 'concierge') {
          setConciergeActive(true);
        } else {
          setConciergeActive(false);
        }
      }

      // Now load the conversation messages
      await loadConversation(conversationId);
      setConversationsModalOpen(false);
    } catch (error) {
      console.error("Failed to load new conversation:", error);
      toast({ title: "Error", description: "Failed to load the new conversation.", variant: "destructive" });
    }
  };

  const handleSelectDataSource = (dataSource: DataSource) => {
    // Check if data source is already selected
    const isAlreadySelected = selectedDataSources.some(ds => ds.id === dataSource.id);

    if (isAlreadySelected) {
      toast({
        title: "Data Source Already Attached",
        description: `"${dataSource.name}" is already attached.`,
        variant: "destructive"
      });
      return;
    }

    setSelectedDataSources(prev => [...prev, dataSource]);
    setDataSourceModalOpen(false);
    toast({
      title: "Data Source Attached",
      description: `"${dataSource.name}" is now attached. Type your instructions for the AI.`
    });

    // Note: Removed automatic message sending - let users provide their own instructions
  };

  const handleSelectPersona = (persona: Persona) => {
    if (!persona.isAvailable) {
      if (persona.isPurchased) {
        toast({ title: "Provider Not Available", description: `Provider for "${persona.name}" unavailable.`, variant: "destructive" });
      }
      return;
    }

    // Check if this is the concierge persona
    const isConcierge = persona.id === 'concierge' || persona.id === 'concierge-agent';

    if (selectedPersona && selectedPersona.id !== persona.id) {
      if (messages.length > 0 && !window.confirm(`Changing persona starts a new conversation. Continue?`)) {
        return;
      }
      // Reset state for the new persona
      creationAttemptedForPersonaRef.current = null;
      setSelectedPersona(persona);
      resetConversation(); // ✅ FIX: Use safe reset instead of loadConversation("")
      setShowSpecializedComponent(false);

      // Update concierge state
      if (isConcierge) {
        setConciergeActive(true);
        resetConciergeState();
      } else {
        setConciergeActive(false);
      }

      // Explicitly create the new conversation *after* state updates
      console.log(`handleSelectPersona: Explicitly creating conversation for new persona ${persona.id}`);
      createConversation(persona.id, `Conversation with ${persona.name}`)
        .catch(error => console.error("Failed to create conversation from handleSelectPersona:", error));

    } else if (!selectedPersona) {
      // This is the initial selection when the component loads
      creationAttemptedForPersonaRef.current = null; // Ensure it's reset if no persona was selected before
      setSelectedPersona(persona);

      // Update concierge state
      if (isConcierge) {
        setConciergeActive(true);
        resetConciergeState();
      } else {
        setConciergeActive(false);
      }
    }
    setPersonaModalOpen(false);
    toast({ title: "AI Persona Selected", description: `"${persona.name}" will assist.` });
  };

  // Function to handle returning to the concierge
  const handleReturnToConcierge = () => {
    // Find the concierge persona (check both IDs)
    const conciergePersona = availablePersonas.find(p => (p.id === 'concierge' || p.id === 'concierge-agent'));
    if (conciergePersona && conciergePersona.isAvailable) {
      handleSelectPersona(conciergePersona);
    } else {
      toast({
        title: "Concierge Not Available",
        description: "The concierge persona is not available. Please purchase it from the marketplace.",
        variant: "destructive"
      });
    }
  };

  // Set the return to concierge callback
  useEffect(() => {
    setOnReturnToConcierge(handleReturnToConcierge);
    return () => setOnReturnToConcierge(undefined);
  }, [setOnReturnToConcierge, availablePersonas]);

  // Function to extract marketing form data from messages
  const extractMarketingFormData = () => {
    console.log("Extracting marketing form data from messages");
    let foundFormData = false;

    // Look through messages for form data
    for (const msg of messages) {
      if (msg.metadata) {
        // Try to extract marketing form data from the message metadata
        let formData = null;

        if (msg.metadata.marketing_form_data) {
          formData = msg.metadata.marketing_form_data;
          foundFormData = true;
        } else if (msg.metadata.context && msg.metadata.context.marketing_form_data) {
          formData = msg.metadata.context.marketing_form_data;
          foundFormData = true;
        }

        // If we found form data, store it in state
        if (formData) {
          console.log("Found marketing form data in messages:", formData);
          setMarketingFormDefaultValues(formData);
          break;
        }
      }
    }

    // If no form data was found in messages, try to get it from localStorage
    if (!foundFormData) {
      try {
        const storedFormData = localStorage.getItem('lastMarketingFormData');
        if (storedFormData) {
          const formData = JSON.parse(storedFormData);
          console.log("Using marketing form data from localStorage:", formData);
          setMarketingFormDefaultValues(formData);
        }
      } catch (e) {
        console.error("Error parsing stored form data:", e);
      }
    }

    // Reset the flag
    setShouldExtractFormData(false);
  };

  // Effect to extract marketing form data when needed
  useEffect(() => {
    if (shouldExtractFormData) {
      extractMarketingFormData();
    }
  }, [shouldExtractFormData, messages]);

  // Classification handler (still mock)
  const handleSubmitClassification = async (config: ClassificationConfig) => { console.log("Classification submitted:", config); };

  // Marketing content handler - real implementation
  const handleSubmitMarketingContent = async (data: MarketingContentFormData) => {
    console.log("Marketing content submitted:", data);

    // Set loading state
    setIsProcessingSpecializedRequest(true);

    try {
      // Get the current conversation ID
      const currentConversationId = chatMessages[0]?.conversation_id;
      if (!currentConversationId) {
        toast({
          title: "Error",
          description: "No active conversation found. Please start a conversation first.",
          variant: "destructive",
        });
        return;
      }

      // Prepare context with data sources if available
      const context = {
        marketing_form_data: data,
        // Include provider and model explicitly at the top level
        provider: data.provider,
        model: data.model,
        data_sources: selectedDataSources.length > 0 ? selectedDataSources.map(ds => ({
          id: ds.id,
          name: ds.name,
          type: ds.type
        })) : undefined
      };

      // Send a message to trigger the marketing content generation
      await sendMessage(`Generate ${data.content_type} content`, context, [], selectedPersona);

      // Close the marketing form after submission
      setShowMarketingForm(false);

      // Show a toast notification
      toast({
        title: "Content Generation Started",
        description: "Your marketing content is being generated. Please wait a moment.",
      });

      // Store the form data in local storage for potential regeneration
      localStorage.setItem('lastMarketingFormData', JSON.stringify(data));

      // Store the form data in state for pre-filling the form when opened again
      setMarketingFormDefaultValues(data);
    } catch (error) {
      console.error("Error generating marketing content:", error);
      toast({
        title: "Error",
        description: "Failed to generate marketing content. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessingSpecializedRequest(false);
    }
  };

  const toggleSpecializedComponent = () => { setShowSpecializedComponent(!showSpecializedComponent); };
  const generateMockClassificationResults = (type: 'llm' | 'huggingface'): ClassificationResult[] => { return []; };
  const generateMockMarketingContent = (contentType: string): string => { return `Mock content for ${contentType}`; };

  // Handle quick actions from specialized component
  const handleQuickAction = async (actionId: string, actionData?: any) => {
    try {
      setIsProcessingSpecializedRequest(true);

      // For marketing actions, send a message to trigger the marketing tool
      if (['marketing_strategy', 'campaign_strategy', 'social_media_content', 'seo_optimization', 'post_composer'].includes(actionId)) {
        const actionMessages = {
          'marketing_strategy': 'Create a comprehensive marketing strategy',
          'campaign_strategy': 'Develop a campaign plan',
          'social_media_content': 'Generate social media content',
          'seo_optimization': 'Provide SEO optimization recommendations',
          'post_composer': 'Help me compose marketing posts'
        };

        const message = actionMessages[actionId as keyof typeof actionMessages] || `Execute ${actionId}`;
        await sendMessage(message, {}, [], selectedPersona);
      }
      // For analysis actions, send appropriate analysis requests
      else if (['data_analysis', 'data_visualization', 'data_filtering', 'statistical_analysis'].includes(actionId)) {
        const actionMessages = {
          'data_analysis': 'Analyze the available data',
          'data_visualization': 'Create visualizations from the data',
          'data_filtering': 'Help me filter and transform the data',
          'statistical_analysis': 'Perform statistical analysis on the data'
        };

        const message = actionMessages[actionId as keyof typeof actionMessages] || `Execute ${actionId}`;
        await sendMessage(message, {}, [], selectedPersona);
      }
      // For classification actions
      else if (actionId === 'text_classification') {
        await sendMessage('Help me classify text content', {}, [], selectedPersona);
      }
      else {
        // Generic action handler
        await sendMessage(`Execute ${actionId.replace(/_/g, ' ')}`, {}, [], selectedPersona);
      }
    } catch (error) {
      console.error('Error executing quick action:', error);
      toast({
        title: "Error",
        description: "Failed to execute quick action. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessingSpecializedRequest(false);
    }
  };

  const renderVisualization = (msg: MessageType) => {
    if (!msg.metadata) return null;

    // Log the metadata for debugging
    console.log('=== RENDER VISUALIZATION DEBUG ===');
    console.log('Message metadata:', msg.metadata);
    console.log('Message content preview:', msg.content?.substring(0, 100));

    // Check for image content in the message
    const hasImageContent = msg.metadata.content &&
                           Array.isArray(msg.metadata.content) &&
                           msg.metadata.content.some(item => item.type === 'image' && item.src);

    if (hasImageContent) {
      console.log('Found image content in message');
      const imageItem = msg.metadata.content.find(item => item.type === 'image' && item.src);
      console.log('Image item:', imageItem);
      console.log('Image src preview:', imageItem?.src?.substring(0, 50) + '...');
    }

    // Function to handle regeneration of content
    const handleRetry = async () => {
      // For marketing content, we need to regenerate using the same parameters
      if (msg.metadata?.generated_content && msg.metadata?.task_type) {
        try {
          // Extract the original form data
          // First check if it's directly in the metadata
          let formData = {};

          if (msg.metadata.marketing_form_data) {
            console.log("Using marketing_form_data from metadata", msg.metadata.marketing_form_data);
            formData = msg.metadata.marketing_form_data;
          } else if (msg.metadata.context && msg.metadata.context.marketing_form_data) {
            console.log("Using marketing_form_data from context", msg.metadata.context.marketing_form_data);
            formData = msg.metadata.context.marketing_form_data;
          } else {
            // Try to get the form data from local storage as a fallback
            try {
              const storedFormData = localStorage.getItem('lastMarketingFormData');
              if (storedFormData) {
                formData = JSON.parse(storedFormData);
                console.log("Using marketing_form_data from localStorage", formData);
              } else {
                console.log("No marketing_form_data found in localStorage, using empty object");
              }
            } catch (e) {
              console.error("Error parsing stored form data:", e);
              console.log("No marketing_form_data found, using empty object");
            }
          } // Moved the closing brace for the else block here

          // Ensure we have the content_type in the form data, checking if formData is not empty
          // Moved this check inside the try block
          if (typeof formData === 'object' && formData !== null && !('content_type' in formData) && msg.metadata.task_type) {
            (formData as any).content_type = msg.metadata.task_type;
          }

          // Get provider and model from the most reliable source
          const provider = msg.metadata.provider ||
                          (formData as any).provider ||
                          (msg.metadata.context && msg.metadata.context.provider);

          const model = msg.metadata.model ||
                       (formData as any).model ||
                       (msg.metadata.context && msg.metadata.context.model);

          // Prepare context with the same parameters
          const context = {
            marketing_form_data: formData,
            provider: provider,
            model: model,
            data_sources: selectedDataSources.length > 0 ? selectedDataSources.map(ds => ({
              id: ds.id,
              name: ds.name,
              type: ds.type
            })) : undefined,
            is_regeneration: true // Flag to indicate this is a regeneration request
          };

          console.log("Regenerating content with context:", context);

          // Send a message to trigger regeneration
          await sendMessage(`Regenerate ${msg.metadata.task_type} content`, context, [], selectedPersona);

          toast({
            title: "Regenerating Content",
            description: "Your request to regenerate content has been sent.",
          });
        } catch (error) {
          console.error("Error regenerating content:", error);
          toast({
            title: "Error",
            description: "Failed to regenerate content. Please try again.",
            variant: "destructive",
          });
        }
      }
    };

    // For marketing content, ensure we pass the response in the metadata
    if (msg.metadata.generated_content && msg.metadata.task_type) {
      // Make sure the response is included in the metadata for the visualization
      const enhancedMetadata = {
        ...msg.metadata,
        response: msg.metadata.response || msg.content
      };
      return <VisualizationRenderer metadata={enhancedMetadata} onRetry={handleRetry} />;
    }

    // For data preview/profile, ensure we pass the metadata properly
    if (msg.metadata.data_preview || msg.metadata.data_profile) {
      return <VisualizationRenderer metadata={msg.metadata} />;
    }

    // For query results, ensure we pass the metadata properly
    if (msg.metadata.query_result) {
      return <VisualizationRenderer metadata={msg.metadata} />;
    }

    // For visualization results, ensure we pass the metadata properly
    if (msg.metadata.visualization) {
      console.log('Found visualization in metadata:', msg.metadata.visualization);
      console.log('🔍 DataChat: Full message metadata:', msg.metadata);
      console.log('🔍 DataChat: Visualization type:', msg.metadata.visualization.type);
      return <VisualizationRenderer metadata={msg.metadata} />;
    }

    // For messages with image content, create a visualization
    if (hasImageContent) {
      // Find the image content
      const imageContent = msg.metadata.content.find(item => item.type === 'image' && item.src);

      // Create a visualization object
      const visualizationMetadata = {
        ...msg.metadata,
        visualization: {
          type: 'chart',
          title: msg.metadata.prompt || 'Visualization',
          description: 'Generated visualization',
          data: {
            image: imageContent.src
          }
        }
      };

      return <VisualizationRenderer metadata={visualizationMetadata} />;
    }

    // Check for analysis agent specific metadata that should render visualizations
    if (msg.metadata.data_info || msg.metadata.task_type === 'data_profile_summary') {
      return <VisualizationRenderer metadata={msg.metadata} />;
    }

    // Check for PandasAI analysis results with content
    if (msg.metadata.content && Array.isArray(msg.metadata.content) && msg.metadata.content.length > 0) {
      const hasVisualizationContent = msg.metadata.content.some((item: any) =>
        item.type === 'image' || item.type === 'table' ||
        (item.type === 'text' && item.text && item.text.includes('|'))
      );

      if (hasVisualizationContent) {
        return <VisualizationRenderer metadata={msg.metadata} />;
      }
    }

    // Check for task_type indicating analysis was performed
    if (msg.metadata.task_type && ['pandasai_analysis', 'pandasai_visualization', 'pandasai_query'].includes(msg.metadata.task_type)) {
      return <VisualizationRenderer metadata={msg.metadata} />;
    }

    // Only render visualization if there's actual visualization data
    // Remove the fallback that was rendering empty visualizations
    return null;
  };

  // Function to parse message content and render text/buttons with markdown support, handling line breaks
  const renderMessageContent = (content: string): React.ReactNode[] => {
    // Check if content contains action buttons
    const hasActionButtons = /\[([^\]]+)\]\(action:([^)]+)\)/g.test(content);

    if (!hasActionButtons) {
      // If no action buttons, render as full markdown
      const markdownComponents: Components = {
        // Custom components for better styling
        h1: ({ children, ...props }) => <h1 className="text-xl font-bold mb-3 mt-4 first:mt-0 text-gray-900 border-b border-gray-200 pb-2" {...props}>{children}</h1>,
        h2: ({ children, ...props }) => <h2 className="text-lg font-semibold mb-2 mt-3 first:mt-0 text-gray-800" {...props}>{children}</h2>,
        h3: ({ children, ...props }) => <h3 className="text-base font-medium mb-2 mt-3 first:mt-0 text-gray-700" {...props}>{children}</h3>,
        p: ({ children, ...props }) => <p className="mb-2 last:mb-0" {...props}>{children}</p>,
        ul: ({ children, ...props }) => <ul className="list-disc list-inside mb-3 space-y-1" {...props}>{children}</ul>,
        ol: ({ children, ...props }) => <ol className="list-decimal list-inside mb-3 space-y-1" {...props}>{children}</ol>,
        li: ({ children, ...props }) => <li className="ml-2" {...props}>{children}</li>,
        strong: ({ children, ...props }) => <strong className="font-semibold" {...props}>{children}</strong>,
        em: ({ children, ...props }) => <em className="italic" {...props}>{children}</em>,
        code: ({ children, ...props }) => <code className="bg-gray-200 px-1 py-0.5 rounded text-sm font-mono" {...props}>{children}</code>,
        pre: ({ children, ...props }) => <pre className="bg-gray-100 p-3 rounded-md overflow-x-auto mb-3" {...props}>{children}</pre>,
        blockquote: ({ children, ...props }) => <blockquote className="border-l-4 border-gray-300 pl-4 italic mb-3" {...props}>{children}</blockquote>,
        a: ({ href, children, ...props }) => <a href={href} className="text-blue-600 hover:text-blue-800 underline" target="_blank" rel="noopener noreferrer" {...props}>{children}</a>,
        // Enhanced table styling for analysis results
        table: ({ children, ...props }) => (
          <div className="overflow-x-auto my-4 border-2 border-gray-300 rounded-lg shadow-lg">
            <table className="w-full border-collapse bg-white" {...props}>
              {children}
            </table>
          </div>
        ),
        thead: ({ children, ...props }) => (
          <thead className="bg-gradient-to-r from-brand-600 to-brand-700 text-white" {...props}>{children}</thead>
        ),
        th: ({ children, ...props }) => (
          <th className="border border-gray-400 px-4 py-3 text-left font-bold text-white text-sm uppercase tracking-wide" {...props}>
            {children}
          </th>
        ),
        tbody: ({ children, ...props }) => (
          <tbody className="divide-y divide-gray-200" {...props}>{children}</tbody>
        ),
        tr: ({ children, ...props }) => (
          <tr className="hover:bg-gray-50 transition-colors duration-150" {...props}>{children}</tr>
        ),
        td: ({ children, ...props }) => (
          <td className="border border-gray-300 px-4 py-3 text-gray-900 font-medium" {...props}>
            {children}
          </td>
        ),
      };

      return [
        <div key="markdown-content" className="prose prose-sm max-w-none prose-gray markdown-content">
          <ReactMarkdown components={markdownComponents}>
            {content}
          </ReactMarkdown>
        </div>
      ];
    }

    // If content has action buttons, use the original parsing logic with markdown support
    const lines = content.split('\n');
    const renderedLines: React.ReactNode[] = [];

    lines.forEach((line, lineIndex) => {
        const lineParts: React.ReactNode[] = [];
        let currentLineIndex = 0;
        const lineRegex = /\[([^\]]+)\]\(action:([^)]+)\)/g; // Need to re-apply regex per line
        let lineMatch;

        while ((lineMatch = lineRegex.exec(line)) !== null) {
             if (lineMatch.index > currentLineIndex) {
                 const textBefore = line.substring(currentLineIndex, lineMatch.index);
                 if (textBefore.trim()) {
                   const inlineComponents: Components = {
                     p: ({ children, ...props }) => <span {...props}>{children}</span>,
                     h1: ({ children, ...props }) => <strong className="text-lg" {...props}>{children}</strong>,
                     h2: ({ children, ...props }) => <strong className="text-base" {...props}>{children}</strong>,
                     h3: ({ children, ...props }) => <strong {...props}>{children}</strong>,
                     strong: ({ children, ...props }) => <strong className="font-semibold" {...props}>{children}</strong>,
                     em: ({ children, ...props }) => <em className="italic" {...props}>{children}</em>,
                     code: ({ children, ...props }) => <code className="bg-gray-200 px-1 py-0.5 rounded text-sm font-mono" {...props}>{children}</code>,
                   };

                   lineParts.push(
                     <span key={`line-${lineIndex}-text-${currentLineIndex}`} className="inline">
                       <ReactMarkdown components={inlineComponents}>
                         {textBefore}
                       </ReactMarkdown>
                     </span>
                   );
                 }
             }
             const buttonText = lineMatch[1];
             const actionKey = lineMatch[2];
             lineParts.push(
                <Button
                  key={`line-${lineIndex}-action-${lineMatch.index}-${actionKey}`}
                  variant="outline"
                  size="sm"
                  className="mx-1 my-0.5 h-auto py-1 px-2 text-sm whitespace-normal text-left inline-block align-middle" // Added align-middle
                  onClick={() => {
                    const finalActionKey = actionKey.trim().toLowerCase();
                    console.log(`Action button clicked (line loop): Original Key='${actionKey}', Processed Key='${finalActionKey}'`);

                    // Define marketing action keys that should open the form
                    const marketingActionKeys = [
                      'marketing_strategy',
                      'create_marketing_strategy',
                      'campaign_strategy',
                      'develop_campaign_plan',
                      'social_media_content',
                      'generate_social_media_content',
                      'seo_optimization',
                      'post_composer'
                    ];

                    // Define analysis action keys that should be sent as specific messages
                    const analysisActionKeys = [
                      'analyze_distribution',
                      'clean_data',
                      'create_visualization',
                      'find_correlations',
                      'statistical_summary',
                      'outlier_detection',
                      'execute_code',
                      'machine_learning',
                      'data_storytelling',
                      'visualize_distributions',
                      'identify_outliers',
                      'correlation_analysis',
                      'explain_chart',
                      'different_chart',
                      'analyze_pattern',
                      'statistical_analysis',
                      'visualize_distribution',
                      'visualize_trends',
                      'visualize_comparisons'
                    ];

                    const shouldOpenForm = marketingActionKeys.includes(finalActionKey);
                    const isAnalysisAction = analysisActionKeys.includes(finalActionKey);

                    // Act based on the determination
                    if (shouldOpenForm) {
                      console.log(`Opening marketing form for key: '${finalActionKey}'`);

                      // Set default values based on the action key
                      const contentTypeMapping: Record<string, string> = {
                        'marketing_strategy': 'marketing_strategy',
                        'campaign_strategy': 'campaign_strategy',
                        'social_media_content': 'social_media_content',
                        'seo_optimization': 'seo_optimization',
                        'blog_content': 'blog_content',
                        'email_marketing': 'email_marketing',
                        'ad_copy': 'ad_copy',
                        'press_release': 'press_release',
                        'competitor_analysis': 'competitor_analysis',
                        'audience_research': 'audience_research',
                        'market_analysis': 'market_analysis',
                        'data_driven_analysis': 'data_driven_analysis',
                        'data_driven_campaigns': 'data_driven_campaigns'
                      };

                      const contentType = contentTypeMapping[finalActionKey] || 'marketing_strategy';
                      setMarketingFormDefaultValues({
                        content_type: contentType,
                        // Set appropriate defaults based on content type
                        tone: getDefaultToneForContentType(contentType),
                        document_format: getDefaultFormatForContentType(contentType)
                      });

                      setShouldExtractFormData(true);
                      setShowMarketingForm(true);
                      setShowSpecializedComponent(false);
                    } else if (isAnalysisAction) {
                      console.log(`Sending analysis action for key: '${finalActionKey}'`);
                      // Create more descriptive messages for analysis actions
                      const analysisActionMessages: Record<string, string> = {
                        'analyze_distribution': 'Analyze the data distribution and show me the patterns',
                        'clean_data': 'Clean the data by handling missing values and inconsistencies',
                        'create_visualization': 'Create visualizations to show patterns in the data',
                        'find_correlations': 'Find correlations between variables in the data',
                        'statistical_summary': 'Provide a statistical summary of the data',
                        'outlier_detection': 'Detect outliers in the data',
                        'execute_code': 'Execute custom Python code for analysis',
                        'machine_learning': 'Apply machine learning techniques to the data',
                        'data_storytelling': 'Generate narrative insights from the data',
                        'visualize_distributions': 'Create distribution visualizations',
                        'identify_outliers': 'Identify and analyze outliers',
                        'correlation_analysis': 'Perform correlation analysis',
                        'explain_chart': 'Explain the chart and its insights',
                        'different_chart': 'Create a different type of visualization',
                        'analyze_pattern': 'Analyze the patterns shown in the data',
                        'statistical_analysis': 'Perform detailed statistical analysis',
                        'visualize_distribution': 'Visualize data distribution',
                        'visualize_trends': 'Visualize trends over time',
                        'visualize_comparisons': 'Create comparison visualizations'
                      };

                      const message = analysisActionMessages[finalActionKey] || finalActionKey.replace(/_/g, ' ');
                      const context: any = {};
                      if (selectedDataSources.length > 0) {
                        context.data_sources = selectedDataSources.map(ds => ({
                          id: ds.id,
                          name: ds.name,
                          type: ds.type
                        }));
                      }
                      sendMessage(message, context, [], selectedPersona);
                    } else {
                      console.log(`Sending message for key: '${finalActionKey}'`);
                      // Default behavior: Send the action key as a message
                      const context: any = {};
                      if (selectedDataSources.length > 0) {
                        context.data_sources = selectedDataSources.map(ds => ({
                          id: ds.id,
                          name: ds.name,
                          type: ds.type
                        }));
                      }
                      sendMessage(finalActionKey.replace(/_/g, ' '), context, [], selectedPersona);
                    }
                  }}
                >
                  {buttonText}
                </Button>
             );
             currentLineIndex = lineRegex.lastIndex;
        }
         if (currentLineIndex < line.length) {
             const remainingText = line.substring(currentLineIndex);
             if (remainingText.trim()) {
               const inlineComponents: Components = {
                 p: ({ children, ...props }) => <span {...props}>{children}</span>,
                 h1: ({ children, ...props }) => <strong className="text-lg" {...props}>{children}</strong>,
                 h2: ({ children, ...props }) => <strong className="text-base" {...props}>{children}</strong>,
                 h3: ({ children, ...props }) => <strong {...props}>{children}</strong>,
                 strong: ({ children, ...props }) => <strong className="font-semibold" {...props}>{children}</strong>,
                 em: ({ children, ...props }) => <em className="italic" {...props}>{children}</em>,
                 code: ({ children, ...props }) => <code className="bg-gray-200 px-1 py-0.5 rounded text-sm font-mono" {...props}>{children}</code>,
               };

               lineParts.push(
                 <span key={`line-${lineIndex}-text-${currentLineIndex}`} className="inline">
                   <ReactMarkdown components={inlineComponents}>
                     {remainingText}
                   </ReactMarkdown>
                 </span>
               );
             }
         }

         // If no buttons were found in this line, render as markdown
         if (lineParts.length === 0 && line.trim()) {
           const inlineComponents: Components = {
             p: ({ children, ...props }) => <span {...props}>{children}</span>,
             h1: ({ children, ...props }) => <strong className="text-lg" {...props}>{children}</strong>,
             h2: ({ children, ...props }) => <strong className="text-base" {...props}>{children}</strong>,
             h3: ({ children, ...props }) => <strong {...props}>{children}</strong>,
             strong: ({ children, ...props }) => <strong className="font-semibold" {...props}>{children}</strong>,
             em: ({ children, ...props }) => <em className="italic" {...props}>{children}</em>,
             code: ({ children, ...props }) => <code className="bg-gray-200 px-1 py-0.5 rounded text-sm font-mono" {...props}>{children}</code>,
           };

           lineParts.push(
             <span key={`line-${lineIndex}`} className="inline">
               <ReactMarkdown components={inlineComponents}>
                 {line}
               </ReactMarkdown>
             </span>
           );
         }

         // Use a div for each line to preserve line breaks, add margin between lines
         if (lineParts.length > 0) {
             renderedLines.push(<div key={`line-${lineIndex}`} className="mb-1 markdown-content">{lineParts}</div>);
         } else if (line.trim() === '') {
             // Add empty lines as spacing
             renderedLines.push(<br key={`line-${lineIndex}`} />);
         }
    });

    return renderedLines; // Return array of line divs
  };

  // Calculate the effective dataSourceId directly before rendering the form section
  // For now, use the first selected data source for marketing form compatibility
  let effectiveDataSourceId: string | undefined = undefined;
  const firstDataSource = selectedDataSources.length > 0 ? selectedDataSources[0] : null;
  if (firstDataSource) {
    if (firstDataSource.type === "file") {
      // First try to get file_id from source_metadata (correct location based on model)
      if (firstDataSource.source_metadata?.file_id) {
        effectiveDataSourceId = firstDataSource.source_metadata.file_id;
      }
      // Try to get file_id directly from the data source ID for the attached file
      else if (firstDataSource.id) {
        // For file data sources, the ID might be the file ID itself
        effectiveDataSourceId = firstDataSource.id;
      }
      // Fallback to old method using metadata.file_path
      else {
        const filePath = firstDataSource.metadata?.file_path;
        if (typeof filePath === 'string') {
          const parts = filePath.split('/');
          const filename = parts.pop(); // Get last part (filename)
          if (filename) {
            effectiveDataSourceId = filename.split('.')[0]; // Get part before extension
          }
        }
      }

      // Add a warning if the ID is still missing
      if (!effectiveDataSourceId) {
         console.warn("Could not extract file ID from data source");
      }
    } else {
      // For non-file data sources, use the data source ID directly
      effectiveDataSourceId = firstDataSource.id;
    }
  }

  return (
    <DashboardLayout
      showFloatingAI={false}
    >
      {/* Modals */}
      <Dialog open={conversationsModalOpen} onOpenChange={setConversationsModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Conversations</DialogTitle>
          </DialogHeader>
          <ConversationList
            onSelectConversation={handleSelectConversation}
            currentConversationId={chatMessages[0]?.conversation_id}
            onCreateNewConversation={handleCreateNewConversation}
            selectedPersona={selectedPersona}
            availablePersonas={availablePersonas}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={dataSourceModalOpen} onOpenChange={setDataSourceModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Attach Data Source</DialogTitle>
          </DialogHeader>
          <DataSourceSelector
            onSelectDataSource={handleSelectDataSource}
            currentDataSourceId={selectedDataSources.length > 0 ? selectedDataSources[selectedDataSources.length - 1]?.id : undefined}
            refreshTrigger={refreshTrigger}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={personaModalOpen} onOpenChange={setPersonaModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Select Persona</DialogTitle>
          </DialogHeader>
          <PersonaSelector
            onSelect={(personaId) => {
              const persona = availablePersonas.find(p => p.id === personaId);
              if (persona) handleSelectPersona(persona);
            }}
            currentPersona={selectedPersona}
            availablePersonas={availablePersonas.filter(p => p.isPurchased)}
            isLoading={isLoadingPersonas}
            onGoToMarketplace={() => {
              setPersonaModalOpen(false);
              navigate("/ai-marketplace");
            }}
          />
        </DialogContent>
      </Dialog>

      <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }} className="relative">
        {/* Concierge Onboarding */}
        <ConciergeOnboarding
          isActive={isConciergeActive && showOnboarding}
          onDismiss={() => setShowOnboarding(false)}
        />
        <div className="flex-1">
          {/* Header */}
          <div className="sticky top-0 z-50 bg-white border-b border-gray-200 flex items-center justify-between p-4">
            {/* Left Header */}
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setConversationsModalOpen(true)}
                className="md:hidden"
              >
                <Menu className="h-5 w-5" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold">Data Chat</h1>
                <div
                  className="md:hidden flex items-center text-sm text-gray-600 -mt-1 cursor-pointer"
                  onClick={() => setPersonaModalOpen(true)}
                >
                  <User className="h-3 w-3 mr-1 flex-shrink-0" />
                  {selectedPersona ? (
                    <span className="whitespace-nowrap">{selectedPersona.name}</span>
                  ) : (
                    <span className="text-brand-500 font-medium">Select Persona</span>
                  )}
                </div>
              </div>
              {/* Enhanced Conversation State Indicator */}
              <div className="hidden md:block">
                <ConversationStateIndicator
                  conversationId={chatMessages.length > 0 ? chatMessages[0]?.conversation_id : undefined}
                  personaName={selectedPersona?.name}
                  isRestoring={isRestoringConversation}
                  connectionStatus={connectionStatus}
                  hasUnsavedChanges={false}
                  lastSaved={chatMessages.length > 0 ? new Date(chatMessages[chatMessages.length - 1]?.created_at || Date.now()) : undefined}
                />
              </div>
            </div>
            {/* Right Header (Desktop) */}
            <div className="hidden md:flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPersonaModalOpen(true)}
                className="flex items-center gap-2 min-w-0"
              >
                <User className="h-4 w-4 flex-shrink-0" />
                {selectedPersona ? (
                  <span className="flex items-center gap-1">
                    <span className="whitespace-nowrap">{selectedPersona.name}</span>
                  </span>
                ) : (
                  "Select Persona"
                )}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setConversationsModalOpen(true)}
                className="flex items-center gap-2"
              >
                <MessageCircle className="h-4 w-4" />
                Conversations
              </Button>
              {selectedPersona && (
                <Button
                  variant={showSpecializedComponent ? "default" : "outline"}
                  size="sm"
                  onClick={toggleSpecializedComponent}
                  className="flex items-center gap-2"
                >
                  <Settings className="h-4 w-4" />
                  Specialized Tools
                </Button>
              )}
            </div>
             {/* Right Header (Mobile) */}
             <div className="flex md:hidden items-center gap-1">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setPersonaModalOpen(true)}
                  className="h-8 w-8"
                  title="Select Persona"
                >
                  <User className="h-4 w-4" />
                </Button>
                {selectedPersona && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={toggleSpecializedComponent}
                    className={`h-8 w-8 ${showSpecializedComponent ? 'bg-brand-100' : ''}`}
                    title="Specialized Tools"
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                )}
             </div>
          </div>

          {/* Chat Card */}
          <Card className="flex flex-col min-h-[600px] mt-6 relative">
            {/* Error/Info Banners */}
            {!hasAvailablePersonas && !isLoadingPersonas && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-t-md relative">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ShoppingCart className="h-5 w-5 mr-2" />
                  </div>
                  <div>
                    <p className="font-bold">No Available Personas</p>
                    <p className="text-sm">Purchase personas from the marketplace.</p>
                  </div>
                  <div className="ml-auto">
                    <Button
                      size="sm"
                      onClick={() => navigate("/ai-marketplace")}
                      className="bg-red-600 hover:bg-red-700 text-white"
                    >
                      Go to Marketplace
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Concierge Workflow Stage Indicator */}
            {isConciergeActive && conciergeState && (
              <div className="border-b border-gray-200 p-3 relative">
                <div className="absolute right-3 top-3">
                  <ConciergeHelpModal />
                </div>
                <WorkflowStageIndicator
                  currentStage={conciergeState.stage as WorkflowStage}
                  className="mx-auto max-w-2xl"
                />
              </div>
            )}

            {/* Return to Concierge Button (when not in concierge) */}
            {!isConciergeActive && selectedPersona && selectedPersona.id !== 'concierge' && (
              <div className="border-b border-gray-200 p-2 flex justify-end">
                <ReturnToConcierge onReturn={handleReturnToConcierge} />
              </div>
            )}

            {/* Specialized Component Area - Now includes Marketing Form */}
            {showMarketingForm && (
              <motion.div initial={{ opacity: 0, height: 0 }} animate={{ opacity: 1, height: 'auto' }} exit={{ opacity: 0, height: 0 }} className="border-b p-4">
                {selectedDataSources.length === 0 && attachedFiles.length === 0 && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 text-yellow-500 mr-2">
                        <Info className="h-5 w-5" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-yellow-800">No Data Sources Attached</h4>
                        <p className="text-sm text-yellow-700 mt-1">
                          Attach data sources to auto-fill this form with relevant content, or upload business documents to improve content generation.
                        </p>
                        <div className="flex gap-2 mt-3">
                          <Button
                            variant="outline"
                            size="sm"
                            className="bg-white"
                            onClick={() => setDataSourceModalOpen(true)}
                          >
                            <Database className="h-4 w-4 mr-2" />
                            Attach Data Source
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="bg-white"
                            onClick={handleUploadFileClick}
                            disabled={isLoading}
                          >
                            <Upload className="h-4 w-4 mr-2" />
                            Upload File
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                {/* Show error only if file_id is missing from the correct location (source_metadata) */}
                {firstDataSource?.type === "file" &&
                 !firstDataSource?.source_metadata?.file_id && (
                  <div className="bg-orange-50 border border-orange-200 rounded-md p-4 mb-4">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 text-orange-500 mr-2">
                        <Info className="h-5 w-5" />
                      </div>
                      <div>
                        <h4 className="font-medium text-orange-800">Invalid File Reference</h4>
                        <p className="text-sm text-orange-700 mt-1">
                          This data source references a file that doesn't exist. Please attach a different data source.
                        </p>
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-2 bg-white"
                          onClick={() => setDataSourceModalOpen(true)}
                        >
                          Choose Different Data Source
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
                {/* Removed logging block from here */}
                <MarketingContentForm
                  onSubmit={handleSubmitMarketingContent} // Reuse existing handler
                  isLoading={isProcessingSpecializedRequest} // Reuse loading state
                  // Pass the calculated ID
                  dataSourceId={effectiveDataSourceId}
                  onClose={() => setShowMarketingForm(false)} // Add close handler
                  defaultValues={marketingFormDefaultValues} // Pass the default values
                  // File upload props - use first attached file for compatibility
                  onFileUpload={handleUploadFileClick}
                  attachedFile={attachedFiles.length > 0 ? attachedFiles[0] : null}
                  onRemoveFile={() => {
                    if (attachedFiles.length > 0) {
                      setAttachedFiles(prev => prev.slice(1)); // Remove first file
                    }
                    if (fileInputRef.current) fileInputRef.current.value = "";
                  }}
                  isUploading={false} // We handle uploading in the main chat component
                />
              </motion.div>
            )}
            {showSpecializedComponent && !showMarketingForm && selectedPersona && ( // Render only if marketing form isn't shown
              <SpecializedComponentRenderer
                persona={selectedPersona}
                onClose={toggleSpecializedComponent}
                onSubmitClassification={handleSubmitClassification}
                onSubmitMarketingContent={handleSubmitMarketingContent} // Keep this? Maybe redundant now.
                onQuickAction={handleQuickAction}
                classificationResults={classificationResults}
                classificationConfig={classificationConfig}
                generatedMarketingContent={generatedMarketingContent}
                isLoading={isProcessingSpecializedRequest}
              />
            )}

            {/* Interactive Chart System - Show for data analysis personas */}
            {selectedPersona &&
             (selectedPersona.name.toLowerCase().includes('analyst') ||
              selectedPersona.name.toLowerCase().includes('analysis') ||
              selectedPersona.id === 'analysis') &&
             selectedDataSources.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="mb-4"
              >
                <InteractiveChartSystem
                  dataSource={selectedDataSources.map(ds => ds.name).join(', ')}
                  onChartRequest={async (request) => {
                    // Handle chart generation request
                    const chartQuery = `${request.query} using ${request.type} chart with ${request.style} style`;
                    const context: any = {
                      data_sources: selectedDataSources.map(ds => ({
                        id: ds.id,
                        name: ds.name,
                        type: ds.type
                      })),
                      chart_request: {
                        type: request.type,
                        style: request.style,
                        size: request.size
                      }
                    };

                    // Send the chart request as a message
                    await sendMessage(chartQuery, context, [], selectedPersona);
                  }}
                  currentChart={
                    // Find the most recent chart in messages
                    (() => {
                      const recentMessages = [...chatMessages].reverse();
                      const chartMessage = recentMessages.find(msg =>
                        msg.sender === 'ai' &&
                        msg.metadata?.visualization?.type === 'chart' &&
                        msg.metadata?.visualization?.data?.image
                      );

                      if (chartMessage) {
                        return {
                          title: chartMessage.metadata.visualization.title || 'Recent Visualization',
                          description: chartMessage.metadata.visualization.description,
                          data: chartMessage.metadata.visualization.data
                        };
                      }
                      return undefined;
                    })()
                  }
                  isGenerating={isLoading}
                />
              </motion.div>
            )}

            {/* Messages Area with Concierge Panel */}
            <div className="flex-1 p-4 relative flex pb-24">
              {/* Main Messages Area */}
              <div className="flex-1 relative">
                {/* Conversation Restoration Loader */}
                <ConversationRestorationLoader
                  isRestoring={isRestoringConversation}
                  personaName={selectedPersona?.name}
                  conversationId={chatMessages.length > 0 ? chatMessages[0]?.conversation_id : undefined}
                />

                {/* Refresh Button */}
                {messages.length > 1 && (
                  <div className="absolute top-2 right-2 z-10">
                    <Button
                      variant="outline"
                      size="sm"
                      className="bg-white/80 backdrop-blur-sm hover:bg-white"
                      onClick={refreshConversation}
                      disabled={isRefreshing || isLoading || isAutoRefreshing}
                      title="Refresh conversation to reload all messages (useful if some messages are missing after page reload)"
                    >
                      {isRefreshing || isAutoRefreshing ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-1" />
                      ) : (
                        <RefreshCw className="h-4 w-4 mr-1" />
                      )}
                      {isRefreshing || isAutoRefreshing ? "Refreshing..." : "Refresh"}
                    </Button>
                  </div>
                )}

                {/* Initial Loading */}
                {messages.length === 0 && isLoading && (
                  <div className="flex flex-col items-center justify-center h-full">
                    <Loader2 className="h-8 w-8 animate-spin text-brand-500 mb-4" />
                    <p className="text-gray-500">Initializing conversation...</p>
                  </div>
                )}

                {/* Auto-Refresh Indicator */}
                {isAutoRefreshing && (
                  <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 text-blue-500 mr-2">
                        <Loader2 className="h-5 w-5 animate-spin" />
                      </div>
                      <div>
                        <h4 className="font-medium text-blue-800">Auto-refreshing...</h4>
                      </div>
                    </div>
                  </div>
                )}

                {/* Concierge Workflow Button (only when in concierge mode) */}
                {isConciergeActive && conciergeState && (
                  <div className="absolute top-2 right-2 z-10">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      initial={{ opacity: 0, y: -10 }}
                      animate={{
                        opacity: 1,
                        y: 0,
                        transition: {
                          type: "spring",
                          stiffness: 500,
                          damping: 30,
                          delay: 0.3
                        }
                      }}
                    >
                      <Button
                        variant="outline"
                        size="sm"
                        className="bg-white/80 backdrop-blur-sm hover:bg-white flex items-center gap-1 shadow-sm border-brand-100"
                        onClick={() => setShowConciergePanel(!showConciergePanel)}
                      >
                        <Compass className="h-4 w-4 text-brand-500" />
                        <span className="hidden md:inline">Workflow</span>
                        {showConciergePanel ? (
                          <ChevronLeft className="h-4 w-4 md:hidden" />
                        ) : (
                          <ChevronRight className="h-4 w-4 md:hidden" />
                        )}
                      </Button>
                    </motion.div>
                  </div>
                )}

                {/* Message List */}
                {messages.map((msg, index) => {
                  // Filter out processing messages to avoid duplicate indicators
                  if (msg.sender === 'ai' &&
                      (msg.content === "Processing your request..." ||
                       msg.content === "Processing..." ||
                       msg.content === "Generating response...") &&
                      msg.metadata?.status === "processing" &&
                      msg.metadata?.processing === true) {
                    console.log(`Filtering out processing message ${msg.id}: "${msg.content}"`);
                    return null;
                  }

                  // Debug logging for all AI messages
                  if (msg.sender === 'ai') {
                    console.log(`🤖 AI Message ${msg.id}: content="${msg.content.substring(0, 100)}...", status=${msg.metadata?.status}, processing=${msg.metadata?.processing}`);
                  }

                  // Check if this is a marketing content message
                  const isMarketingContent = msg.sender === 'ai' &&
                                            msg.metadata?.generated_content === true &&
                                            msg.metadata?.task_type;

                  const attachment = msg.metadata?.attachment || (msg as any).attachment; // Check both metadata and direct property

                  // Check if this message has a data preview visualization that should fill the container
                  const hasDataPreview = msg.metadata?.data_preview || msg.metadata?.data_profile;
                  const shouldFillContainer = hasDataPreview;



                  // Get delivery status for user messages
                  const deliveryStatus = msg.sender === 'user' ? messageDeliveryStatus[msg.id] : undefined;

                  // Prepare attachments for EnhancedMessage
                  const messageAttachments = msg.metadata?.attachments?.map((att: any) => ({
                    id: att.id || `${msg.id}-${att.name}`,
                    name: att.name,
                    type: att.type || 'unknown',
                    size: att.size
                  })) || [];

                  // For complex messages with special rendering, use custom rendering
                  if (isMarketingContent || (msg.metadata?.is_persona_recommendation && msg.metadata.recommended_personas)) {
                    return (
                      <motion.div key={msg.id || `message-${index}`} initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} className={`mb-4 ${shouldFillContainer ? 'w-full' : 'max-w-3xl'} ${msg.sender === 'user' ? 'ml-auto' : ''}`}>
                        <div className={`p-3 rounded-lg ${msg.sender === 'user' ? 'bg-brand-500 text-white' : 'bg-gray-100 text-gray-800'}`}>
                          <div className="flex justify-between items-start"> {/* Use items-start */}
                            <div className="flex-1 mr-2 prose prose-sm max-w-none"> {/* Added prose classes */}
                            {isMarketingContent ? (
                              <div className="flex items-center gap-2">
                                <BarChart4 className="h-4 w-4 text-brand-500" />
                                <span>I've generated your {msg.metadata.task_type.replace(/_/g, ' ')} content below.</span>
                              </div>
                            ) : msg.metadata?.is_persona_recommendation && msg.metadata.recommended_personas ? (
                              <>
                                {renderMessageContent(msg.content)}
                                <div className="mt-4 space-y-3">
                                  {/* Group personas by category */}
                                  {msg.metadata.persona_query_response && (
                                    <>
                                      {/* Owned Personas */}
                                      {msg.metadata.recommended_personas.filter((p: any) => p.category === 'owned').length > 0 && (
                                        <div className="space-y-2">
                                          <div className="text-sm font-medium text-green-700 bg-green-50 px-2 py-1 rounded">
                                            🎯 Your Owned Personas ({msg.metadata.recommended_personas.filter((p: any) => p.category === 'owned').length})
                                          </div>
                                          {msg.metadata.recommended_personas.filter((p: any) => p.category === 'owned').map((persona: any) => (
                                            <Button
                                              key={persona.id}
                                              variant="outline"
                                              size="sm"
                                              className="w-full justify-start text-left h-auto py-3 border-green-200 hover:border-green-300 hover:bg-green-50"
                                              onClick={() => {
                                                const fullPersona = availablePersonas.find(p => p.id === persona.id);
                                                if (fullPersona) {
                                                  handleSelectPersona(fullPersona);
                                                } else {
                                                  toast({ title: "Persona Not Found", description: `Could not find details for ${persona.name}.`, variant: "destructive" });
                                                }
                                              }}
                                            >
                                              <div className="flex flex-col w-full">
                                                <div className="flex items-center justify-between">
                                                  <span className="font-semibold text-green-800">{persona.name}</span>
                                                  <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">OWNED</span>
                                                </div>
                                                {persona.description && <span className="text-xs text-gray-600 mt-1">{persona.description}</span>}
                                                {persona.capabilities && persona.capabilities.length > 0 && (
                                                  <span className="text-xs text-gray-500 mt-1">
                                                    Capabilities: {persona.capabilities.slice(0, 3).join(', ')}
                                                    {persona.capabilities.length > 3 && '...'}
                                                  </span>
                                                )}
                                              </div>
                                            </Button>
                                          ))}
                                        </div>
                                      )}

                                      {/* Free Personas */}
                                      {msg.metadata.recommended_personas.filter((p: any) => p.category === 'free').length > 0 && (
                                        <div className="space-y-2">
                                          <div className="text-sm font-medium text-blue-700 bg-blue-50 px-2 py-1 rounded">
                                            🆓 Free Personas Available ({msg.metadata.recommended_personas.filter((p: any) => p.category === 'free').length})
                                          </div>
                                          {msg.metadata.recommended_personas.filter((p: any) => p.category === 'free').map((persona: any) => (
                                            <Button
                                              key={persona.id}
                                              variant="outline"
                                              size="sm"
                                              className="w-full justify-start text-left h-auto py-3 border-blue-200 hover:border-blue-300 hover:bg-blue-50"
                                              onClick={() => {
                                                const fullPersona = availablePersonas.find(p => p.id === persona.id);
                                                if (fullPersona) {
                                                  handleSelectPersona(fullPersona);
                                                } else {
                                                  toast({ title: "Persona Not Found", description: `Could not find details for ${persona.name}.`, variant: "destructive" });
                                                }
                                              }}
                                            >
                                              <div className="flex flex-col w-full">
                                                <div className="flex items-center justify-between">
                                                  <span className="font-semibold text-blue-800">{persona.name}</span>
                                                  <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">FREE</span>
                                                </div>
                                                {persona.description && <span className="text-xs text-gray-600 mt-1">{persona.description}</span>}
                                                {persona.capabilities && persona.capabilities.length > 0 && (
                                                  <span className="text-xs text-gray-500 mt-1">
                                                    Capabilities: {persona.capabilities.slice(0, 3).join(', ')}
                                                    {persona.capabilities.length > 3 && '...'}
                                                  </span>
                                                )}
                                                {persona.rating > 0 && (
                                                  <span className="text-xs text-gray-500 mt-1">
                                                    ⭐ {persona.rating.toFixed(1)}/5.0 ({persona.review_count} reviews)
                                                  </span>
                                                )}
                                              </div>
                                            </Button>
                                          ))}
                                        </div>
                                      )}

                                      {/* Paid Personas */}
                                      {msg.metadata.recommended_personas.filter((p: any) => p.category === 'paid').length > 0 && (
                                        <div className="space-y-2">
                                          <div className="text-sm font-medium text-purple-700 bg-purple-50 px-2 py-1 rounded">
                                            💎 Premium Personas Available ({msg.metadata.recommended_personas.filter((p: any) => p.category === 'paid').length})
                                          </div>
                                          {msg.metadata.recommended_personas.filter((p: any) => p.category === 'paid').map((persona: any) => (
                                            <Button
                                              key={persona.id}
                                              variant="outline"
                                              size="sm"
                                              className="w-full justify-start text-left h-auto py-3 border-purple-200 hover:border-purple-300 hover:bg-purple-50"
                                              onClick={() => {
                                                // For paid personas, redirect to marketplace
                                                navigate("/ai-marketplace");
                                                toast({
                                                  title: "Purchase Required",
                                                  description: `Visit the marketplace to purchase ${persona.name} for $${persona.price.toFixed(2)}.`,
                                                  variant: "default"
                                                });
                                              }}
                                            >
                                              <div className="flex flex-col w-full">
                                                <div className="flex items-center justify-between">
                                                  <span className="font-semibold text-purple-800">{persona.name}</span>
                                                  <span className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">${persona.price.toFixed(2)}</span>
                                                </div>
                                                {persona.description && <span className="text-xs text-gray-600 mt-1">{persona.description}</span>}
                                                {persona.capabilities && persona.capabilities.length > 0 && (
                                                  <span className="text-xs text-gray-500 mt-1">
                                                    Capabilities: {persona.capabilities.slice(0, 3).join(', ')}
                                                    {persona.capabilities.length > 3 && '...'}
                                                  </span>
                                                )}
                                                {persona.rating > 0 && (
                                                  <span className="text-xs text-gray-500 mt-1">
                                                    ⭐ {persona.rating.toFixed(1)}/5.0 ({persona.review_count} reviews)
                                                  </span>
                                                )}
                                              </div>
                                            </Button>
                                          ))}
                                        </div>
                                      )}
                                    </>
                                  )}

                                  {/* Fallback for non-categorized personas */}
                                  {!msg.metadata.persona_query_response && msg.metadata.recommended_personas.map((persona: Persona) => (
                                    <Button
                                      key={persona.id}
                                      variant="outline"
                                      size="sm"
                                      className="w-full justify-start text-left h-auto py-2"
                                      onClick={() => {
                                        const fullPersona = availablePersonas.find(p => p.id === persona.id);
                                        if (fullPersona) {
                                          handleSelectPersona(fullPersona);
                                        } else {
                                          toast({ title: "Persona Not Found", description: `Could not find details for ${persona.name}.`, variant: "destructive" });
                                        }
                                      }}
                                    >
                                      <div className="flex flex-col">
                                        <span className="font-semibold">{persona.name}</span>
                                        {persona.description && <span className="text-xs text-gray-500">{persona.description}</span>}
                                      </div>
                                    </Button>
                                  ))}
                                </div>
                              </>
                            ) : (
                              <>
                                {/* Display file attachments for user messages */}
                                {msg.sender === 'user' && msg.metadata?.attachments && msg.metadata.attachments.length > 0 && (
                                  <div className="mb-2 space-y-1">
                                    {msg.metadata.attachments.map((attachment: any, index: number) => (
                                      <div key={index} className="inline-flex items-center gap-2 px-2 py-1 bg-white bg-opacity-20 rounded-md text-xs">
                                        <Paperclip className="h-3 w-3 text-white" />
                                        <span className="text-white">
                                          {attachment.name} ({Math.round(attachment.size / 1024)} KB)
                                        </span>
                                        {attachment.type && (
                                          <span className="text-white opacity-75 uppercase">{attachment.type}</span>
                                        )}
                                      </div>
                                    ))}
                                  </div>
                                )}
                                {msg.sender === 'ai' ? renderMessageContent(msg.content) : msg.content}
                              </>
                            )}
                            {attachment && (
                              <div className={`mt-2 p-2 rounded-md flex items-center gap-2 ${msg.sender === 'user' ? 'bg-brand-600' : 'bg-gray-200'}`}>
                                <Paperclip className={`h-4 w-4 ${msg.sender === 'user' ? 'text-white' : 'text-gray-600'}`} />
                                <span className={`text-sm ${msg.sender === 'user' ? 'text-white' : 'text-gray-700'}`}>
                                  {attachment.name} ({Math.round(attachment.size / 1024)} KB)
                                </span>
                              </div>
                            )}
                          </div>
                          {/* Delivery Status */}
                          {msg.sender === 'user' && messageDeliveryStatus[msg.id] && (
                            <div className="flex items-center self-start pt-1"> {/* Align to top */}
                              {messageDeliveryStatus[msg.id] === 'pending' && <span className="text-xs opacity-70">Sending...</span>}
                              {messageDeliveryStatus[msg.id] === 'delivered' && <span className="text-xs opacity-70">✓</span>}
                              {messageDeliveryStatus[msg.id] === 'failed' && <span className="text-xs text-red-300">Failed</span>}
                            </div>
                          )}
                        </div>
                      </div>
                      {renderVisualization(msg)}
                    </motion.div>
                  );
                  }

                  // Determine if this specific message is currently streaming
                  const isMessageStreaming = msg.metadata?.streaming === true && msg.metadata?.status === 'streaming';

                  // For simple messages, use EnhancedMessage component
                  return (
                    <EnhancedMessage
                      key={msg.id || `message-${index}`}
                      id={msg.id}
                      conversationId={msg.conversation_id || chatMessages[0]?.conversation_id || ''}
                      content={msg.content}
                      sender={msg.sender}
                      timestamp={msg.created_at}
                      personaName={selectedPersona?.name}
                      personaAvatar={selectedPersona?.avatarUrl}
                      attachments={messageAttachments}
                      deliveryStatus={deliveryStatus}
                      isStreaming={isMessageStreaming}
                      metadata={msg.metadata}
                      toolExecutionStatus={toolExecutionStatus[msg.id] || null}
                      onCopy={() => {
                        navigator.clipboard.writeText(msg.content);
                        toast({ title: 'Copied', description: 'Message copied to clipboard' });
                      }}
                      onFeedback={(rating) => {
                        // TODO: Implement feedback system
                        console.log('Feedback:', rating, 'for message:', msg.id);
                      }}
                      onRetry={() => {
                        // TODO: Implement retry functionality
                        console.log('Retry message:', msg.id);
                      }}
                      onMessageEdited={async (editResponse) => {
                        // Handle message edit - refresh the conversation to get updated messages
                        try {
                          await hookRefreshConversation();
                          toast({
                            title: 'Message edited',
                            description: 'Your message has been updated successfully.'
                          });
                        } catch (error) {
                          console.error('Error refreshing conversation after edit:', error);
                          toast({
                            title: 'Message edited',
                            description: 'Message was edited but failed to refresh. Please refresh manually.',
                            variant: 'destructive'
                          });
                        }
                      }}
                      onMessageResubmit={async (messageId) => {
                        // Handle message resubmission - this will trigger a new AI response
                        toast({
                          title: 'Message resubmitted',
                          description: 'The edited message has been sent for reprocessing.'
                        });
                        // The resubmission is handled by the API, new messages will come via WebSocket
                        // Optionally refresh the conversation to ensure we have the latest state
                        try {
                          await hookRefreshConversation();
                        } catch (error) {
                          console.error('Error refreshing conversation after resubmit:', error);
                        }
                      }}
                      onActionClick={(actionKey) => {
                        console.log(`Action button clicked: ${actionKey}`);
                        // Handle action button clicks using the existing logic
                        const finalActionKey = actionKey.trim().toLowerCase();

                        // Define marketing action keys that should open the form
                        const marketingActionKeys = [
                          'marketing_strategy',
                          'create_marketing_strategy',
                          'campaign_strategy',
                          'develop_campaign_plan',
                          'social_media_content',
                          'generate_social_media_content',
                          'seo_optimization',
                          'post_composer',
                            // Enhanced content creation actions
                            'blog_content',
                            'email_marketing',
                            'ad_copy',
                            'press_release',
                            // Analysis and research actions
                            'competitor_analysis',
                            'audience_research',
                            'market_analysis',
                            // Data-driven actions (these should open the form, not send messages)
                            'data_driven_analysis',
                            'data_driven_campaigns'
                          ];

                          // Define analysis action keys that should be sent as specific messages
                          const analysisActionKeys = [
                            'analyze_distribution',
                            'clean_data',
                            'create_visualization',
                            'find_correlations',
                            'statistical_summary',
                            'outlier_detection',
                            'execute_code',
                            'machine_learning',
                            'data_storytelling',
                            'visualize_distributions',
                            'identify_outliers',
                            'correlation_analysis',
                            'explain_chart',
                            'different_chart',
                            'analyze_pattern',
                            'statistical_analysis',
                            'visualize_distribution',
                            'visualize_trends',
                            'visualize_comparisons'
                          ];

                          const shouldOpenForm = marketingActionKeys.includes(finalActionKey);
                          const isAnalysisAction = analysisActionKeys.includes(finalActionKey);

                          if (shouldOpenForm) {
                            console.log(`Opening marketing form for key: '${finalActionKey}'`);

                            // Set default values based on the action key
                            const contentTypeMapping: Record<string, string> = {
                              'marketing_strategy': 'marketing_strategy',
                              'campaign_strategy': 'campaign_strategy',
                              'social_media_content': 'social_media_content',
                              'seo_optimization': 'seo_optimization',
                              'blog_content': 'blog_content',
                              'email_marketing': 'email_marketing',
                              'ad_copy': 'ad_copy',
                              'press_release': 'press_release',
                              'competitor_analysis': 'competitor_analysis',
                              'audience_research': 'audience_research',
                              'market_analysis': 'market_analysis',
                              'data_driven_analysis': 'data_driven_analysis',
                              'data_driven_campaigns': 'data_driven_campaigns'
                            };

                            const contentType = contentTypeMapping[finalActionKey] || 'marketing_strategy';
                            setMarketingFormDefaultValues({
                              content_type: contentType,
                              // Set appropriate defaults based on content type
                              tone: getDefaultToneForContentType(contentType),
                              document_format: getDefaultFormatForContentType(contentType)
                            });

                            setShouldExtractFormData(true);
                            setShowMarketingForm(true);
                            setShowSpecializedComponent(false);
                          } else if (isAnalysisAction) {
                            console.log(`Sending analysis action for key: '${finalActionKey}'`);
                            // Create more descriptive messages for analysis actions
                            const analysisActionMessages: Record<string, string> = {
                              'analyze_distribution': 'Analyze the data distribution and show me the patterns',
                              'clean_data': 'Clean the data by handling missing values and inconsistencies',
                              'create_visualization': 'Create visualizations to show patterns in the data',
                              'find_correlations': 'Find correlations between variables in the data',
                              'statistical_summary': 'Provide a statistical summary of the data',
                              'outlier_detection': 'Detect outliers in the data',
                              'execute_code': 'Execute custom Python code for analysis',
                              'machine_learning': 'Apply machine learning techniques to the data',
                              'data_storytelling': 'Generate narrative insights from the data',
                              'visualize_distributions': 'Create distribution visualizations',
                              'identify_outliers': 'Identify and analyze outliers',
                              'correlation_analysis': 'Perform correlation analysis',
                              'explain_chart': 'Explain the chart and its insights',
                              'different_chart': 'Create a different type of visualization',
                              'analyze_pattern': 'Analyze the patterns shown in the data',
                              'statistical_analysis': 'Perform detailed statistical analysis',
                              'visualize_distribution': 'Visualize data distribution',
                              'visualize_trends': 'Visualize trends over time',
                              'visualize_comparisons': 'Create comparison visualizations'
                            };

                            const message = analysisActionMessages[finalActionKey] || finalActionKey.replace(/_/g, ' ');
                            const context: any = {};
                            if (selectedDataSources.length > 0) {
                              context.data_sources = selectedDataSources.map(ds => ({
                                id: ds.id,
                                name: ds.name,
                                type: ds.type
                              }));
                            }
                            sendMessage(message, context, [], selectedPersona);
                          } else {
                            console.log(`Sending message for key: '${finalActionKey}'`);

                            // Handle template gallery and setup actions
                            const templateGalleryActions = [
                              'template_gallery',
                              'business_setup',
                              'show_examples',
                              'business_setup_basic',
                              'business_setup_goals',
                              'business_setup_audience',
                              'business_setup_complete'
                            ];

                            if (templateGalleryActions.includes(finalActionKey)) {
                              // Send template gallery and setup actions as specific messages with proper context
                              const actionMessages = {
                                'template_gallery': 'Browse the template gallery and show me available marketing templates',
                                'business_setup': 'Start the business setup guide to personalize my marketing',
                                'show_examples': 'Show me marketing examples and inspiration',
                                'business_setup_basic': 'Continue with basic business setup',
                                'business_setup_goals': 'Continue to marketing goals setup',
                                'business_setup_audience': 'Continue to audience setup',
                                'business_setup_complete': 'Complete my business setup'
                              };

                              const message = actionMessages[finalActionKey as keyof typeof actionMessages] || `Execute ${finalActionKey}`;
                              const context: any = {
                                action_type: 'template_gallery_action',
                                action_key: finalActionKey,
                                trigger_tool: true  // Flag to indicate this should trigger a tool, not just conversation
                              };
                              if (selectedDataSources.length > 0) {
                                context.data_sources = selectedDataSources.map(ds => ({
                                  id: ds.id,
                                  name: ds.name,
                                  type: ds.type
                                }));
                              }
                              sendMessage(message, context, [], selectedPersona);
                            } else {
                              // Default behavior: Send the action key as a message
                              const context: any = {};
                              if (selectedDataSources.length > 0) {
                                context.data_sources = selectedDataSources.map(ds => ({
                                  id: ds.id,
                                  name: ds.name,
                                  type: ds.type
                                }));
                              }
                              sendMessage(finalActionKey.replace(/_/g, ' '), context, [], selectedPersona);
                            }
                          }
                        }}
                      />
                    );
                })}
                {/* Removed debug streaming messages to reduce console verbosity */}

                {/* Only show typing indicator if no messages are currently streaming */}
                {!messages.some(msg => msg.metadata?.streaming === true && msg.metadata?.status === 'streaming') && (
                  <TypingIndicator
                    isTyping={isTyping}
                    personaName={selectedPersona?.name}
                    avatarUrl={selectedPersona?.avatarUrl}
                  />
                )}
                <div ref={messagesEndRef} />
              </div>

              {/* Concierge Panel - Desktop */}
              <AnimatePresence>
                {isConciergeActive && showConciergePanel && conciergeState && (
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{
                      opacity: 1,
                      x: 0,
                      transition: {
                        type: "spring",
                        stiffness: 300,
                        damping: 30
                      }
                    }}
                    exit={{
                      opacity: 0,
                      x: 20,
                      transition: {
                        duration: 0.2
                      }
                    }}
                    className="ml-4 w-80 hidden md:block"
                  >
                    <div className="relative">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setShowConciergePanel(false)}
                        className="absolute top-2 right-2 z-10 h-8 w-8 text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                      <WorkflowVisualization steps={workflowSteps} />
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Concierge Panel - Mobile (Fixed at bottom) */}
              <AnimatePresence>
                {isConciergeActive && showConciergePanel && conciergeState && (
                  <motion.div
                    initial={{ opacity: 0, y: 100 }}
                    animate={{
                      opacity: 1,
                      y: 0,
                      transition: {
                        type: "spring",
                        stiffness: 300,
                        damping: 30
                      }
                    }}
                    exit={{
                      opacity: 0,
                      y: 100,
                      transition: {
                        duration: 0.2
                      }
                    }}
                    className="fixed bottom-32 left-0 right-0 z-50 p-4 md:hidden"
                    style={{ bottom: '8rem' }}
                  >
                    <div className="relative max-h-[60vh] overflow-auto">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setShowConciergePanel(false)}
                        className="absolute top-2 right-2 z-10 h-8 w-8 text-gray-500"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                      <WorkflowVisualization steps={workflowSteps} />
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </Card>

          {/* Floating Chat Input Area */}
          <div className="fixed bottom-0 left-0 right-0 bg-white border-t shadow-lg p-4 z-50">
            {/* Display attached files */}
            {attachedFiles.length > 0 && (
              <div className="mb-2 space-y-1">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs text-gray-600">Attached Files</span>
                  <div className="flex items-center gap-2">
                    <label className="flex items-center gap-1 text-xs text-gray-600">
                      <input
                        type="checkbox"
                        checked={persistentAttachments}
                        onChange={(e) => setPersistentAttachments(e.target.checked)}
                        className="w-3 h-3"
                      />
                      Keep attached
                    </label>
                  </div>
                </div>
                {attachedFiles.map((file, index) => (
                  <div key={index} className="p-1.5 bg-blue-50 border border-blue-200 rounded-md flex items-center justify-between text-xs">
                    <div className="flex items-center gap-2">
                      <Paperclip className="h-3 w-3 text-blue-600" />
                      <span className="text-blue-700 truncate">
                        {file.name} ({Math.round(file.size / 1024)} KB)
                      </span>
                      {!persistentAttachments && (
                        <span className="text-gray-500 text-xs">(auto-detach)</span>
                      )}
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-5 w-5 text-blue-600 hover:bg-blue-100"
                      onClick={() => {
                        setAttachedFiles(prev => prev.filter((_, i) => i !== index));
                        if (fileInputRef.current) fileInputRef.current.value = "";
                      }}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            )}

            {/* Display selected data sources */}
            {selectedDataSources.length > 0 && (
              <div className="mb-2 space-y-1">
                {selectedDataSources.map((dataSource) => (
                  <div key={dataSource.id} className="p-1.5 bg-green-50 border border-green-200 rounded-md flex items-center justify-between text-xs">
                    <div className="flex items-center gap-2">
                      <Database className="h-3 w-3 text-green-600" />
                      <span className="text-green-700 truncate">
                        {dataSource.name}
                      </span>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-5 w-5 text-green-600 hover:bg-green-100"
                      onClick={() => {
                        setSelectedDataSources(prev => prev.filter(ds => ds.id !== dataSource.id));
                      }}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            )}

            <div className="flex gap-2">
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileSelected}
                className="hidden"
                accept="*" // Allow all file types
                multiple // Allow multiple file selection
              />
              <Popover open={attachmentPopoverOpen} onOpenChange={setAttachmentPopoverOpen}>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="icon" disabled={isLoading || connectionStatus === 'connecting' || !hasAvailablePersonas} title="Attach data">
                    <Paperclip className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-48 p-2" align="start">
                  <div className="space-y-1">
                    <Button
                      variant="ghost"
                      className="w-full justify-start"
                      onClick={handleUploadFileClick}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload File
                    </Button>
                    <Button
                      variant="ghost"
                      className="w-full justify-start"
                      onClick={handleAttachDataSourceClick}
                    >
                      <Database className="h-4 w-4 mr-2" />
                      Attach Data Source
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>
              <Button variant="outline" size="icon" className={isRecording ? 'bg-red-100 text-red-500' : ''} onClick={toggleRecording} disabled={isLoading || connectionStatus === 'connecting' || !hasAvailablePersonas} title={isRecording ? "Stop recording" : "Start recording"}>{isRecording ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}</Button>
              <Input
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                placeholder={
                  attachedFiles.length > 0 || selectedDataSources.length > 0
                    ? `Type instructions for your ${attachedFiles.length + selectedDataSources.length} attachment(s)...`
                    : !hasAvailablePersonas
                      ? 'Purchase AI personas...'
                      : isLoading
                        ? 'Processing...'
                        : 'Ask about your data...'
                }
                className="flex-1"
                disabled={isLoading || connectionStatus === 'connecting' || !hasAvailablePersonas}
                onKeyDown={(e) => { if (e.key === 'Enter' && !isLoading && hasAvailablePersonas && (inputMessage.trim() || attachedFiles.length > 0 || selectedDataSources.length > 0)) { handleSendMessage(); } }}
              />
              <Button onClick={handleSendMessage} disabled={isLoading || connectionStatus === 'connecting' || !hasAvailablePersonas || (!inputMessage.trim() && attachedFiles.length === 0 && selectedDataSources.length === 0)}>
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
              </Button>
            </div>

            {/* Subtle attachment indicator at bottom */}
            {(attachedFiles.length > 0 || selectedDataSources.length > 0) && (
              <div className="mt-2 flex items-center justify-center">
                <div className="text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded-full flex items-center gap-1">
                  <FileText className="h-3 w-3" />
                  <span>
                    {attachedFiles.length + selectedDataSources.length} attachment{attachedFiles.length + selectedDataSources.length !== 1 ? 's' : ''} ready
                  </span>
                </div>
              </div>
            )}

            {/* Connection Status Footer */}
            {connectionStatus !== 'connected' && <div className={`text-xs p-2 text-center mt-2 rounded ${connectionStatus === 'connecting' ? 'bg-yellow-50 text-yellow-700' : 'bg-red-50 text-red-700'}`}>{connectionStatus === 'connecting' ? 'Connecting...' : 'Disconnected.'}</div>}
          </div>
        </div>
      </motion.div>
    </DashboardLayout>
  );
};

export default DataChat;
