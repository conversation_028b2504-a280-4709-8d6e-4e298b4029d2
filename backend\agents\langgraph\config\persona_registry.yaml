# Unified Persona Registry Configuration
# This file defines the complete registry of all available personas in the system
# All personas are loaded dynamically from their individual configuration files

# Registry metadata
registry_version: "1.0.0"
last_updated: "2024-07-24"
total_personas: 4
migration_status: "completed"

# Global persona settings
global_settings:
  # Default configuration applied to all personas
  default_timeout_seconds: 300
  default_cache_duration_minutes: 60
  enable_cross_agent_intelligence: true
  enable_business_profile_integration: true
  enable_performance_monitoring: true
  
  # LLM-driven intent interpretation (no hardcoded intents)
  intent_interpretation:
    enable_dynamic_intent_detection: true
    use_llm_for_intent_analysis: true
    intent_confidence_threshold: 0.6
    fallback_to_capability_matching: true
  
  # Shared tool configurations
  shared_tools:
    - "conversation_management"
    - "business_context_manager"
    - "cross_agent_intelligence"
    - "performance_tracker"

# Persona registry entries
personas:
  # Analysis Persona
  analysis:
    persona_id: "analysis"
    config_file: "personas/analysis.yaml"
    status: "active"
    priority: 1
    category: "data_analysis"

    # Quick reference metadata
    metadata:
      name: "Data Analysis AI"
      description: "Expert data analyst specializing in insights, visualization, and statistical analysis"
      agent_type: "analysis"
      
    # Capability summary for routing
    capability_summary:
      primary_capabilities:
        - "data_analysis"
        - "visualization_creation"
        - "statistical_analysis"
        - "report_generation"
      
      specializations:
        - "pandasai_integration"
        - "machine_learning"
        - "advanced_visualization"
        - "data_storytelling"
      
      best_for:
        - "analyzing datasets"
        - "creating charts and graphs"
        - "statistical insights"
        - "data exploration"
        - "trend analysis"
    
    # Integration settings
    integrations:
      mcp_tools: true
      business_profile: true
      cross_agent_intelligence: true

  # Marketing Persona
  marketing:
    persona_id: "marketing"
    config_file: "personas/marketing.yaml"
    status: "active"
    priority: 2
    category: "marketing"

    # Quick reference metadata
    metadata:
      name: "Marketing AI"
      description: "Marketing expert specializing in content creation, strategy, and campaign planning"
      agent_type: "marketing"
      
    # Capability summary for routing
    capability_summary:
      primary_capabilities:
        - "content_generation"
        - "campaign_creation"
        - "social_media_management"
        - "brand_strategy"
      
      specializations:
        - "email_marketing"
        - "competitor_analysis"
        - "audience_research"
        - "performance_tracking"
      
      best_for:
        - "creating marketing content"
        - "developing campaigns"
        - "brand messaging"
        - "social media strategy"
        - "market analysis"
    
    # Integration settings
    integrations:
      mcp_tools: true
      business_profile: true
      cross_agent_intelligence: true

  # Concierge Persona
  concierge:
    persona_id: "concierge"
    config_file: "personas/concierge.yaml"
    status: "active"
    priority: 0  # Highest priority as entry point
    category: "guidance"

    # Quick reference metadata
    metadata:
      name: "Datagenius Concierge"
      description: "Intelligent guide for persona recommendations and platform navigation"
      agent_type: "concierge"
      
    # Capability summary for routing
    capability_summary:
      primary_capabilities:
        - "persona_recommendation"
        - "intent_analysis"
        - "conversation_management"
        - "user_guidance"
      
      specializations:
        - "intelligent_routing"
        - "workflow_coordination"
        - "platform_navigation"
        - "context_management"
      
      best_for:
        - "getting started"
        - "finding the right persona"
        - "general questions"
        - "platform guidance"
        - "workflow coordination"
    
    # Integration settings
    integrations:
      mcp_tools: true
      business_profile: true
      cross_agent_intelligence: true
      persona_routing: true

  # Classification Persona
  classification:
    persona_id: "classification"
    config_file: "personas/classification.yaml"
    status: "active"
    priority: 3
    category: "organization"

    # Quick reference metadata
    metadata:
      name: "Classification Specialist"
      description: "Expert in categorizing, organizing, and classifying content and data"
      agent_type: "classification"
      
    # Capability summary for routing
    capability_summary:
      primary_capabilities:
        - "text_classification"
        - "content_categorization"
        - "data_organization"
        - "pattern_recognition"
      
      specializations:
        - "sentiment_analysis"
        - "topic_modeling"
        - "entity_extraction"
        - "taxonomy_creation"
      
      best_for:
        - "organizing content"
        - "classifying documents"
        - "categorizing data"
        - "pattern detection"
        - "content filtering"
    
    # Integration settings
    integrations:
      mcp_tools: true
      business_profile: true
      cross_agent_intelligence: true

# Persona routing configuration
routing:
  # Default routing strategy
  default_strategy: "llm_driven_routing"
  
  # Routing rules (LLM-interpreted, not hardcoded)
  routing_rules:
    enable_intelligent_routing: true
    use_capability_matching: true
    enable_context_awareness: true
    confidence_threshold: 0.7
    
    # Fallback chain
    fallback_chain:
      - "concierge"  # Always fall back to concierge for guidance
      - "analysis"   # Secondary fallback for data-related queries
  
  # Persona selection criteria
  selection_criteria:
    factors:
      - "user_message_content"
      - "conversation_context"
      - "business_profile"
      - "previous_interactions"
      - "capability_match_score"
    
    weights:
      capability_match: 0.4
      context_relevance: 0.3
      user_preference: 0.2
      conversation_history: 0.1

# Cross-agent intelligence configuration
cross_agent_intelligence:
  enable_knowledge_sharing: true
  enable_context_propagation: true
  enable_collaborative_processing: true
  
  # Shared knowledge areas
  shared_knowledge:
    business_profile: true
    user_preferences: true
    conversation_history: true
    analysis_insights: true
    marketing_insights: true
    classification_results: true
  
  # Coordination protocols
  coordination:
    enable_agent_handoffs: true
    enable_collaborative_responses: true
    enable_insight_sharing: true
    coordination_timeout_seconds: 30

# Performance and monitoring
performance:
  # Caching strategy
  caching:
    enable_persona_config_caching: true
    cache_duration_minutes: 60
    enable_capability_caching: true
    
  # Load balancing
  load_balancing:
    enable_load_balancing: false  # Single instance deployment
    max_concurrent_personas: 5
    
  # Monitoring
  monitoring:
    enable_performance_tracking: true
    enable_usage_analytics: true
    track_routing_accuracy: true
    track_user_satisfaction: true

# System configuration
system:
  # Unified persona system
  mode: "unified_only"
  legacy_support: false
  extensible_personas: true

  # System settings
  settings:
    maintain_api_compatibility: true
    preserve_conversation_state: true
    enable_rollback: false

  # System status
  status:
    migration_completed: true
    system_ready: true
    all_personas_active: true

# Validation and quality assurance
validation:
  # Configuration validation
  config_validation:
    validate_on_load: true
    strict_validation: true
    enable_schema_validation: true
    
  # Runtime validation
  runtime_validation:
    validate_persona_responses: true
    check_capability_alignment: true
    monitor_performance_metrics: true
  
  # Quality metrics
  quality_metrics:
    min_response_quality_score: 0.8
    max_response_time_seconds: 30
    min_capability_match_score: 0.7

# Extensibility
extensibility:
  # Plugin support
  enable_plugins: true
  plugin_directories:
    - "plugins/personas"
    - "custom_plugins/personas"
  
  # Custom persona support
  enable_custom_personas: true
  custom_persona_registry: "custom_personas.yaml"
  
  # API extensions
  enable_api_extensions: true
  api_extension_endpoints:
    - "/api/personas/custom"
    - "/api/personas/registry"
