"""Enhanced marketplace architecture

Revision ID: 3784bbc73d80
Revises: fix_message_id_lengths
Create Date: 2025-07-23 12:50:26.877877

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import logging

logger = logging.getLogger(__name__)

# revision identifiers, used by Alembic.
revision = '3784bbc73d80'
down_revision = 'fix_message_id_lengths'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create enhanced marketplace tables."""

    # Enhanced persona configuration table
    op.create_table(
        'persona_configurations',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('persona_id', sa.String(255), nullable=False),
        sa.Column('configuration', postgresql.JSONB, nullable=False),
        sa.Column('industry_specialization', sa.String(100), nullable=True),
        sa.Column('methodology_framework', sa.String(100), nullable=True),
        sa.Column('enable_cross_agent_intelligence', sa.<PERSON>, default=True),
        sa.Column('specialized_tools', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('compliance_requirements', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('workflow_patterns', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('performance_optimization', postgresql.JSONB, default=sa.text("'{}'")),
        sa.Column('created_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.Column('updated_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
    )
    op.create_index('idx_persona_configurations_persona_id', 'persona_configurations', ['persona_id'])
    op.create_index('idx_persona_configurations_industry', 'persona_configurations', ['industry_specialization'])

    # Plugin registry table
    op.create_table(
        'agent_plugins',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('plugin_id', sa.String(255), unique=True, nullable=False),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('version', sa.String(50), nullable=False),
        sa.Column('author', sa.String(255), nullable=True),
        sa.Column('description', sa.Text, nullable=True),
        sa.Column('plugin_package', postgresql.JSONB, nullable=False),
        sa.Column('security_validation', postgresql.JSONB, nullable=True),
        sa.Column('capability_validation', postgresql.JSONB, nullable=True),
        sa.Column('marketplace_listing_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('status', sa.String(50), default='pending'),
        sa.Column('supported_industries', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('required_tools', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('compliance_certifications', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('installation_config', postgresql.JSONB, default=sa.text("'{}'")),
        sa.Column('created_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.Column('updated_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
    )
    op.create_index('idx_agent_plugins_plugin_id', 'agent_plugins', ['plugin_id'])
    op.create_index('idx_agent_plugins_status', 'agent_plugins', ['status'])
    op.create_index('idx_agent_plugins_author', 'agent_plugins', ['author'])

    # Hierarchical message structure with LTREE support
    op.execute('CREATE EXTENSION IF NOT EXISTS ltree')

    op.create_table(
        'message_threads',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('conversation_id', sa.String(64), nullable=False),  # Match conversations.id type
        sa.Column('parent_message_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('thread_path', sa.Text, nullable=True),  # LTREE path
        sa.Column('message_content', postgresql.JSONB, nullable=False),
        sa.Column('message_type', sa.String(50), nullable=False),
        sa.Column('message_status', sa.String(50), default='active'),
        sa.Column('created_by', sa.Integer, nullable=False),  # Match users.id type
        sa.Column('created_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.Column('updated_at', sa.TIMESTAMP, nullable=True),
        sa.Column('metadata', postgresql.JSONB, default=sa.text("'{}'")),
        sa.ForeignKeyConstraint(['conversation_id'], ['conversations.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['parent_message_id'], ['message_threads.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ondelete='CASCADE'),
    )
    op.create_index('idx_message_threads_conversation_id', 'message_threads', ['conversation_id'])
    op.create_index('idx_message_threads_parent_id', 'message_threads', ['parent_message_id'])
    op.create_index('idx_message_threads_type', 'message_threads', ['message_type'])
    op.create_index('idx_message_threads_status', 'message_threads', ['message_status'])
    op.create_index('idx_message_threads_created_by', 'message_threads', ['created_by'])
    # Create GIST index for LTREE path queries (commented out for now, can be added later when LTREE is fully implemented)
    # op.create_index('idx_message_threads_path', 'message_threads', ['thread_path'], postgresql_using='gist')

    # Message edit history
    op.create_table(
        'message_edit_history',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('edit_id', sa.String(255), unique=True, nullable=False),
        sa.Column('original_message_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('edited_message_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('edit_type', sa.String(50), nullable=False),
        sa.Column('edited_by', sa.Integer, nullable=False),  # Match users.id type
        sa.Column('edited_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.Column('edit_reason', sa.Text, nullable=True),
        sa.Column('diff_data', postgresql.JSONB, default=sa.text("'{}'")),
        sa.ForeignKeyConstraint(['original_message_id'], ['message_threads.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['edited_message_id'], ['message_threads.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['edited_by'], ['users.id'], ondelete='CASCADE'),
    )
    op.create_index('idx_message_edit_history_original_id', 'message_edit_history', ['original_message_id'])
    op.create_index('idx_message_edit_history_edited_id', 'message_edit_history', ['edited_message_id'])
    op.create_index('idx_message_edit_history_edited_by', 'message_edit_history', ['edited_by'])
    op.create_index('idx_message_edit_history_edit_type', 'message_edit_history', ['edit_type'])

    # Conversation resubmission tracking
    op.create_table(
        'conversation_resubmissions',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('resubmission_id', sa.String(255), unique=True, nullable=False),
        sa.Column('original_conversation_id', sa.String(64), nullable=False),  # Match conversations.id type
        sa.Column('new_conversation_id', sa.String(64), nullable=False),  # Match conversations.id type
        sa.Column('branch_point_message_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('resubmitted_by', sa.Integer, nullable=False),  # Match users.id type
        sa.Column('resubmission_time', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.Column('resubmission_reason', sa.Text, nullable=True),
        sa.Column('original_workflow_context', postgresql.JSONB, default=sa.text("'{}'")),
        sa.ForeignKeyConstraint(['original_conversation_id'], ['conversations.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['new_conversation_id'], ['conversations.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['branch_point_message_id'], ['message_threads.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['resubmitted_by'], ['users.id'], ondelete='CASCADE'),
    )
    op.create_index('idx_conversation_resubmissions_original_id', 'conversation_resubmissions', ['original_conversation_id'])
    op.create_index('idx_conversation_resubmissions_new_id', 'conversation_resubmissions', ['new_conversation_id'])
    op.create_index('idx_conversation_resubmissions_resubmitted_by', 'conversation_resubmissions', ['resubmitted_by'])

    # Industry templates
    op.create_table(
        'industry_templates',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('industry', sa.String(100), unique=True, nullable=False),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('description', sa.Text, nullable=True),
        sa.Column('specialized_nodes', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('workflow_patterns', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('compliance_requirements', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('data_requirements', postgresql.JSONB, default=sa.text("'{}'")),
        sa.Column('security_requirements', postgresql.JSONB, default=sa.text("'{}'")),
        sa.Column('performance_requirements', postgresql.JSONB, default=sa.text("'{}'")),
        sa.Column('created_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.Column('updated_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
    )
    op.create_index('idx_industry_templates_industry', 'industry_templates', ['industry'])

    # Workflow adaptation tracking
    op.create_table(
        'workflow_adaptations',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('adaptation_id', sa.String(255), unique=True, nullable=False),
        sa.Column('workflow_id', sa.String(255), nullable=False),
        sa.Column('persona_id', sa.String(255), nullable=False),
        sa.Column('user_id', sa.Integer, nullable=False),  # Match users.id type
        sa.Column('adaptation_type', sa.String(100), nullable=False),
        sa.Column('adaptation_triggers', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('performance_data', postgresql.JSONB, default=sa.text("'{}'")),
        sa.Column('adaptation_config', postgresql.JSONB, default=sa.text("'{}'")),
        sa.Column('success_metrics', postgresql.JSONB, default=sa.text("'{}'")),
        sa.Column('created_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    )
    op.create_index('idx_workflow_adaptations_workflow_id', 'workflow_adaptations', ['workflow_id'])
    op.create_index('idx_workflow_adaptations_persona_id', 'workflow_adaptations', ['persona_id'])
    op.create_index('idx_workflow_adaptations_user_id', 'workflow_adaptations', ['user_id'])
    op.create_index('idx_workflow_adaptations_type', 'workflow_adaptations', ['adaptation_type'])

    # Workflow experiments (A/B testing)
    op.create_table(
        'workflow_experiments',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('experiment_id', sa.String(255), unique=True, nullable=False),
        sa.Column('experiment_name', sa.String(255), nullable=False),
        sa.Column('description', sa.Text, nullable=True),
        sa.Column('control_workflow_config', postgresql.JSONB, nullable=False),
        sa.Column('variation_workflows_config', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('traffic_split', postgresql.JSONB, default=sa.text("'{}'")),
        sa.Column('success_metrics', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('experiment_status', sa.String(50), default='draft'),
        sa.Column('start_time', sa.TIMESTAMP, nullable=True),
        sa.Column('end_time', sa.TIMESTAMP, nullable=True),
        sa.Column('created_by', sa.Integer, nullable=False),  # Match users.id type
        sa.Column('created_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.Column('updated_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ondelete='CASCADE'),
    )
    op.create_index('idx_workflow_experiments_experiment_id', 'workflow_experiments', ['experiment_id'])
    op.create_index('idx_workflow_experiments_status', 'workflow_experiments', ['experiment_status'])
    op.create_index('idx_workflow_experiments_created_by', 'workflow_experiments', ['created_by'])

    # Experiment participation tracking
    op.create_table(
        'experiment_participations',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('experiment_id', sa.String(255), nullable=False),
        sa.Column('user_id', sa.Integer, nullable=False),  # Match users.id type
        sa.Column('variation_assigned', sa.String(100), nullable=False),
        sa.Column('assignment_time', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.Column('metrics_data', postgresql.JSONB, default=sa.text("'{}'")),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    )
    op.create_index('idx_experiment_participations_experiment_id', 'experiment_participations', ['experiment_id'])
    op.create_index('idx_experiment_participations_user_id', 'experiment_participations', ['user_id'])
    op.create_index('idx_experiment_participations_variation', 'experiment_participations', ['variation_assigned'])
    op.create_unique_constraint('uq_experiment_user', 'experiment_participations', ['experiment_id', 'user_id'])

    # Performance metrics tracking
    op.create_table(
        'workflow_performance_metrics',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('workflow_id', sa.String(255), nullable=False),
        sa.Column('persona_id', sa.String(255), nullable=False),
        sa.Column('user_id', sa.Integer, nullable=False),  # Match users.id type
        sa.Column('conversation_id', sa.String(64), nullable=True),  # Match conversations.id type
        sa.Column('execution_time', sa.Float, nullable=True),
        sa.Column('memory_usage', sa.Float, nullable=True),
        sa.Column('success_rate', sa.Float, nullable=True),
        sa.Column('user_satisfaction', sa.Float, nullable=True),
        sa.Column('business_relevance', sa.Float, nullable=True),
        sa.Column('custom_metrics', postgresql.JSONB, default=sa.text("'{}'")),
        sa.Column('recorded_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['conversation_id'], ['conversations.id'], ondelete='CASCADE'),
    )
    op.create_index('idx_workflow_performance_workflow_id', 'workflow_performance_metrics', ['workflow_id'])
    op.create_index('idx_workflow_performance_persona_id', 'workflow_performance_metrics', ['persona_id'])
    op.create_index('idx_workflow_performance_user_id', 'workflow_performance_metrics', ['user_id'])
    op.create_index('idx_workflow_performance_recorded_at', 'workflow_performance_metrics', ['recorded_at'])

    logger.info("Enhanced marketplace tables created successfully")


def downgrade() -> None:
    """Drop enhanced marketplace tables."""

    # Drop tables in reverse order to handle foreign key constraints
    op.drop_table('workflow_performance_metrics')
    op.drop_table('experiment_participations')
    op.drop_table('workflow_experiments')
    op.drop_table('workflow_adaptations')
    op.drop_table('industry_templates')
    op.drop_table('conversation_resubmissions')
    op.drop_table('message_edit_history')
    op.drop_table('message_threads')
    op.drop_table('agent_plugins')
    op.drop_table('persona_configurations')

    # Drop LTREE extension if no other tables use it
    op.execute('DROP EXTENSION IF EXISTS ltree')

    logger.info("Enhanced marketplace tables dropped successfully")
