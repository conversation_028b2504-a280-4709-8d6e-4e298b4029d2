#!/usr/bin/env python3
"""
Simple test to verify agent registration without dependencies.
"""

import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def test_persona_discovery():
    """Test persona discovery directly."""
    try:
        print("Testing persona discovery...")
        
        # Test direct file discovery
        current_dir = Path(__file__).parent
        personas_dir = current_dir / "agents" / "langgraph" / "config" / "personas"
        
        if not personas_dir.exists():
            print(f"❌ Personas directory does not exist: {personas_dir}")
            return False
        
        # Scan for YAML and JSON files
        yaml_files = list(personas_dir.glob("*.yaml"))
        json_files = list(personas_dir.glob("*.json"))
        
        total_files = len(yaml_files) + len(json_files)
        
        print(f"✅ Found {len(yaml_files)} YAML files and {len(json_files)} JSON files")
        print(f"✅ Total persona configuration files: {total_files}")
        
        # List the files
        for file_path in yaml_files + json_files:
            print(f"  - {file_path.name}")
        
        return total_files > 0
        
    except Exception as e:
        print(f"❌ Persona discovery test failed: {e}")
        return False

def test_yaml_loading():
    """Test YAML loading directly."""
    try:
        print("\nTesting YAML loading...")
        
        import yaml
        from pathlib import Path
        
        current_dir = Path(__file__).parent
        personas_dir = current_dir / "agents" / "langgraph" / "config" / "personas"
        
        if not personas_dir.exists():
            print(f"❌ Personas directory does not exist: {personas_dir}")
            return False
        
        loaded_configs = 0
        
        # Try to load each YAML file
        for yaml_file in personas_dir.glob("*.yaml"):
            try:
                with open(yaml_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                
                if config:
                    persona_id = config.get("persona_id") or config.get("id")
                    name = config.get("name", "Unknown")
                    print(f"  ✅ Loaded {yaml_file.name}: {persona_id} - {name}")
                    loaded_configs += 1
                else:
                    print(f"  ⚠️ Empty config in {yaml_file.name}")
                    
            except Exception as e:
                print(f"  ❌ Failed to load {yaml_file.name}: {e}")
        
        print(f"✅ Successfully loaded {loaded_configs} persona configurations")
        return loaded_configs > 0
        
    except Exception as e:
        print(f"❌ YAML loading test failed: {e}")
        return False

def test_agent_factory_import():
    """Test if agent factory can be imported."""
    try:
        print("\nTesting agent factory import...")
        
        # Try to import just the class without initializing
        from agents.langgraph.core.agent_factory import AgentNodeFactory
        print("✅ Successfully imported AgentNodeFactory")
        
        # Try to create instance
        factory = AgentNodeFactory()
        print("✅ Successfully created AgentNodeFactory instance")
        
        # Check initial state
        available_agents = factory.get_available_agents()
        print(f"✅ Initial available agents: {available_agents}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent factory import test failed: {e}")
        return False

def test_manual_agent_registration():
    """Test manual agent registration."""
    try:
        print("\nTesting manual agent registration...")
        
        from agents.langgraph.core.agent_factory import AgentNodeFactory
        import yaml
        from pathlib import Path
        
        # Create factory
        factory = AgentNodeFactory()
        
        # Load persona configurations manually
        current_dir = Path(__file__).parent
        personas_dir = current_dir / "agents" / "langgraph" / "config" / "personas"
        
        registered_count = 0
        
        for yaml_file in personas_dir.glob("*.yaml"):
            try:
                with open(yaml_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                
                if config:
                    persona_id = config.get("persona_id") or config.get("id")
                    if persona_id:
                        # Register the agent configuration
                        factory.register_agent_config(persona_id, config)
                        registered_count += 1
                        print(f"  ✅ Registered {persona_id}")
                    else:
                        print(f"  ⚠️ No persona_id in {yaml_file.name}")
                        
            except Exception as e:
                print(f"  ❌ Failed to register from {yaml_file.name}: {e}")
        
        # Check final state
        available_agents = factory.get_available_agents()
        print(f"✅ Registered {registered_count} agents")
        print(f"✅ Available agents after registration: {available_agents}")
        
        return registered_count > 0
        
    except Exception as e:
        print(f"❌ Manual agent registration test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting agent registration tests...")
    
    results = {}
    
    # Test 1: Persona discovery
    results['persona_discovery'] = test_persona_discovery()
    
    # Test 2: YAML loading
    results['yaml_loading'] = test_yaml_loading()
    
    # Test 3: Agent factory import
    results['agent_factory_import'] = test_agent_factory_import()
    
    # Test 4: Manual agent registration
    results['manual_registration'] = test_manual_agent_registration()
    
    # Summary
    print("\n" + "="*50)
    print("TEST RESULTS SUMMARY")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Agent registration is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the logs above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
