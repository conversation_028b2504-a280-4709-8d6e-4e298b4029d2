"""Fix message ID field lengths

Revision ID: fix_message_id_lengths
Revises: add_hierarchical_message_ids
Create Date: 2025-01-23 15:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'fix_message_id_lengths'
down_revision = 'add_hierarchical_message_ids'
branch_labels = None
depends_on = None


def upgrade():
    """Increase VARCHAR lengths for message ID fields to accommodate hierarchical IDs."""
    
    # Increase the length of ID fields in messages table from VARCHAR(36) to VARCHAR(64)
    # This is needed because hierarchical message IDs can be longer than 36 characters
    # Format: conversation_prefix-parent_prefix-uuid (8+1+8+1+36 = 54 characters max)
    
    with op.batch_alter_table('messages', schema=None) as batch_op:
        # Increase primary key id field
        batch_op.alter_column('id',
                            existing_type=sa.String(36),
                            type_=sa.String(64),
                            existing_nullable=False)
        
        # Increase thread_id field
        batch_op.alter_column('thread_id',
                            existing_type=sa.String(36),
                            type_=sa.String(64),
                            existing_nullable=True)
        
        # Increase parent_message_id field
        batch_op.alter_column('parent_message_id',
                            existing_type=sa.String(36),
                            type_=sa.String(64),
                            existing_nullable=True)
        
        # Increase original_message_id field
        batch_op.alter_column('original_message_id',
                            existing_type=sa.String(36),
                            type_=sa.String(64),
                            existing_nullable=True)

    # Also update conversations table id field to be consistent
    with op.batch_alter_table('conversations', schema=None) as batch_op:
        batch_op.alter_column('id',
                            existing_type=sa.String(36),
                            type_=sa.String(64),
                            existing_nullable=False)

    # Update the foreign key reference in messages table
    with op.batch_alter_table('messages', schema=None) as batch_op:
        batch_op.alter_column('conversation_id',
                            existing_type=sa.String(36),
                            type_=sa.String(64),
                            existing_nullable=False)


def downgrade():
    """Revert VARCHAR lengths for message ID fields back to 36 characters."""
    
    # Note: This downgrade assumes no hierarchical IDs longer than 36 characters exist
    # If they do, this migration will fail and manual cleanup will be required
    
    with op.batch_alter_table('messages', schema=None) as batch_op:
        # Revert conversation_id field
        batch_op.alter_column('conversation_id',
                            existing_type=sa.String(64),
                            type_=sa.String(36),
                            existing_nullable=False)

    # Revert conversations table id field
    with op.batch_alter_table('conversations', schema=None) as batch_op:
        batch_op.alter_column('id',
                            existing_type=sa.String(64),
                            type_=sa.String(36),
                            existing_nullable=False)

    with op.batch_alter_table('messages', schema=None) as batch_op:
        # Revert original_message_id field
        batch_op.alter_column('original_message_id',
                            existing_type=sa.String(64),
                            type_=sa.String(36),
                            existing_nullable=True)
        
        # Revert parent_message_id field
        batch_op.alter_column('parent_message_id',
                            existing_type=sa.String(64),
                            type_=sa.String(36),
                            existing_nullable=True)
        
        # Revert thread_id field
        batch_op.alter_column('thread_id',
                            existing_type=sa.String(64),
                            type_=sa.String(36),
                            existing_nullable=True)
        
        # Revert primary key id field
        batch_op.alter_column('id',
                            existing_type=sa.String(64),
                            type_=sa.String(36),
                            existing_nullable=False)
