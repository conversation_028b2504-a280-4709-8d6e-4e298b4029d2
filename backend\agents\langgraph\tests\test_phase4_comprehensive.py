"""
Comprehensive Test Suite for Phase 4: Platform Evolution

This test suite validates the complete Phase 4 implementation including
integration between all components and end-to-end functionality.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch, MagicMock

from ..phase4_integration import Phase4PlatformEvolution
from ..marketplace.capability_marketplace import CapabilityMarketplace
from ..marketplace.capability_registry import CapabilityRegistry
from ..marketplace.trading_engine import TradingEngine
from ..marketplace.certification_system import CertificationSystem
from ..ai.workflow_composer import AIWorkflowComposer
from ..ai.pattern_recognition import WorkflowPatternRecognition
from ..core.workflow_manager import WorkflowManager


class TestPhase4Integration:
    """Test suite for Phase 4 platform integration."""
    
    @pytest.fixture
    async def phase4_platform(self):
        """Create a test Phase 4 platform instance."""
        platform = Phase4PlatformEvolution()
        
        # Mock the initialization of components to avoid external dependencies
        with patch.object(platform.marketplace.registry, 'initialize', return_value=None), \
             patch.object(platform.trading_engine, 'initialize', return_value=None), \
             patch.object(platform.certification_system, 'initialize', return_value=None), \
             patch.object(platform.marketplace, 'initialize', return_value=None), \
             patch.object(platform.pattern_recognition, 'initialize', return_value=None), \
             patch.object(platform.workflow_composer, 'initialize', return_value=None):
            
            await platform.initialize()
        
        return platform
    
    @pytest.mark.asyncio
    async def test_phase4_platform_initialization(self, phase4_platform):
        """Test Phase 4 platform initialization."""
        assert phase4_platform.is_initialized
        assert phase4_platform.initialization_time is not None
        assert phase4_platform.marketplace is not None
        assert phase4_platform.workflow_composer is not None
        assert phase4_platform.pattern_recognition is not None
        assert phase4_platform.trading_engine is not None
        assert phase4_platform.certification_system is not None
    
    @pytest.mark.asyncio
    async def test_component_status(self, phase4_platform):
        """Test component status reporting."""
        status = phase4_platform.get_component_status()
        
        assert isinstance(status, dict)
        assert "marketplace" in status
        assert "trading_engine" in status
        assert "certification_system" in status
        assert "workflow_composer" in status
        assert "pattern_recognition" in status
        assert "platform_initialized" in status
        assert status["platform_initialized"] is True
    
    @pytest.mark.asyncio
    async def test_intelligent_workflow_composition(self, phase4_platform):
        """Test intelligent workflow composition."""
        requirements = "Analyze sales data and generate a comprehensive report"
        context = {"domain": "sales", "urgency": "high"}
        
        # Mock the workflow composer
        mock_workflow = {
            "workflow": {
                "workflow_id": "test_workflow_1",
                "name": "Sales Analysis Workflow",
                "nodes": ["data_collection", "analysis", "reporting"],
                "edges": [("data_collection", "analysis"), ("analysis", "reporting")],
                "confidence_score": 0.9
            },
            "marketplace_integration": True,
            "pattern_optimization": True
        }
        
        with patch.object(phase4_platform.workflow_composer, 'compose_workflow', return_value=mock_workflow["workflow"]):
            result = await phase4_platform.compose_intelligent_workflow(requirements, context)
            
            assert result is not None
            assert result["marketplace_integration"] is True
            assert result["pattern_optimization"] is True
            assert result["workflow"]["confidence_score"] == 0.9
    
    @pytest.mark.asyncio
    async def test_capability_discovery(self, phase4_platform):
        """Test optimal capability discovery."""
        requirements = {
            "requester_id": "test_user",
            "category": "data_analysis",
            "budget": 100.0,
            "deadline": datetime.now() + timedelta(hours=2)
        }
        
        # Mock marketplace recommendations
        mock_recommendations = [
            {
                "capability_id": "data_analysis_pro",
                "agent_id": "analyst_agent",
                "score": 0.95,
                "price": 50.0
            },
            {
                "capability_id": "data_analysis_basic",
                "agent_id": "basic_agent", 
                "score": 0.85,
                "price": 30.0
            }
        ]
        
        with patch.object(phase4_platform.marketplace, 'get_capability_recommendations', return_value=mock_recommendations):
            capabilities = await phase4_platform.discover_optimal_capabilities(requirements)
            
            assert capabilities is not None
            assert len(capabilities) == 2
            assert capabilities[0]["score"] == 0.95
            assert capabilities[1]["score"] == 0.85
    
    @pytest.mark.asyncio
    async def test_cross_component_integration(self, phase4_platform):
        """Test integration between Phase 4 components."""
        # Test that workflow composer can access marketplace
        assert phase4_platform.workflow_composer.marketplace is not None
        
        # Test that pattern recognition is connected to workflow composer
        assert phase4_platform.workflow_composer.pattern_recognizer is not None
        
        # Test that marketplace uses trading engine and certification system
        assert phase4_platform.marketplace.trading_engine is not None
        assert phase4_platform.marketplace.certification_system is not None
    
    @pytest.mark.asyncio
    async def test_platform_analytics(self, phase4_platform):
        """Test platform analytics collection."""
        # Mock analytics from all components
        with patch.object(phase4_platform.marketplace, 'get_marketplace_analytics', return_value={"total_listings": 10}), \
             patch.object(phase4_platform.trading_engine, 'get_market_analytics', return_value={"total_trades": 5}), \
             patch.object(phase4_platform.certification_system, 'get_certification_analytics', return_value={"total_certifications": 8}), \
             patch.object(phase4_platform.workflow_composer, 'get_composition_analytics', return_value={"total_workflows": 15}), \
             patch.object(phase4_platform.pattern_recognition, 'get_pattern_analytics', return_value={"total_patterns": 12}):
            
            analytics = await phase4_platform.get_platform_analytics()
            
            assert analytics is not None
            assert "marketplace" in analytics
            assert "trading" in analytics
            assert "certification" in analytics
            assert "composition" in analytics
            assert "patterns" in analytics
            assert "cross_component_metrics" in analytics
    
    @pytest.mark.asyncio
    async def test_background_services(self, phase4_platform):
        """Test background services are properly configured."""
        # Check that background services would be started
        # (We can't test actual background tasks in unit tests)
        
        # Verify the platform has the necessary methods for background services
        assert hasattr(phase4_platform, '_pattern_learning_service')
        assert hasattr(phase4_platform, '_workflow_optimization_service')
        assert hasattr(phase4_platform, '_marketplace_maintenance_service')
    
    @pytest.mark.asyncio
    async def test_error_handling(self, phase4_platform):
        """Test error handling in Phase 4 components."""
        # Test graceful handling of component failures
        with patch.object(phase4_platform.workflow_composer, 'compose_workflow', side_effect=Exception("Test error")):
            result = await phase4_platform.compose_intelligent_workflow("test requirements")
            # Should handle error gracefully and not crash
            assert result is None or "error" in result
    
    @pytest.mark.asyncio
    async def test_configuration_integration(self, phase4_platform):
        """Test configuration integration."""
        # Test that platform respects configuration settings
        assert phase4_platform.config is not None
        assert isinstance(phase4_platform.config, dict)
        
        # Test configuration keys
        expected_config_keys = [
            "enable_auto_certification",
            "enable_pattern_learning", 
            "enable_workflow_optimization",
            "marketplace_auto_pricing",
            "trading_auto_selection"
        ]
        
        for key in expected_config_keys:
            assert key in phase4_platform.config


class TestPhase4WorkflowManagerIntegration:
    """Test Phase 4 integration with WorkflowManager."""
    
    @pytest.fixture
    async def workflow_manager(self):
        """Create a test workflow manager with Phase 4 integration."""
        manager = WorkflowManager()
        
        # Mock Phase 4 initialization
        with patch('backend.agents.langgraph.phase4_integration.Phase4PlatformEvolution') as mock_platform_class:
            mock_platform = AsyncMock()
            mock_platform.initialize.return_value = True
            mock_platform_class.return_value = mock_platform
            
            # Initialize Phase 4 services
            await manager._initialize_phase4_services()
            manager.phase4_platform = mock_platform
        
        return manager
    
    @pytest.mark.asyncio
    async def test_workflow_manager_phase4_integration(self, workflow_manager):
        """Test WorkflowManager Phase 4 integration."""
        assert workflow_manager.phase4_enabled is True
        assert workflow_manager.phase4_platform is not None
    
    @pytest.mark.asyncio
    async def test_intelligent_workflow_composition_via_manager(self, workflow_manager):
        """Test intelligent workflow composition through WorkflowManager."""
        requirements = "Create a marketing campaign analysis"
        context = {"domain": "marketing"}
        
        # Mock the platform method
        workflow_manager.phase4_platform.compose_intelligent_workflow.return_value = {
            "workflow_id": "marketing_workflow_1",
            "success": True
        }
        
        result = await workflow_manager.compose_intelligent_workflow(requirements, context)
        
        assert result is not None
        assert result["success"] is True
        workflow_manager.phase4_platform.compose_intelligent_workflow.assert_called_once_with(requirements, context)
    
    @pytest.mark.asyncio
    async def test_capability_discovery_via_manager(self, workflow_manager):
        """Test capability discovery through WorkflowManager."""
        requirements = {"category": "analysis", "budget": 50.0}
        
        # Mock the platform method
        workflow_manager.phase4_platform.discover_optimal_capabilities.return_value = [
            {"capability_id": "analysis_1", "score": 0.9}
        ]
        
        result = await workflow_manager.discover_optimal_capabilities(requirements)
        
        assert result is not None
        assert len(result) == 1
        assert result[0]["score"] == 0.9
        workflow_manager.phase4_platform.discover_optimal_capabilities.assert_called_once_with(requirements)
