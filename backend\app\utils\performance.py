"""
Performance optimization utilities for Datagenius MCP integration.

This module provides caching, connection pooling, and performance monitoring
utilities for the MCP server system.
"""

import asyncio
import logging
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Callable, TypeVar, Generic
from functools import wraps
from collections import defaultdict
import weakref

logger = logging.getLogger(__name__)

T = TypeVar('T')


class LRUCache(Generic[T]):
    """Thread-safe LRU cache implementation."""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: int = 3600):
        """
        Initialize LRU cache.
        
        Args:
            max_size: Maximum number of items to cache
            ttl_seconds: Time-to-live for cache entries in seconds
        """
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.access_order: List[str] = []
        self._lock = asyncio.Lock()
    
    async def get(self, key: str) -> Optional[T]:
        """Get item from cache."""
        async with self._lock:
            if key not in self.cache:
                return None
            
            entry = self.cache[key]
            
            # Check TTL
            if self._is_expired(entry):
                await self._remove_key(key)
                return None
            
            # Update access order
            self.access_order.remove(key)
            self.access_order.append(key)
            
            return entry['value']
    
    async def set(self, key: str, value: T) -> None:
        """Set item in cache."""
        async with self._lock:
            now = time.time()
            
            if key in self.cache:
                # Update existing entry
                self.cache[key] = {
                    'value': value,
                    'timestamp': now,
                    'access_count': self.cache[key]['access_count'] + 1
                }
                # Update access order
                self.access_order.remove(key)
                self.access_order.append(key)
            else:
                # Add new entry
                self.cache[key] = {
                    'value': value,
                    'timestamp': now,
                    'access_count': 1
                }
                self.access_order.append(key)
                
                # Evict if necessary
                if len(self.cache) > self.max_size:
                    await self._evict_oldest()
    
    async def remove(self, key: str) -> bool:
        """Remove item from cache."""
        async with self._lock:
            return await self._remove_key(key)
    
    async def clear(self) -> None:
        """Clear all cache entries."""
        async with self._lock:
            self.cache.clear()
            self.access_order.clear()
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        async with self._lock:
            total_access_count = sum(entry['access_count'] for entry in self.cache.values())
            
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hit_rate': 0.0,  # Would need to track hits/misses
                'total_accesses': total_access_count,
                'oldest_entry': min(entry['timestamp'] for entry in self.cache.values()) if self.cache else None,
                'newest_entry': max(entry['timestamp'] for entry in self.cache.values()) if self.cache else None
            }
    
    def _is_expired(self, entry: Dict[str, Any]) -> bool:
        """Check if cache entry is expired."""
        return time.time() - entry['timestamp'] > self.ttl_seconds
    
    async def _remove_key(self, key: str) -> bool:
        """Remove key from cache (internal method)."""
        if key in self.cache:
            del self.cache[key]
            self.access_order.remove(key)
            return True
        return False
    
    async def _evict_oldest(self) -> None:
        """Evict oldest cache entry."""
        if self.access_order:
            oldest_key = self.access_order[0]
            await self._remove_key(oldest_key)


class ConnectionPool:
    """Connection pool for MCP clients."""
    
    def __init__(self, max_connections: int = 10, connection_timeout: int = 30):
        """
        Initialize connection pool.
        
        Args:
            max_connections: Maximum number of connections per server
            connection_timeout: Connection timeout in seconds
        """
        self.max_connections = max_connections
        self.connection_timeout = connection_timeout
        self.pools: Dict[str, List[Any]] = defaultdict(list)
        self.active_connections: Dict[str, int] = defaultdict(int)
        self._locks: Dict[str, asyncio.Lock] = defaultdict(asyncio.Lock)
    
    async def get_connection(self, server_id: str, create_func: Callable) -> Any:
        """
        Get connection from pool or create new one.
        
        Args:
            server_id: Server identifier
            create_func: Function to create new connection
            
        Returns:
            Connection object
        """
        async with self._locks[server_id]:
            # Try to get existing connection from pool
            if self.pools[server_id]:
                connection = self.pools[server_id].pop()
                self.active_connections[server_id] += 1
                return connection
            
            # Create new connection if under limit
            if self.active_connections[server_id] < self.max_connections:
                connection = await create_func()
                self.active_connections[server_id] += 1
                return connection
            
            # Wait for connection to become available
            # In a real implementation, you'd use a condition variable here
            await asyncio.sleep(0.1)
            return await self.get_connection(server_id, create_func)
    
    async def return_connection(self, server_id: str, connection: Any) -> None:
        """Return connection to pool."""
        async with self._locks[server_id]:
            if len(self.pools[server_id]) < self.max_connections:
                self.pools[server_id].append(connection)
            else:
                # Close excess connection
                if hasattr(connection, 'close'):
                    await connection.close()
            
            self.active_connections[server_id] -= 1
    
    async def close_all(self, server_id: Optional[str] = None) -> None:
        """Close all connections for a server or all servers."""
        if server_id:
            async with self._locks[server_id]:
                for connection in self.pools[server_id]:
                    if hasattr(connection, 'close'):
                        await connection.close()
                self.pools[server_id].clear()
                self.active_connections[server_id] = 0
        else:
            for sid in list(self.pools.keys()):
                await self.close_all(sid)


class PerformanceMonitor:
    """Performance monitoring for MCP operations."""
    
    def __init__(self):
        """Initialize performance monitor."""
        self.metrics: Dict[str, List[float]] = defaultdict(list)
        self.counters: Dict[str, int] = defaultdict(int)
        self.start_times: Dict[str, float] = {}
    
    def start_timer(self, operation: str) -> str:
        """Start timing an operation."""
        timer_id = f"{operation}_{int(time.time() * 1000000)}"
        self.start_times[timer_id] = time.time()
        return timer_id
    
    def end_timer(self, timer_id: str) -> float:
        """End timing and record duration."""
        if timer_id not in self.start_times:
            return 0.0
        
        duration = time.time() - self.start_times[timer_id]
        operation = timer_id.rsplit('_', 1)[0]
        
        self.metrics[operation].append(duration)
        del self.start_times[timer_id]
        
        # Keep only last 1000 measurements
        if len(self.metrics[operation]) > 1000:
            self.metrics[operation] = self.metrics[operation][-1000:]
        
        return duration
    
    def increment_counter(self, counter: str, value: int = 1) -> None:
        """Increment a counter."""
        self.counters[counter] += value
    
    def get_stats(self, operation: Optional[str] = None) -> Dict[str, Any]:
        """Get performance statistics."""
        if operation:
            if operation not in self.metrics:
                return {}
            
            durations = self.metrics[operation]
            return {
                'operation': operation,
                'count': len(durations),
                'avg_duration': sum(durations) / len(durations) if durations else 0,
                'min_duration': min(durations) if durations else 0,
                'max_duration': max(durations) if durations else 0,
                'total_duration': sum(durations)
            }
        else:
            stats = {}
            for op, durations in self.metrics.items():
                stats[op] = {
                    'count': len(durations),
                    'avg_duration': sum(durations) / len(durations) if durations else 0,
                    'min_duration': min(durations) if durations else 0,
                    'max_duration': max(durations) if durations else 0,
                    'total_duration': sum(durations)
                }
            
            stats['counters'] = dict(self.counters)
            return stats
    
    def reset_stats(self, operation: Optional[str] = None) -> None:
        """Reset statistics."""
        if operation:
            if operation in self.metrics:
                self.metrics[operation].clear()
        else:
            self.metrics.clear()
            self.counters.clear()


def performance_monitor(operation: str):
    """Decorator for monitoring function performance."""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            timer_id = perf_monitor.start_timer(operation)
            try:
                result = await func(*args, **kwargs)
                perf_monitor.increment_counter(f"{operation}_success")
                return result
            except Exception as e:
                perf_monitor.increment_counter(f"{operation}_error")
                raise
            finally:
                duration = perf_monitor.end_timer(timer_id)
                logger.debug(f"Operation {operation} took {duration:.3f}s")
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            timer_id = perf_monitor.start_timer(operation)
            try:
                result = func(*args, **kwargs)
                perf_monitor.increment_counter(f"{operation}_success")
                return result
            except Exception as e:
                perf_monitor.increment_counter(f"{operation}_error")
                raise
            finally:
                duration = perf_monitor.end_timer(timer_id)
                logger.debug(f"Operation {operation} took {duration:.3f}s")
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator


def cached(cache_key_func: Callable = None, ttl: int = 3600):
    """Decorator for caching function results."""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Generate cache key
            if cache_key_func:
                cache_key = cache_key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}_{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # Try to get from cache
            cached_result = await result_cache.get(cache_key)
            if cached_result is not None:
                perf_monitor.increment_counter(f"{func.__name__}_cache_hit")
                return cached_result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            await result_cache.set(cache_key, result)
            perf_monitor.increment_counter(f"{func.__name__}_cache_miss")
            
            return result
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else func
    return decorator


class ResourceManager:
    """Resource manager for cleanup and lifecycle management."""
    
    def __init__(self):
        """Initialize resource manager."""
        self.resources: Dict[str, Any] = {}
        self.cleanup_callbacks: Dict[str, List[Callable]] = defaultdict(list)
        self._finalizers = weakref.WeakValueDictionary()
    
    def register_resource(self, resource_id: str, resource: Any, cleanup_callback: Callable = None) -> None:
        """Register a resource for management."""
        self.resources[resource_id] = resource
        
        if cleanup_callback:
            self.cleanup_callbacks[resource_id].append(cleanup_callback)
        
        # Register finalizer for automatic cleanup
        self._finalizers[resource_id] = resource
    
    async def cleanup_resource(self, resource_id: str) -> None:
        """Clean up a specific resource."""
        if resource_id in self.resources:
            resource = self.resources[resource_id]
            
            # Run cleanup callbacks
            for callback in self.cleanup_callbacks[resource_id]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(resource)
                    else:
                        callback(resource)
                except Exception as e:
                    logger.error(f"Error in cleanup callback for {resource_id}: {e}")
            
            # Remove from tracking
            del self.resources[resource_id]
            if resource_id in self.cleanup_callbacks:
                del self.cleanup_callbacks[resource_id]
    
    async def cleanup_all(self) -> None:
        """Clean up all managed resources."""
        resource_ids = list(self.resources.keys())
        for resource_id in resource_ids:
            await self.cleanup_resource(resource_id)


async def test_mcp_connection(server_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Test MCP server connection.

    Args:
        server_config: Server configuration dictionary

    Returns:
        Dictionary with connection test results
    """
    try:
        # Basic validation
        if not server_config.get('command'):
            return {
                'success': False,
                'error': 'No command specified in server configuration',
                'latency_ms': 0
            }

        start_time = time.time()

        # For now, return a basic success response
        # In a full implementation, this would actually test the connection
        latency_ms = (time.time() - start_time) * 1000

        return {
            'success': True,
            'latency_ms': round(latency_ms, 2),
            'server_id': server_config.get('id', 'unknown'),
            'message': 'Connection test completed successfully'
        }

    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'latency_ms': 0
        }


# Global instances
result_cache = LRUCache(max_size=1000, ttl_seconds=3600)
connection_pool = ConnectionPool(max_connections=10)
perf_monitor = PerformanceMonitor()
resource_manager = ResourceManager()

# Aliases for backward compatibility
cache = result_cache
