"""
Persona service for the Datagenius backend.

This module provides services for managing AI personas, including purchasing and access control.
"""

import logging
import uuid
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session

from ..database import (
    get_persona, get_personas, create_persona, update_persona, delete_persona,
    has_user_purchased_persona, get_user_purchased_personas,
    create_purchase, add_purchased_item, update_purchase_status
)

# Configure logging
logger = logging.getLogger(__name__)


class PersonaService:
    """
    Service for managing AI personas.

    This class provides methods for managing AI personas, including purchasing and access control.
    """

    @staticmethod
    def get_all_personas(db: Session, skip: int = 0, limit: int = 100, industry: Optional[str] = None) -> List[Any]:
        """
        Get all personas.

        Args:
            db: Database session
            skip: Number of personas to skip
            limit: Maximum number of personas to return
            industry: Optional filter by industry

        Returns:
            List of personas
        """
        return get_personas(db, skip, limit, industry)

    @staticmethod
    def get_persona_by_id(db: Session, persona_id: str) -> Optional[Any]:
        """
        Get a persona by ID.

        Args:
            db: Database session
            persona_id: ID of the persona

        Returns:
            Persona if found, None otherwise
        """
        return get_persona(db, persona_id)

    @staticmethod
    def has_user_purchased_persona(db: Session, user_id: int, persona_id: str) -> bool:
        """
        Check if a user has purchased a persona.

        Args:
            db: Database session
            user_id: ID of the user
            persona_id: ID of the persona

        Returns:
            True if the user has purchased the persona or if it's the concierge agent, False otherwise
        """
        # Always return True for the concierge agent (both IDs)
        if persona_id in ["concierge", "concierge-agent"]:
            return True

        # For other personas, check if the user has purchased them
        return has_user_purchased_persona(db, user_id, persona_id)

    @staticmethod
    def get_user_purchased_personas(db: Session, user_id: int) -> List[str]:
        """
        Get all personas purchased by a user.

        Args:
            db: Database session
            user_id: ID of the user

        Returns:
            List of persona IDs purchased by the user, always including the concierge agent
        """
        # Get the list of purchased personas
        purchased_personas = get_user_purchased_personas(db, user_id)

        # Always include both concierge agent IDs if they're not already present
        if "concierge" not in purchased_personas:
            purchased_personas.append("concierge")
        if "concierge-agent" not in purchased_personas:
            purchased_personas.append("concierge-agent")

        return purchased_personas

    @staticmethod
    def purchase_persona(db: Session, user_id: int, persona_id: str, payment_method: str = "credit_card") -> Dict[str, Any]:
        """
        Purchase a persona for a user.

        Args:
            db: Database session
            user_id: ID of the user
            persona_id: ID of the persona
            payment_method: Payment method to use

        Returns:
            Dictionary with purchase information
        """
        # Check if the persona exists
        persona = get_persona(db, persona_id)
        if not persona:
            logger.error(f"Persona {persona_id} not found")
            raise ValueError(f"Persona {persona_id} not found")

        # Check if the user has already purchased the persona
        if has_user_purchased_persona(db, user_id, persona_id):
            logger.info(f"User {user_id} has already purchased persona {persona_id}")
            return {"message": "Persona already purchased", "already_purchased": True}

        # Create a purchase
        purchase = create_purchase(
            db,
            user_id=user_id,
            payment_method=payment_method,
            total_amount=persona.price
        )
        logger.info(f"Created purchase {purchase.id} for user {user_id}")

        # Add the purchased item
        purchased_item = add_purchased_item(
            db,
            purchase_id=purchase.id,
            persona_id=persona_id,
            quantity=1,
            price=persona.price
        )
        logger.info(f"Added purchased item {purchased_item.id} for purchase {purchase.id}")

        # Update the purchase status to completed
        updated_purchase = update_purchase_status(db, purchase.id, "completed")
        logger.info(f"Updated purchase {purchase.id} status to completed")

        # Refresh user permissions to include the newly purchased persona
        try:
            from agents.tools.mcp.security.access_control import refresh_user_permissions
            refresh_user_permissions(str(user_id))
            logger.info(f"Refreshed permissions for user {user_id} after purchasing {persona_id}")
        except Exception as e:
            logger.warning(f"Failed to refresh permissions for user {user_id}: {e}")

        return {
            "purchase_id": purchase.id,
            "persona_id": persona_id,
            "total_amount": persona.price,
            "status": "completed",
            "message": "Persona purchased successfully"
        }

    @staticmethod
    def get_available_personas_for_user(db: Session, user_id: int, skip: int = 0, limit: int = 100, industry: Optional[str] = None) -> List[Any]:
        """
        Get all personas available for a user.

        This includes all personas that the user has purchased and the concierge agent.

        Args:
            db: Database session
            user_id: ID of the user
            skip: Number of personas to skip
            limit: Maximum number of personas to return
            industry: Optional filter by industry

        Returns:
            List of personas available for the user
        """
        # Get all personas
        all_personas = get_personas(db, skip, limit, industry)

        # Get purchased persona IDs
        purchased_persona_ids = get_user_purchased_personas(db, user_id)

        # Filter personas to include purchased ones, free ones (price == 0), and the concierge agent
        available_personas = []
        for p in all_personas:
            # A persona is available if it's purchased OR if it's free (price is 0) OR if it's the concierge agent
            if p.id in purchased_persona_ids or p.price == 0 or p.id in ["concierge", "concierge-agent"]:
                available_personas.append(p)

        return available_personas


# Create a singleton instance
persona_service = PersonaService()
