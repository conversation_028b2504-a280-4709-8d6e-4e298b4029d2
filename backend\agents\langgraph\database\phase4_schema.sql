-- Phase 4: Platform Evolution Database Schema
-- PostgreSQL schema for Phase 4 components

-- Capability Marketplace Tables
CREATE TABLE IF NOT EXISTS capability_listings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    capability_id VARCHAR(255) UNIQUE NOT NULL,
    agent_id VARCHAR(255) NOT NULL,
    name VA<PERSON>HAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    version VARCHAR(50) DEFAULT '1.0.0',
    price DECIMAL(10,2) DEFAULT 0.00,
    status VARCHAR(50) DEFAULT 'available',
    trading_mode VARCHAR(50) DEFAULT 'fixed_price',
    performance_score DECIMAL(3,2) DEFAULT 0.80,
    success_rate DECIMAL(3,2) DEFAULT 0.80,
    usage_count INTEGER DEFAULT 0,
    certification_level VARCHAR(50),
    is_public BOOLEAN DEFAULT true,
    estimated_execution_time INTEGER DEFAULT 3600,
    complexity_level VARCHAR(50) DEFAULT 'medium',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE IF NOT EXISTS capability_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    request_id VARCHAR(255) UNIQUE NOT NULL,
    requester_id VARCHAR(255) NOT NULL,
    requirements JSONB NOT NULL,
    budget DECIMAL(10,2) DEFAULT 0.00,
    deadline TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) DEFAULT 'pending',
    selected_capability_id VARCHAR(255),
    selected_agent VARCHAR(255),
    final_price DECIMAL(10,2),
    quality_score DECIMAL(3,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Trading Engine Tables
CREATE TABLE IF NOT EXISTS trades (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    trade_id VARCHAR(255) UNIQUE NOT NULL,
    request_id VARCHAR(255) NOT NULL,
    requester_id VARCHAR(255) NOT NULL,
    capability_requirements JSONB NOT NULL,
    budget DECIMAL(10,2) NOT NULL,
    deadline TIMESTAMP WITH TIME ZONE NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    trading_mode VARCHAR(50) DEFAULT 'auction',
    min_quality_score DECIMAL(3,2) DEFAULT 0.70,
    max_execution_time INTEGER DEFAULT 3600,
    selected_agent VARCHAR(255),
    winning_bid_id UUID,
    final_price DECIMAL(10,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    execution_started_at TIMESTAMP WITH TIME ZONE,
    execution_completed_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE IF NOT EXISTS bids (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bid_id VARCHAR(255) UNIQUE NOT NULL,
    trade_id VARCHAR(255) NOT NULL,
    agent_id VARCHAR(255) NOT NULL,
    capability_id VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    estimated_execution_time INTEGER NOT NULL,
    quality_score DECIMAL(3,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    bid_details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    accepted_at TIMESTAMP WITH TIME ZONE,
    rejected_at TIMESTAMP WITH TIME ZONE
);

-- Certification System Tables
CREATE TABLE IF NOT EXISTS certifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    certification_id VARCHAR(255) UNIQUE NOT NULL,
    capability_id VARCHAR(255) NOT NULL,
    agent_id VARCHAR(255) NOT NULL,
    target_level VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    performance_metrics JSONB,
    test_results JSONB,
    quality_assessment JSONB,
    compliance_check JSONB,
    certification_score DECIMAL(3,2),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE
);

-- Workflow Pattern Recognition Tables
CREATE TABLE IF NOT EXISTS workflow_patterns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    pattern_id VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    pattern_type VARCHAR(50) NOT NULL,
    workflow_structure JSONB NOT NULL,
    performance_metrics JSONB,
    usage_count INTEGER DEFAULT 0,
    confidence_score DECIMAL(3,2) DEFAULT 0.80,
    complexity_level VARCHAR(50) DEFAULT 'medium',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE IF NOT EXISTS pattern_usage_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    pattern_id VARCHAR(255) NOT NULL,
    workflow_id VARCHAR(255),
    agent_id VARCHAR(255),
    execution_time INTEGER,
    success BOOLEAN,
    quality_score DECIMAL(3,2),
    performance_metrics JSONB,
    used_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- AI Workflow Composer Tables
CREATE TABLE IF NOT EXISTS composed_workflows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    requirements JSONB NOT NULL,
    workflow_structure JSONB NOT NULL,
    agent_assignments JSONB,
    performance_metrics JSONB,
    optimization_applied JSONB,
    status VARCHAR(50) DEFAULT 'draft',
    complexity VARCHAR(50) DEFAULT 'medium',
    estimated_execution_time INTEGER,
    created_by VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    compiled_at TIMESTAMP WITH TIME ZONE,
    executed_at TIMESTAMP WITH TIME ZONE
);

-- Execution History Tables
CREATE TABLE IF NOT EXISTS execution_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    execution_id VARCHAR(255) UNIQUE NOT NULL,
    workflow_id VARCHAR(255),
    capability_id VARCHAR(255),
    agent_id VARCHAR(255),
    execution_time INTEGER,
    success BOOLEAN NOT NULL,
    quality_score DECIMAL(3,2),
    error_message TEXT,
    performance_data JSONB,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Agent Performance Tracking
CREATE TABLE IF NOT EXISTS agent_performance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id VARCHAR(255) NOT NULL,
    capability_id VARCHAR(255),
    success_rate DECIMAL(3,2) DEFAULT 0.80,
    avg_execution_time INTEGER DEFAULT 3600,
    quality_score DECIMAL(3,2) DEFAULT 0.80,
    total_executions INTEGER DEFAULT 0,
    last_execution_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(agent_id, capability_id)
);

-- Market Analytics Tables
CREATE TABLE IF NOT EXISTS market_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    category VARCHAR(100) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(10,4) NOT NULL,
    additional_data JSONB,
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(category, metric_name, recorded_at)
);

-- Indexes for Performance
CREATE INDEX IF NOT EXISTS idx_capability_listings_category ON capability_listings(category);
CREATE INDEX IF NOT EXISTS idx_capability_listings_agent_id ON capability_listings(agent_id);
CREATE INDEX IF NOT EXISTS idx_capability_listings_status ON capability_listings(status);
CREATE INDEX IF NOT EXISTS idx_capability_requests_requester_id ON capability_requests(requester_id);
CREATE INDEX IF NOT EXISTS idx_capability_requests_status ON capability_requests(status);
CREATE INDEX IF NOT EXISTS idx_trades_requester_id ON trades(requester_id);
CREATE INDEX IF NOT EXISTS idx_trades_status ON trades(status);
CREATE INDEX IF NOT EXISTS idx_bids_trade_id ON bids(trade_id);
CREATE INDEX IF NOT EXISTS idx_bids_agent_id ON bids(agent_id);
CREATE INDEX IF NOT EXISTS idx_certifications_capability_id ON certifications(capability_id);
CREATE INDEX IF NOT EXISTS idx_certifications_agent_id ON certifications(agent_id);
CREATE INDEX IF NOT EXISTS idx_workflow_patterns_category ON workflow_patterns(category);
CREATE INDEX IF NOT EXISTS idx_pattern_usage_history_pattern_id ON pattern_usage_history(pattern_id);
CREATE INDEX IF NOT EXISTS idx_execution_history_capability_id ON execution_history(capability_id);
CREATE INDEX IF NOT EXISTS idx_execution_history_agent_id ON execution_history(agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_performance_agent_id ON agent_performance(agent_id);
CREATE INDEX IF NOT EXISTS idx_market_analytics_category ON market_analytics(category);

-- Foreign Key Constraints
ALTER TABLE capability_requests ADD CONSTRAINT fk_capability_requests_capability 
    FOREIGN KEY (selected_capability_id) REFERENCES capability_listings(capability_id) ON DELETE SET NULL;

ALTER TABLE bids ADD CONSTRAINT fk_bids_trade 
    FOREIGN KEY (trade_id) REFERENCES trades(trade_id) ON DELETE CASCADE;

ALTER TABLE trades ADD CONSTRAINT fk_trades_winning_bid 
    FOREIGN KEY (winning_bid_id) REFERENCES bids(id) ON DELETE SET NULL;

ALTER TABLE pattern_usage_history ADD CONSTRAINT fk_pattern_usage_pattern 
    FOREIGN KEY (pattern_id) REFERENCES workflow_patterns(pattern_id) ON DELETE CASCADE;

-- Update Triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_capability_listings_updated_at BEFORE UPDATE ON capability_listings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_capability_requests_updated_at BEFORE UPDATE ON capability_requests 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_trades_updated_at BEFORE UPDATE ON trades 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_certifications_updated_at BEFORE UPDATE ON certifications 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_workflow_patterns_updated_at BEFORE UPDATE ON workflow_patterns 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_composed_workflows_updated_at BEFORE UPDATE ON composed_workflows 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_performance_updated_at BEFORE UPDATE ON agent_performance 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
