import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { chatApi, type Message, type EditMessageResponse } from '@/lib/api';
import { 
  Edit3, 
  Save, 
  X, 
  Clock, 
  User, 
  Bot,
  AlertCircle,
  CheckCircle
} from 'lucide-react';

interface MessageEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  message: Message;
  onMessageEdited?: (editResponse: EditMessageResponse) => void;
  onResubmit?: (messageId: string) => void;
}

export const MessageEditModal: React.FC<MessageEditModalProps> = ({
  isOpen,
  onClose,
  message,
  onMessageEdited,
  onResubmit
}) => {
  const [editedContent, setEditedContent] = useState(message.content);
  const [isLoading, setIsLoading] = useState(false);
  const [showResubmitOption, setShowResubmitOption] = useState(false);
  const { toast } = useToast();

  // Reset content when message changes
  useEffect(() => {
    setEditedContent(message.content);
    setShowResubmitOption(false);
  }, [message]);

  const handleSave = async () => {
    if (editedContent.trim() === message.content.trim()) {
      toast({
        title: 'No changes detected',
        description: 'The message content is unchanged.',
        variant: 'default'
      });
      return;
    }

    setIsLoading(true);
    try {
      const editResponse = await chatApi.editMessage(
        message.id,
        editedContent.trim(),
        { edited_at: new Date().toISOString() }
      );

      toast({
        title: 'Message edited successfully',
        description: 'Your message has been updated.',
      });

      onMessageEdited?.(editResponse);
      setShowResubmitOption(true);

    } catch (error) {
      console.error('Error editing message:', error);
      toast({
        title: 'Failed to edit message',
        description: error instanceof Error ? error.message : 'An unexpected error occurred.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResubmit = async () => {
    if (!showResubmitOption) return;

    setIsLoading(true);
    try {
      await chatApi.resubmitMessage(message.id, {
        resubmitted_at: new Date().toISOString(),
        original_content: message.content,
        edited_content: editedContent
      });

      toast({
        title: 'Message resubmitted',
        description: 'The edited message has been sent to the agent for reprocessing.',
      });

      onResubmit?.(message.id);
      onClose();

    } catch (error) {
      console.error('Error resubmitting message:', error);
      toast({
        title: 'Failed to resubmit message',
        description: error instanceof Error ? error.message : 'An unexpected error occurred.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setEditedContent(message.content);
    setShowResubmitOption(false);
    onClose();
  };

  const isContentChanged = editedContent.trim() !== message.content.trim();
  const isUser = message.sender === 'user';

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit3 className="h-5 w-5" />
            Edit Message
          </DialogTitle>
          <DialogDescription className="flex items-center gap-2">
            <div className="flex items-center gap-2">
              {isUser ? (
                <User className="h-4 w-4 text-blue-500" />
              ) : (
                <Bot className="h-4 w-4 text-green-500" />
              )}
              <span>{isUser ? 'User' : 'AI'} message</span>
              <Badge variant="outline" className="text-xs">
                <Clock className="h-3 w-3 mr-1" />
                {new Date(message.created_at).toLocaleString()}
              </Badge>
            </div>
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col gap-4">
          <div>
            <Label htmlFor="message-content" className="text-sm font-medium">
              Message Content
            </Label>
            <Textarea
              id="message-content"
              value={editedContent}
              onChange={(e) => setEditedContent(e.target.value)}
              className="min-h-[200px] mt-2 resize-none"
              placeholder="Enter your message content..."
              disabled={isLoading}
            />
            <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
              <span>{editedContent.length} characters</span>
              {isContentChanged && (
                <Badge variant="secondary" className="text-xs">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Modified
                </Badge>
              )}
            </div>
          </div>

          {showResubmitOption && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="p-4 bg-green-50 border border-green-200 rounded-lg"
            >
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">
                  Message edited successfully!
                </span>
              </div>
              <p className="text-sm text-green-700 mb-3">
                Would you like to resubmit this edited message to the agent for reprocessing?
              </p>
              <Button
                onClick={handleResubmit}
                disabled={isLoading}
                size="sm"
                className="bg-green-600 hover:bg-green-700"
              >
                Resubmit to Agent
              </Button>
            </motion.div>
          )}
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isLoading}
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isLoading || !isContentChanged}
            className="min-w-[100px]"
          >
            {isLoading ? (
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                className="h-4 w-4 border-2 border-white border-t-transparent rounded-full"
              />
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
