# Unified Persona Configuration for LangGraph-based Datagenius System
# This configuration defines the consolidated persona system that replaces
# individual agent implementations with a unified, strategy-based approach.

# Global configuration
global_config:
  enable_unified_personas: true
  legacy_support: false
  enable_cross_agent_intelligence: true
  enable_business_profile_integration: true
  default_persona_type: "concierge"
  
  # Performance settings
  performance:
    enable_caching: true
    cache_ttl_seconds: 3600
    max_concurrent_tools: 5
    tool_execution_timeout: 30
    
  # Monitoring settings
  monitoring:
    enable_metrics: true
    enable_performance_tracking: true
    enable_error_tracking: true

# Persona definitions
personas:
  # Analysis Persona
  analysis:
    persona_id: "unified-analysis"
    agent_type: "analysis"
    name: "Data Analysis AI"
    description: "Expert data analyst specializing in insights, visualization, and statistical analysis"
    
    # Capabilities and intents
    capabilities:
      - "data_analysis"
      - "visualization_creation"
      - "statistical_analysis"
      - "report_generation"
      - "trend_analysis"
      - "data_exploration"
      - "chart_creation"
      - "dashboard_generation"
    
    supported_intents:
      - "data_analysis"
      - "visualization_request"
      - "statistical_analysis"
      - "report_generation"
      - "trend_analysis"
      - "data_exploration"
      - "chart_creation"
      - "dashboard_request"
    
    # Methodology framework
    methodology_framework: "UNDERSTAND_ASSESS_EXECUTE_DELIVER"
    
    # Specialized tools
    tools:
      - "data_analyzer"
      - "chart_generator"
      - "report_generator"
      - "pandasai_query"
      - "statistical_analyzer"
      - "trend_detector"
      - "data_cleaner"
      - "visualization_engine"
    
    # Strategy configuration
    strategy_config:
      enable_uaed_framework: true
      enable_statistical_validation: true
      enable_data_quality_checks: true
      default_chart_types: ["bar", "line", "scatter", "histogram"]
      
  # Marketing Persona
  marketing:
    persona_id: "unified-marketing"
    agent_type: "marketing"
    name: "Marketing AI"
    description: "Marketing strategist and content creator specializing in campaigns and brand development"
    
    # Capabilities and intents
    capabilities:
      - "content_generation"
      - "campaign_creation"
      - "social_media_management"
      - "brand_strategy"
      - "marketing_analysis"
      - "audience_targeting"
      - "performance_tracking"
      - "competitive_analysis"
    
    supported_intents:
      - "content_creation"
      - "campaign_planning"
      - "social_media_post"
      - "marketing_strategy"
      - "brand_development"
      - "audience_analysis"
      - "performance_review"
      - "competitor_analysis"
    
    # Specialized tools
    tools:
      - "content_generator"
      - "social_media_poster"
      - "campaign_analyzer"
      - "brand_strategy_generator"
      - "audience_analyzer"
      - "performance_tracker"
      - "competitor_analyzer"
      - "email_marketing_generator"
    
    # Strategy configuration
    strategy_config:
      enable_brand_voice_consistency: true
      enable_audience_personalization: true
      enable_performance_optimization: true
      default_content_types: ["blog", "social", "email", "ad_copy"]
      
  # Concierge Persona
  concierge:
    persona_id: "unified-concierge"
    agent_type: "concierge"
    name: "Datagenius Concierge"
    description: "Helpful guide for persona recommendations, user assistance, and workflow coordination"
    
    # Capabilities and intents
    capabilities:
      - "persona_recommendation"
      - "intent_analysis"
      - "conversation_management"
      - "data_attachment_assistance"
      - "workflow_coordination"
      - "user_guidance"
      - "business_context_awareness"
    
    supported_intents:
      - "persona_request"
      - "general_inquiry"
      - "data_attachment"
      - "workflow_coordination"
      - "help_request"
      - "greeting"
      - "persona_recommendation"
    
    # Specialized tools
    tools:
      - "persona_recommender"
      - "data_attachment_assistant"
      - "context_manager"
      - "conversation_state_manager"
      - "intent_analyzer"
      - "workflow_coordinator"
    
    # Strategy configuration
    strategy_config:
      enable_smart_routing: true
      enable_context_awareness: true
      enable_proactive_assistance: true
      fallback_persona: true
      
  # Classification Persona
  classification:
    persona_id: "unified-classification"
    agent_type: "classification"
    name: "Classification AI"
    description: "Text and data classification specialist for organizing and categorizing content"
    
    # Capabilities and intents
    capabilities:
      - "text_classification"
      - "data_categorization"
      - "content_organization"
      - "sentiment_analysis"
      - "document_sorting"
      - "entity_extraction"
      - "topic_analysis"
      - "data_labeling"
    
    supported_intents:
      - "text_classification"
      - "data_categorization"
      - "content_organization"
      - "sentiment_analysis"
      - "document_sorting"
      - "entity_extraction"
      - "topic_analysis"
      - "data_labeling"
    
    # Specialized tools
    tools:
      - "text_classifier"
      - "data_categorizer"
      - "sentiment_analyzer"
      - "entity_extractor"
      - "topic_modeler"
      - "document_classifier"
      - "content_organizer"
    
    # Strategy configuration
    strategy_config:
      enable_multi_label_classification: true
      enable_confidence_scoring: true
      enable_active_learning: true
      default_classification_models: ["bert", "roberta", "distilbert"]

# Legacy mappings removed - unified persona system only

# Tool configuration
tool_config:
  # Global tool settings
  global_tools:
    - "conversation_handler"
    - "language_detector"
    - "intent_detector"
    - "business_context_injector"
    
  # Shared tools across personas
  shared_tools:
    - "data_access_tool"
    - "file_processor"
    - "memory_manager"
    - "cross_agent_coordinator"
    
  # Tool execution settings
  execution:
    enable_parallel_execution: true
    max_parallel_tools: 3
    tool_timeout_seconds: 30
    enable_tool_caching: true
    cache_duration_seconds: 1800

# Integration settings
integrations:
  # MCP tool integration
  mcp_tools:
    enable_mcp_integration: true
    mcp_server_url: "http://localhost:8000/mcp"
    enable_third_party_tools: true
    
  # Business profile integration
  business_profile:
    enable_auto_injection: true
    enable_industry_specialization: true
    enable_context_enrichment: true
    
  # Cross-agent intelligence
  cross_agent_intelligence:
    enable_knowledge_sharing: true
    enable_collaborative_learning: true
    enable_insight_propagation: true
    
  # Database integration
  database:
    enable_persona_persistence: true
    enable_conversation_history: true
    enable_performance_tracking: true

# System settings
system:
  # Unified persona system only
  mode: "unified_only"
  legacy_support: false
  extensible_personas: true
    
  # Migration phases
  phases:
    phase1_analysis: "completed"
    phase2_enhanced_ux: "completed"
    phase3_ai_optimization: "completed"
    phase4_marketplace: "completed"
    phase5_migration_completion: "in_progress"

  # System status - migration completed
  system_status:
    unified_system_ready: true
    legacy_support_removed: true
    production_deployed: true
    extensible_personas_enabled: true
