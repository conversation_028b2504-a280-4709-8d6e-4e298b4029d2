#!/usr/bin/env python3
"""
Production Fixes Validation Script.

This script validates all the production-ready fixes implemented
based on the comprehensive code review, including:
- Constants usage instead of hardcoded strings
- Centralized routing utilities
- Comprehensive error handling
- Input validation
- Performance optimizations
"""

import sys
import os
import logging
from typing import Dict, Any

# Add the parent directory to the path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


def test_routing_constants():
    """Test routing constants implementation."""
    logger.info("🧪 Testing routing constants...")
    
    try:
        from core.routing_constants import (
            RoutingConstants, 
            format_agent_node_name, 
            is_concierge_agent,
            ValidationError
        )
        
        # Test constants are defined
        assert hasattr(RoutingConstants, 'AGENT_PREFIX')
        assert hasattr(RoutingConstants, 'CONCIERGE_AGENT_PATTERN')
        assert hasattr(RoutingConstants, 'ROUTING_FALLBACK')
        
        # Test utility functions
        assert format_agent_node_name("marketing") == "agent_marketing"
        assert is_concierge_agent("concierge") is True
        assert is_concierge_agent("marketing") is False
        
        # Test validation error
        try:
            format_agent_node_name("")
            assert False, "Should have raised ValidationError"
        except ValidationError:
            pass
        
        logger.info("✅ Routing constants tests passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Routing constants test failed: {e}")
        return False


def test_routing_utils():
    """Test routing utilities implementation."""
    logger.info("🧪 Testing routing utilities...")
    
    try:
        from core.routing_utils import (
            create_routing_decision_maker,
            RoutingValidator,
            FallbackAgentSelector,
            RoutingDecisionMaker
        )
        from core.routing_constants import RoutingContext, ValidationError
        
        # Mock agent nodes
        agent_nodes = {
            "concierge": "MockConciergeAgent",
            "marketing": "MockMarketingAgent"
        }
        
        # Test routing decision maker creation
        decision_maker = create_routing_decision_maker(agent_nodes)
        assert isinstance(decision_maker, RoutingDecisionMaker)
        
        # Test validator
        validator = RoutingValidator()
        assert validator.validate_state_parameter({"test": "data"}, "test_method") is True
        assert validator.validate_state_parameter(None, "test_method") is False
        assert validator.validate_agent_id("marketing", agent_nodes) is True
        assert validator.validate_agent_id("nonexistent", agent_nodes) is False
        
        # Test fallback selector
        fallback_selector = FallbackAgentSelector(agent_nodes)
        concierge = fallback_selector.get_concierge_agent()
        assert concierge == "concierge"
        
        fallback_agent = fallback_selector.get_fallback_agent(RoutingContext.ENTRY_POINT)
        assert fallback_agent == "agent_concierge"
        
        logger.info("✅ Routing utilities tests passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Routing utilities test failed: {e}")
        return False


def test_error_handling():
    """Test comprehensive error handling."""
    logger.info("🧪 Testing error handling...")
    
    try:
        from core.routing_utils import create_routing_decision_maker
        from core.routing_constants import ValidationError, AgentNotFoundError
        
        # Test with empty agent registry
        empty_decision_maker = create_routing_decision_maker({})
        
        # Test validation errors
        try:
            empty_decision_maker.determine_entry_point(None)
            assert False, "Should have raised ValidationError"
        except ValidationError:
            pass
        
        # Test agent not found errors
        try:
            empty_decision_maker.determine_entry_point({"selected_agent": "nonexistent"})
            assert False, "Should have raised AgentNotFoundError"
        except AgentNotFoundError:
            pass
        
        logger.info("✅ Error handling tests passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error handling test failed: {e}")
        return False


def test_state_validation():
    """Test state validation improvements."""
    logger.info("🧪 Testing state validation...")
    
    try:
        from states.unified_state import get_routing_target, create_unified_state
        
        # Test with valid state
        valid_state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation",
            selected_agent="marketing"
        )
        
        target = get_routing_target(valid_state)
        assert target == "marketing"
        
        # Test with None state
        target = get_routing_target(None)
        assert target is None
        
        # Test with invalid state type
        target = get_routing_target("invalid")
        assert target is None
        
        logger.info("✅ State validation tests passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ State validation test failed: {e}")
        return False


def test_performance_optimizations():
    """Test performance optimizations."""
    logger.info("🧪 Testing performance optimizations...")
    
    try:
        from core.routing_utils import FallbackAgentSelector
        
        # Test agent caching
        agent_nodes = {
            "concierge-agent": "MockConciergeAgent",
            "marketing": "MockMarketingAgent",
            "analysis": "MockAnalysisAgent"
        }
        
        selector = FallbackAgentSelector(agent_nodes)
        
        # First call should cache the concierge agent
        concierge1 = selector.get_concierge_agent()
        assert concierge1 == "concierge-agent"
        
        # Second call should use cached value
        concierge2 = selector.get_concierge_agent()
        assert concierge2 == concierge1
        
        # Test cache refresh
        selector.refresh_cache()
        concierge3 = selector.get_concierge_agent()
        assert concierge3 == "concierge-agent"
        
        logger.info("✅ Performance optimization tests passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Performance optimization test failed: {e}")
        return False


def test_integration_compatibility():
    """Test integration with existing systems."""
    logger.info("🧪 Testing integration compatibility...")
    
    try:
        from states.unified_state import get_routing_target, create_unified_state, ConversationMode
        
        # Test Command pattern compatibility
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation"
        )
        
        # Mock agent command
        class MockCommand:
            def __init__(self, goto):
                self.goto = goto
        
        state["agent_command"] = MockCommand("analysis")
        target = get_routing_target(state)
        assert target == "analysis"
        
        # Test user selection overrides command
        state["selected_agent"] = "marketing"
        target = get_routing_target(state)
        assert target == "marketing"  # Should prioritize user selection
        
        logger.info("✅ Integration compatibility tests passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Integration compatibility test failed: {e}")
        return False


def main():
    """Run all production fixes validation tests."""
    logger.info("🚀 Starting production fixes validation...")
    
    tests = [
        test_routing_constants,
        test_routing_utils,
        test_error_handling,
        test_state_validation,
        test_performance_optimizations,
        test_integration_compatibility
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
                logger.error(f"❌ Test failed: {test.__name__}")
        except Exception as e:
            failed += 1
            logger.error(f"❌ Test error in {test.__name__}: {e}")
    
    logger.info(f"📊 Validation Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        logger.info("🎉 All production fixes validated successfully!")
        logger.info("✅ Code is ready for production deployment")
        return True
    else:
        logger.error("💥 Some validation tests failed!")
        logger.error("❌ Additional fixes needed before production")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
