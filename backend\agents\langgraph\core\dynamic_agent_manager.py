"""
Dynamic Agent Manager for LangGraph-based Datagenius System.

This module provides dynamic agent management capabilities including:
- Runtime agent discovery and registration
- Configuration-based agent loading
- Hot-swapping of agent implementations
- Agent health monitoring and management
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Set
from datetime import datetime
from pathlib import Path
import yaml
import importlib
import inspect
from dataclasses import dataclass

from ..nodes.base_agent_node import BaseAgentNode
from .agent_factory import agent_factory

logger = logging.getLogger(__name__)


@dataclass
class AgentStatus:
    """Status information for an agent."""
    agent_id: str
    status: str  # 'active', 'inactive', 'error', 'loading'
    last_updated: datetime
    error_message: Optional[str] = None
    load_time: Optional[float] = None


class DynamicAgentManager:
    """
    Manages dynamic loading, unloading, and configuration of agents.
    
    This manager provides runtime agent management capabilities that allow
    the system to adapt to changing agent configurations without restarts.
    """
    
    def __init__(self, workflow_manager=None):
        """
        Initialize the dynamic agent manager.
        
        Args:
            workflow_manager: Reference to the workflow manager for agent registration
        """
        self.logger = logging.getLogger(__name__)
        self.workflow_manager = workflow_manager
        
        # Agent status tracking
        self.agent_status: Dict[str, AgentStatus] = {}
        
        # Configuration monitoring
        self.config_paths: Set[Path] = set()
        self.last_config_check = datetime.now()
        
        # Agent discovery settings
        self.discovery_enabled = True
        self.discovery_paths = [
            "agents.langgraph.agents",
            "agents.custom"
        ]
        
        self.logger.info("DynamicAgentManager initialized")
    
    async def discover_and_load_agents(self) -> Dict[str, Any]:
        """
        Discover and load all available agents dynamically.
        
        Returns:
            Dictionary with discovery results and statistics
        """
        start_time = datetime.now()
        results = {
            "discovered": 0,
            "loaded": 0,
            "failed": 0,
            "agents": [],
            "errors": []
        }
        
        try:
            self.logger.info("🔍 Starting dynamic agent discovery")
            
            # Get agents from factory
            available_agents = agent_factory.get_available_agents()
            results["discovered"] = len(available_agents)
            
            for agent_id in available_agents:
                try:
                    await self._load_agent(agent_id)
                    results["loaded"] += 1
                    results["agents"].append(agent_id)
                    
                except Exception as e:
                    results["failed"] += 1
                    error_msg = f"Failed to load {agent_id}: {str(e)}"
                    results["errors"].append(error_msg)
                    self.logger.error(error_msg)
            
            # Update discovery time
            discovery_time = (datetime.now() - start_time).total_seconds()
            
            self.logger.info(
                f"🎯 Agent discovery complete: "
                f"{results['loaded']}/{results['discovered']} agents loaded "
                f"in {discovery_time:.2f}s"
            )
            
            results["discovery_time"] = discovery_time
            return results
            
        except Exception as e:
            self.logger.error(f"❌ Error in agent discovery: {e}")
            results["errors"].append(str(e))
            return results
    
    async def _load_agent(self, agent_id: str) -> bool:
        """
        Load a specific agent.
        
        Args:
            agent_id: Agent identifier to load
            
        Returns:
            True if loaded successfully, False otherwise
        """
        start_time = datetime.now()
        
        try:
            self.logger.debug(f"Loading agent: {agent_id}")
            
            # Update status to loading
            self.agent_status[agent_id] = AgentStatus(
                agent_id=agent_id,
                status="loading",
                last_updated=datetime.now()
            )
            
            # Create agent node using factory
            agent_node = agent_factory.create_agent_node(agent_id)
            
            if not agent_node:
                raise ValueError(f"Factory returned None for agent {agent_id}")
            
            # Register with workflow manager if available
            if self.workflow_manager:
                self.workflow_manager.register_agent_node(agent_id, agent_node)
            
            # Update status to active
            load_time = (datetime.now() - start_time).total_seconds()
            self.agent_status[agent_id] = AgentStatus(
                agent_id=agent_id,
                status="active",
                last_updated=datetime.now(),
                load_time=load_time
            )
            
            self.logger.info(f"✅ Loaded agent {agent_id} in {load_time:.3f}s")
            return True
            
        except Exception as e:
            # Update status to error
            self.agent_status[agent_id] = AgentStatus(
                agent_id=agent_id,
                status="error",
                last_updated=datetime.now(),
                error_message=str(e)
            )
            
            self.logger.error(f"❌ Failed to load agent {agent_id}: {e}")
            return False
    
    async def unload_agent(self, agent_id: str) -> bool:
        """
        Unload a specific agent.
        
        Args:
            agent_id: Agent identifier to unload
            
        Returns:
            True if unloaded successfully, False otherwise
        """
        try:
            self.logger.info(f"🗑️ Unloading agent: {agent_id}")
            
            # Unregister from workflow manager
            if self.workflow_manager:
                self.workflow_manager.unregister_agent_node(agent_id)
            
            # Update status to inactive
            self.agent_status[agent_id] = AgentStatus(
                agent_id=agent_id,
                status="inactive",
                last_updated=datetime.now()
            )
            
            self.logger.info(f"✅ Unloaded agent: {agent_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to unload agent {agent_id}: {e}")
            return False
    
    async def reload_agent(self, agent_id: str) -> bool:
        """
        Reload a specific agent (unload then load).
        
        Args:
            agent_id: Agent identifier to reload
            
        Returns:
            True if reloaded successfully, False otherwise
        """
        self.logger.info(f"🔄 Reloading agent: {agent_id}")
        
        # Unload first
        await self.unload_agent(agent_id)
        
        # Then load
        return await self._load_agent(agent_id)
    
    def get_agent_status(self, agent_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get status information for agents.
        
        Args:
            agent_id: Specific agent ID, or None for all agents
            
        Returns:
            Status information dictionary
        """
        if agent_id:
            if agent_id in self.agent_status:
                status = self.agent_status[agent_id]
                return {
                    "agent_id": status.agent_id,
                    "status": status.status,
                    "last_updated": status.last_updated.isoformat(),
                    "error_message": status.error_message,
                    "load_time": status.load_time
                }
            else:
                return {"error": f"Agent {agent_id} not found"}
        else:
            return {
                "agents": {
                    agent_id: {
                        "status": status.status,
                        "last_updated": status.last_updated.isoformat(),
                        "error_message": status.error_message,
                        "load_time": status.load_time
                    }
                    for agent_id, status in self.agent_status.items()
                },
                "summary": {
                    "total": len(self.agent_status),
                    "active": len([s for s in self.agent_status.values() if s.status == "active"]),
                    "inactive": len([s for s in self.agent_status.values() if s.status == "inactive"]),
                    "error": len([s for s in self.agent_status.values() if s.status == "error"]),
                    "loading": len([s for s in self.agent_status.values() if s.status == "loading"])
                }
            }
    
    async def refresh_all_agents(self) -> Dict[str, Any]:
        """
        Refresh all agents by re-discovering and reloading.
        
        Returns:
            Refresh results dictionary
        """
        self.logger.info("🔄 Refreshing all agents")
        
        # Clear current status
        self.agent_status.clear()
        
        # Refresh workflow manager agents
        if self.workflow_manager:
            self.workflow_manager.refresh_agents()
        
        # Re-discover and load
        return await self.discover_and_load_agents()


# Global instance
dynamic_agent_manager = DynamicAgentManager()
