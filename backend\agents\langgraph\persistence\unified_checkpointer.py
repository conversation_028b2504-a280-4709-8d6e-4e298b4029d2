"""
Unified Checkpointer for LangGraph-based Datagenius System.

This module provides state persistence capabilities that integrate
with the existing database system while providing LangGraph-compatible
checkpointing functionality.
"""

import logging
import json
import pickle
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
import uuid

from sqlalchemy.orm import Session
from sqlalchemy import create_engine, text

from ..states.unified_state import UnifiedDatageniusState
from app.database import get_db

logger = logging.getLogger(__name__)


class UnifiedCheckpointer:
    """
    Unified checkpointer that provides state persistence for LangGraph workflows.
    
    This checkpointer integrates with the existing Datagenius database
    while providing LangGraph-compatible state management.
    """
    
    def __init__(self, db_session: Optional[Session] = None):
        """
        Initialize the unified checkpointer.

        Args:
            db_session: Optional database session
        """
        self.db_session = db_session
        self.logger = logging.getLogger(__name__)

        # Enhanced state cache for performance
        self.state_cache: Dict[str, UnifiedDatageniusState] = {}
        self.cache_ttl = 300  # 5 minutes
        self.cache_timestamps: Dict[str, datetime] = {}
        self.cache_hits = 0
        self.cache_misses = 0

        # Connection pool for better performance
        self._connection_pool = None
        self._pool_initialized = False

        self.logger.info("UnifiedCheckpointer initialized with enhanced caching")

    def _is_cache_valid(self, workflow_id: str) -> bool:
        """Check if cached state is still valid."""
        if workflow_id not in self.cache_timestamps:
            return False

        cache_time = self.cache_timestamps[workflow_id]
        return (datetime.now() - cache_time).total_seconds() < self.cache_ttl

    def _get_from_cache(self, workflow_id: str) -> Optional[UnifiedDatageniusState]:
        """Get state from cache if valid."""
        if workflow_id in self.state_cache and self._is_cache_valid(workflow_id):
            self.cache_hits += 1
            return self.state_cache[workflow_id]

        self.cache_misses += 1
        return None

    def _store_in_cache(self, workflow_id: str, state: UnifiedDatageniusState) -> None:
        """Store state in cache with timestamp."""
        self.state_cache[workflow_id] = state
        self.cache_timestamps[workflow_id] = datetime.now()

        # Cleanup old cache entries if cache is getting large
        if len(self.state_cache) > 1000:
            self._cleanup_cache()

    def _cleanup_cache(self) -> None:
        """Remove expired cache entries."""
        current_time = datetime.now()
        expired_keys = [
            key for key, timestamp in self.cache_timestamps.items()
            if (current_time - timestamp).total_seconds() > self.cache_ttl
        ]

        for key in expired_keys:
            self.state_cache.pop(key, None)
            self.cache_timestamps.pop(key, None)

        self.logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
    
    def _get_db_session(self) -> Session:
        """Get database session."""
        if self.db_session:
            return self.db_session
        return next(get_db())
    
    async def save_state(
        self,
        workflow_id: str,
        state: UnifiedDatageniusState,
        checkpoint_id: Optional[str] = None
    ) -> str:
        """
        Save workflow state to database.
        
        Args:
            workflow_id: Workflow identifier
            state: State to save
            checkpoint_id: Optional checkpoint identifier
            
        Returns:
            Checkpoint ID
        """
        try:
            if not checkpoint_id:
                checkpoint_id = str(uuid.uuid4())
            
            db = self._get_db_session()
            
            # Serialize state
            serialized_state = self._serialize_state(state)
            
            # Save to database
            query = text("""
                INSERT INTO langgraph_workflow_checkpoints 
                (id, workflow_id, checkpoint_id, state_data, created_at, updated_at)
                VALUES (:id, :workflow_id, :checkpoint_id, :state_data, :created_at, :updated_at)
                ON CONFLICT (workflow_id, checkpoint_id) 
                DO UPDATE SET 
                    state_data = EXCLUDED.state_data,
                    updated_at = EXCLUDED.updated_at
            """)
            
            db.execute(query, {
                "id": str(uuid.uuid4()),
                "workflow_id": workflow_id,
                "checkpoint_id": checkpoint_id,
                "state_data": serialized_state,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            })
            
            db.commit()
            
            # Update cache
            self.state_cache[workflow_id] = state
            self.cache_timestamps[workflow_id] = datetime.now()
            
            self.logger.debug(f"Saved state for workflow {workflow_id}, checkpoint {checkpoint_id}")
            
            return checkpoint_id
            
        except Exception as e:
            self.logger.error(f"Error saving state for workflow {workflow_id}: {e}", exc_info=True)
            if db:
                db.rollback()
            raise
    
    async def get_state(
        self,
        workflow_id: str,
        checkpoint_id: Optional[str] = None
    ) -> Optional[UnifiedDatageniusState]:
        """
        Get workflow state from database.
        
        Args:
            workflow_id: Workflow identifier
            checkpoint_id: Optional specific checkpoint to retrieve
            
        Returns:
            Workflow state or None if not found
        """
        try:
            # Check cache first
            if workflow_id in self.state_cache and self._is_cache_valid(workflow_id):
                self.logger.debug(f"Retrieved state for workflow {workflow_id} from cache")
                return self.state_cache[workflow_id]
            
            db = self._get_db_session()
            
            if checkpoint_id:
                # Get specific checkpoint
                query = text("""
                    SELECT state_data FROM langgraph_workflow_checkpoints
                    WHERE workflow_id = :workflow_id AND checkpoint_id = :checkpoint_id
                    ORDER BY created_at DESC LIMIT 1
                """)
                result = db.execute(query, {
                    "workflow_id": workflow_id,
                    "checkpoint_id": checkpoint_id
                }).fetchone()
            else:
                # Get latest checkpoint
                query = text("""
                    SELECT state_data FROM langgraph_workflow_checkpoints
                    WHERE workflow_id = :workflow_id
                    ORDER BY created_at DESC LIMIT 1
                """)
                result = db.execute(query, {"workflow_id": workflow_id}).fetchone()
            
            if not result:
                self.logger.debug(f"No state found for workflow {workflow_id}")
                return None
            
            # Deserialize state
            state = self._deserialize_state(result[0])
            
            # Update cache
            self.state_cache[workflow_id] = state
            self.cache_timestamps[workflow_id] = datetime.now()
            
            self.logger.debug(f"Retrieved state for workflow {workflow_id} from database")
            
            return state
            
        except Exception as e:
            self.logger.error(f"Error getting state for workflow {workflow_id}: {e}", exc_info=True)
            return None
    
    async def list_checkpoints(
        self,
        workflow_id: str,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        List checkpoints for a workflow.
        
        Args:
            workflow_id: Workflow identifier
            limit: Maximum number of checkpoints to return
            
        Returns:
            List of checkpoint information
        """
        try:
            db = self._get_db_session()
            
            query = text("""
                SELECT checkpoint_id, created_at, updated_at
                FROM langgraph_workflow_checkpoints
                WHERE workflow_id = :workflow_id
                ORDER BY created_at DESC
                LIMIT :limit
            """)
            
            results = db.execute(query, {
                "workflow_id": workflow_id,
                "limit": limit
            }).fetchall()
            
            checkpoints = []
            for result in results:
                checkpoints.append({
                    "checkpoint_id": result[0],
                    "created_at": result[1].isoformat(),
                    "updated_at": result[2].isoformat()
                })
            
            return checkpoints
            
        except Exception as e:
            self.logger.error(f"Error listing checkpoints for workflow {workflow_id}: {e}")
            return []
    
    async def delete_workflow_checkpoints(self, workflow_id: str) -> bool:
        """
        Delete all checkpoints for a workflow.
        
        Args:
            workflow_id: Workflow identifier
            
        Returns:
            True if successful, False otherwise
        """
        try:
            db = self._get_db_session()
            
            query = text("""
                DELETE FROM langgraph_workflow_checkpoints
                WHERE workflow_id = :workflow_id
            """)
            
            db.execute(query, {"workflow_id": workflow_id})
            db.commit()
            
            # Remove from cache
            if workflow_id in self.state_cache:
                del self.state_cache[workflow_id]
            if workflow_id in self.cache_timestamps:
                del self.cache_timestamps[workflow_id]
            
            self.logger.info(f"Deleted checkpoints for workflow {workflow_id}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting checkpoints for workflow {workflow_id}: {e}")
            if db:
                db.rollback()
            return False
    
    def _serialize_state(self, state: UnifiedDatageniusState) -> str:
        """
        Serialize state for database storage.

        Args:
            state: State to serialize

        Returns:
            Serialized state string
        """
        try:
            # Convert sets to lists and remove non-serializable objects for JSON serialization
            serializable_state = {}
            for key, value in state.items():
                if isinstance(value, set):
                    serializable_state[key] = list(value)
                elif callable(value):
                    # Skip function objects that can't be serialized
                    self.logger.debug(f"Skipping non-serializable function: {key}")
                    continue
                elif key == "stream_callback":
                    # Specifically skip stream_callback functions
                    self.logger.debug("Skipping stream_callback function from serialization")
                    continue
                else:
                    serializable_state[key] = value

            return json.dumps(serializable_state, default=str)

        except Exception as e:
            self.logger.error(f"Error serializing state: {e}")
            # Fallback to pickle for complex objects
            return pickle.dumps(state).hex()
    
    def _deserialize_state(self, serialized_state: str) -> UnifiedDatageniusState:
        """
        Deserialize state from database storage.
        
        Args:
            serialized_state: Serialized state string
            
        Returns:
            Deserialized state
        """
        try:
            # Try JSON first
            state_dict = json.loads(serialized_state)
            
            # Convert lists back to sets where appropriate
            set_fields = ["active_agents"]
            for field in set_fields:
                if field in state_dict and isinstance(state_dict[field], list):
                    state_dict[field] = set(state_dict[field])
            
            return state_dict
            
        except json.JSONDecodeError:
            # Fallback to pickle
            try:
                return pickle.loads(bytes.fromhex(serialized_state))
            except Exception as e:
                self.logger.error(f"Error deserializing state: {e}")
                raise
    
    def _is_cache_valid(self, workflow_id: str) -> bool:
        """
        Check if cached state is still valid.
        
        Args:
            workflow_id: Workflow identifier
            
        Returns:
            True if cache is valid, False otherwise
        """
        if workflow_id not in self.cache_timestamps:
            return False
        
        cache_age = (datetime.now() - self.cache_timestamps[workflow_id]).total_seconds()
        return cache_age < self.cache_ttl
    
    def clear_cache(self) -> None:
        """Clear the state cache."""
        self.state_cache.clear()
        self.cache_timestamps.clear()
        self.logger.info("Cleared state cache")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.
        
        Returns:
            Cache statistics
        """
        return {
            "cached_workflows": len(self.state_cache),
            "cache_ttl": self.cache_ttl,
            "oldest_cache_entry": min(self.cache_timestamps.values()) if self.cache_timestamps else None,
            "newest_cache_entry": max(self.cache_timestamps.values()) if self.cache_timestamps else None
        }
