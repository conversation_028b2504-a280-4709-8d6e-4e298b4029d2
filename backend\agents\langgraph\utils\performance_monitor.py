"""
Performance Monitoring Utilities for LangGraph Agents

This module provides performance monitoring, metrics collection, and
resource tracking for the agent system.
"""

import time
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict, deque
import threading
from contextlib import contextmanager


@dataclass
class PerformanceMetric:
    """Individual performance metric data."""
    name: str
    value: float
    timestamp: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AgentPerformanceStats:
    """Performance statistics for an agent."""
    agent_id: str
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_response_time: float = 0.0
    min_response_time: float = float('inf')
    max_response_time: float = 0.0
    last_activity: Optional[datetime] = None
    coordination_count: int = 0
    coordination_success_rate: float = 0.0


class PerformanceMonitor:
    """
    Performance monitoring system for LangGraph agents.
    
    Tracks metrics like response times, success rates, coordination performance,
    and resource usage across all agents in the system.
    """
    
    def __init__(self, max_metrics_history: int = 1000):
        """
        Initialize performance monitor.
        
        Args:
            max_metrics_history: Maximum number of metrics to keep in memory
        """
        self.max_metrics_history = max_metrics_history
        self.metrics_history: deque = deque(maxlen=max_metrics_history)
        self.agent_stats: Dict[str, AgentPerformanceStats] = {}
        self.active_operations: Dict[str, float] = {}  # operation_id -> start_time
        self.logger = logging.getLogger(__name__)
        self._lock = threading.Lock()
        
        # Aggregated metrics
        self.hourly_metrics: Dict[str, List[float]] = defaultdict(list)
        self.daily_metrics: Dict[str, List[float]] = defaultdict(list)
        
    def record_metric(
        self, 
        name: str, 
        value: float, 
        agent_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Record a performance metric.
        
        Args:
            name: Metric name
            value: Metric value
            agent_id: Optional agent ID associated with the metric
            metadata: Optional additional metadata
        """
        with self._lock:
            metric = PerformanceMetric(
                name=name,
                value=value,
                timestamp=datetime.now(),
                metadata=metadata or {}
            )
            
            if agent_id:
                metric.metadata['agent_id'] = agent_id
            
            self.metrics_history.append(metric)
            
            # Update aggregated metrics
            current_hour = datetime.now().replace(minute=0, second=0, microsecond=0)
            self.hourly_metrics[f"{name}_{current_hour.isoformat()}"].append(value)
            
            current_day = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            self.daily_metrics[f"{name}_{current_day.isoformat()}"].append(value)
    
    @contextmanager
    def measure_operation(self, operation_name: str, agent_id: Optional[str] = None):
        """
        Context manager to measure operation duration.
        
        Args:
            operation_name: Name of the operation being measured
            agent_id: Optional agent ID
        """
        operation_id = f"{operation_name}_{id(threading.current_thread())}_{time.time()}"
        start_time = time.time()
        
        try:
            with self._lock:
                self.active_operations[operation_id] = start_time
            yield
        finally:
            end_time = time.time()
            duration = end_time - start_time
            
            with self._lock:
                self.active_operations.pop(operation_id, None)
            
            self.record_metric(
                f"{operation_name}_duration",
                duration,
                agent_id=agent_id,
                metadata={"operation_type": "duration"}
            )
    
    def update_agent_stats(
        self, 
        agent_id: str, 
        success: bool, 
        response_time: float,
        operation_type: str = "request"
    ) -> None:
        """
        Update performance statistics for an agent.
        
        Args:
            agent_id: Agent identifier
            success: Whether the operation was successful
            response_time: Time taken for the operation
            operation_type: Type of operation (request, coordination, etc.)
        """
        with self._lock:
            if agent_id not in self.agent_stats:
                self.agent_stats[agent_id] = AgentPerformanceStats(agent_id=agent_id)
            
            stats = self.agent_stats[agent_id]
            stats.last_activity = datetime.now()
            
            if operation_type == "request":
                stats.total_requests += 1
                if success:
                    stats.successful_requests += 1
                else:
                    stats.failed_requests += 1
                
                # Update response time statistics
                if response_time < stats.min_response_time:
                    stats.min_response_time = response_time
                if response_time > stats.max_response_time:
                    stats.max_response_time = response_time
                
                # Calculate rolling average
                total_time = stats.average_response_time * (stats.total_requests - 1) + response_time
                stats.average_response_time = total_time / stats.total_requests
                
            elif operation_type == "coordination":
                stats.coordination_count += 1
                if success:
                    # Update coordination success rate
                    success_count = stats.coordination_success_rate * (stats.coordination_count - 1)
                    if success:
                        success_count += 1
                    stats.coordination_success_rate = success_count / stats.coordination_count
    
    def get_agent_stats(self, agent_id: str) -> Optional[AgentPerformanceStats]:
        """Get performance statistics for a specific agent."""
        with self._lock:
            return self.agent_stats.get(agent_id)
    
    def get_all_agent_stats(self) -> Dict[str, AgentPerformanceStats]:
        """Get performance statistics for all agents."""
        with self._lock:
            return self.agent_stats.copy()
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """Get overall system performance metrics."""
        with self._lock:
            total_requests = sum(stats.total_requests for stats in self.agent_stats.values())
            total_successful = sum(stats.successful_requests for stats in self.agent_stats.values())
            total_failed = sum(stats.failed_requests for stats in self.agent_stats.values())
            
            active_agents = len([
                stats for stats in self.agent_stats.values()
                if stats.last_activity and 
                (datetime.now() - stats.last_activity).total_seconds() < 300  # Active in last 5 minutes
            ])
            
            avg_response_time = (
                sum(stats.average_response_time for stats in self.agent_stats.values()) / 
                len(self.agent_stats) if self.agent_stats else 0
            )
            
            return {
                "total_requests": total_requests,
                "successful_requests": total_successful,
                "failed_requests": total_failed,
                "success_rate": total_successful / total_requests if total_requests > 0 else 0,
                "active_agents": active_agents,
                "total_agents": len(self.agent_stats),
                "average_response_time": avg_response_time,
                "active_operations": len(self.active_operations),
                "metrics_in_memory": len(self.metrics_history)
            }
    
    def get_recent_metrics(self, minutes: int = 60) -> List[PerformanceMetric]:
        """Get metrics from the last N minutes."""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        with self._lock:
            return [
                metric for metric in self.metrics_history
                if metric.timestamp >= cutoff_time
            ]
    
    def cleanup_old_metrics(self, hours: int = 24) -> None:
        """Clean up metrics older than specified hours."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        with self._lock:
            # Clean up hourly metrics
            old_hourly_keys = [
                key for key in self.hourly_metrics.keys()
                if datetime.fromisoformat(key.split('_', 1)[1]) < cutoff_time
            ]
            for key in old_hourly_keys:
                del self.hourly_metrics[key]
            
            # Clean up daily metrics (keep longer)
            daily_cutoff = datetime.now() - timedelta(days=30)
            old_daily_keys = [
                key for key in self.daily_metrics.keys()
                if datetime.fromisoformat(key.split('_', 1)[1]) < daily_cutoff
            ]
            for key in old_daily_keys:
                del self.daily_metrics[key]
    
    def export_metrics(self) -> Dict[str, Any]:
        """Export all metrics for external monitoring systems."""
        with self._lock:
            return {
                "timestamp": datetime.now().isoformat(),
                "system_metrics": self.get_system_metrics(),
                "agent_stats": {
                    agent_id: {
                        "total_requests": stats.total_requests,
                        "successful_requests": stats.successful_requests,
                        "failed_requests": stats.failed_requests,
                        "average_response_time": stats.average_response_time,
                        "min_response_time": stats.min_response_time if stats.min_response_time != float('inf') else 0,
                        "max_response_time": stats.max_response_time,
                        "coordination_count": stats.coordination_count,
                        "coordination_success_rate": stats.coordination_success_rate,
                        "last_activity": stats.last_activity.isoformat() if stats.last_activity else None
                    }
                    for agent_id, stats in self.agent_stats.items()
                },
                "recent_metrics_count": len(self.get_recent_metrics(60)),
                "active_operations": len(self.active_operations)
            }


# Global performance monitor instance
performance_monitor = PerformanceMonitor()
