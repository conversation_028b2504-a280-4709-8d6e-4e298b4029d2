"""
MCP Monitoring and Analytics Service for Datagenius.

This service provides comprehensive monitoring, analytics, and performance
tracking for MCP server operations and tool usage.
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Tuple
from collections import defaultdict
from sqlalchemy.orm import Session
from sqlalchemy import func, desc

from ..database import MCPServer, MCPTool, MCPResource, MCPPrompt
from ..models.mcp_server import MCPServerStatus
from ..utils.performance import perf_monitor, performance_monitor

logger = logging.getLogger(__name__)


class MCPMonitoringService:
    """Service for monitoring and analytics of MCP operations."""
    
    def __init__(self):
        """Initialize the monitoring service."""
        self.logger = logging.getLogger(__name__)
        self.metrics_cache: Dict[str, Any] = {}
        self.cache_ttl = 300  # 5 minutes
        self.last_cache_update = datetime.now(timezone.utc)
    
    @performance_monitor("get_system_overview")
    async def get_system_overview(self, db: Session, user_id: int) -> Dict[str, Any]:
        """
        Get comprehensive system overview for a user.
        
        Args:
            db: Database session
            user_id: User ID
            
        Returns:
            System overview dictionary
        """
        try:
            # Check cache first
            cache_key = f"system_overview_{user_id}"
            if self._is_cache_valid(cache_key):
                return self.metrics_cache[cache_key]
            
            # Get server statistics
            servers = db.query(MCPServer).filter(MCPServer.user_id == user_id).all()
            
            total_servers = len(servers)
            active_servers = len([s for s in servers if s.status == MCPServerStatus.ACTIVE])
            error_servers = len([s for s in servers if s.status == MCPServerStatus.ERROR])
            
            # Get tool statistics
            total_tools = db.query(MCPTool).join(MCPServer).filter(
                MCPServer.user_id == user_id
            ).count()
            
            enabled_tools = db.query(MCPTool).join(MCPServer).filter(
                MCPServer.user_id == user_id,
                MCPTool.is_enabled == True
            ).count()
            
            # Get usage statistics
            total_executions = db.query(func.sum(MCPTool.usage_count)).join(MCPServer).filter(
                MCPServer.user_id == user_id
            ).scalar() or 0
            
            # Get recent activity
            recent_activity = await self._get_recent_activity(db, user_id, limit=10)
            
            # Calculate health score
            health_score = self._calculate_health_score(
                total_servers, active_servers, error_servers, enabled_tools, total_tools
            )
            
            overview = {
                "summary": {
                    "total_servers": total_servers,
                    "active_servers": active_servers,
                    "error_servers": error_servers,
                    "total_tools": total_tools,
                    "enabled_tools": enabled_tools,
                    "total_executions": total_executions,
                    "health_score": health_score
                },
                "server_status_distribution": {
                    "active": active_servers,
                    "inactive": total_servers - active_servers - error_servers,
                    "error": error_servers
                },
                "recent_activity": recent_activity,
                "performance_metrics": await self._get_performance_metrics(),
                "alerts": await self._get_system_alerts(db, user_id)
            }
            
            # Cache the result
            self.metrics_cache[cache_key] = overview
            
            return overview
            
        except Exception as e:
            self.logger.error(f"Error getting system overview for user {user_id}: {e}")
            return {
                "summary": {},
                "server_status_distribution": {},
                "recent_activity": [],
                "performance_metrics": {},
                "alerts": []
            }
    
    @performance_monitor("get_server_analytics")
    async def get_server_analytics(
        self, 
        db: Session, 
        server_id: str, 
        user_id: int,
        time_range: str = "7d"
    ) -> Dict[str, Any]:
        """
        Get detailed analytics for a specific server.
        
        Args:
            db: Database session
            server_id: Server ID
            user_id: User ID for authorization
            time_range: Time range for analytics (1h, 24h, 7d, 30d)
            
        Returns:
            Server analytics dictionary
        """
        try:
            # Verify server ownership
            server = db.query(MCPServer).filter(
                MCPServer.id == server_id,
                MCPServer.user_id == user_id
            ).first()
            
            if not server:
                return {"error": "Server not found or access denied"}
            
            # Get time range
            end_time = datetime.now(timezone.utc)
            start_time = self._get_start_time(time_range, end_time)
            
            # Get tools for this server
            tools = db.query(MCPTool).filter(MCPTool.server_id == server_id).all()
            
            # Calculate tool usage statistics
            tool_stats = []
            for tool in tools:
                tool_stats.append({
                    "name": tool.tool_name,
                    "usage_count": tool.usage_count,
                    "last_used": tool.last_used_at.isoformat() if tool.last_used_at else None,
                    "enabled": tool.is_enabled
                })
            
            # Sort by usage count
            tool_stats.sort(key=lambda x: x["usage_count"], reverse=True)
            
            # Get resources and prompts
            resources = db.query(MCPResource).filter(MCPResource.server_id == server_id).all()
            prompts = db.query(MCPPrompt).filter(MCPPrompt.server_id == server_id).all()
            
            analytics = {
                "server_info": {
                    "id": server.id,
                    "name": server.name,
                    "status": server.status,
                    "transport_type": server.transport_type,
                    "created_at": server.created_at.isoformat(),
                    "last_connected_at": server.last_connected_at.isoformat() if server.last_connected_at else None
                },
                "capabilities": {
                    "tools": len(tools),
                    "resources": len(resources),
                    "prompts": len(prompts)
                },
                "tool_usage": {
                    "total_executions": sum(tool.usage_count for tool in tools),
                    "active_tools": len([t for t in tools if t.is_enabled]),
                    "most_used_tools": tool_stats[:10],
                    "usage_distribution": self._calculate_usage_distribution(tool_stats)
                },
                "performance": {
                    "uptime_percentage": self._calculate_uptime(server, start_time, end_time),
                    "average_response_time": self._get_average_response_time(server_id),
                    "error_rate": self._calculate_error_rate(server_id, start_time, end_time)
                },
                "time_range": {
                    "start": start_time.isoformat(),
                    "end": end_time.isoformat(),
                    "range": time_range
                }
            }
            
            return analytics
            
        except Exception as e:
            self.logger.error(f"Error getting server analytics for {server_id}: {e}")
            return {"error": f"Failed to get server analytics: {str(e)}"}
    
    @performance_monitor("get_tool_analytics")
    async def get_tool_analytics(
        self, 
        db: Session, 
        user_id: int,
        time_range: str = "7d"
    ) -> Dict[str, Any]:
        """
        Get tool usage analytics across all servers.
        
        Args:
            db: Database session
            user_id: User ID
            time_range: Time range for analytics
            
        Returns:
            Tool analytics dictionary
        """
        try:
            # Get all tools for user's servers
            tools = db.query(MCPTool).join(MCPServer).filter(
                MCPServer.user_id == user_id
            ).all()
            
            # Calculate analytics
            total_tools = len(tools)
            enabled_tools = len([t for t in tools if t.is_enabled])
            total_executions = sum(tool.usage_count for tool in tools)
            
            # Group by server
            server_tool_counts = defaultdict(int)
            server_execution_counts = defaultdict(int)
            
            for tool in tools:
                server_name = tool.server.name if hasattr(tool, 'server') else 'Unknown'
                server_tool_counts[server_name] += 1
                server_execution_counts[server_name] += tool.usage_count
            
            # Most popular tools
            popular_tools = sorted(
                [
                    {
                        "name": tool.tool_name,
                        "server_name": tool.server.name if hasattr(tool, 'server') else 'Unknown',
                        "usage_count": tool.usage_count,
                        "last_used": tool.last_used_at.isoformat() if tool.last_used_at else None
                    }
                    for tool in tools
                ],
                key=lambda x: x["usage_count"],
                reverse=True
            )[:20]
            
            # Usage trends (would need historical data for real trends)
            usage_trends = self._generate_usage_trends(tools, time_range)
            
            analytics = {
                "summary": {
                    "total_tools": total_tools,
                    "enabled_tools": enabled_tools,
                    "total_executions": total_executions,
                    "average_executions_per_tool": total_executions / total_tools if total_tools > 0 else 0
                },
                "distribution": {
                    "by_server": dict(server_tool_counts),
                    "executions_by_server": dict(server_execution_counts)
                },
                "popular_tools": popular_tools,
                "usage_trends": usage_trends,
                "categories": self._categorize_tools(tools)
            }
            
            return analytics
            
        except Exception as e:
            self.logger.error(f"Error getting tool analytics for user {user_id}: {e}")
            return {"error": f"Failed to get tool analytics: {str(e)}"}
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get system performance metrics."""
        try:
            return perf_monitor.get_stats()
        except Exception as e:
            self.logger.error(f"Error getting performance metrics: {e}")
            return {}
    
    async def get_health_check(self, db: Session, user_id: int) -> Dict[str, Any]:
        """
        Perform comprehensive health check.
        
        Args:
            db: Database session
            user_id: User ID
            
        Returns:
            Health check results
        """
        try:
            health_issues = []
            recommendations = []
            
            # Check server statuses
            servers = db.query(MCPServer).filter(MCPServer.user_id == user_id).all()
            error_servers = [s for s in servers if s.status == MCPServerStatus.ERROR]
            
            if error_servers:
                health_issues.append({
                    "type": "error_servers",
                    "severity": "high",
                    "message": f"{len(error_servers)} servers have errors",
                    "servers": [s.name for s in error_servers]
                })
                recommendations.append("Check server configurations and connection settings")
            
            # Check for unused tools
            unused_tools = db.query(MCPTool).join(MCPServer).filter(
                MCPServer.user_id == user_id,
                MCPTool.usage_count == 0,
                MCPTool.is_enabled == True
            ).count()
            
            if unused_tools > 0:
                health_issues.append({
                    "type": "unused_tools",
                    "severity": "low",
                    "message": f"{unused_tools} enabled tools have never been used",
                    "count": unused_tools
                })
                recommendations.append("Consider disabling unused tools to improve performance")
            
            # Check for old connections
            stale_threshold = datetime.now(timezone.utc) - timedelta(days=7)
            stale_servers = [
                s for s in servers 
                if s.last_connected_at and s.last_connected_at < stale_threshold
            ]
            
            if stale_servers:
                health_issues.append({
                    "type": "stale_connections",
                    "severity": "medium",
                    "message": f"{len(stale_servers)} servers haven't connected recently",
                    "servers": [s.name for s in stale_servers]
                })
                recommendations.append("Test connections for servers that haven't been active recently")
            
            # Calculate overall health score
            health_score = self._calculate_health_score(
                len(servers),
                len([s for s in servers if s.status == MCPServerStatus.ACTIVE]),
                len(error_servers),
                db.query(MCPTool).join(MCPServer).filter(
                    MCPServer.user_id == user_id,
                    MCPTool.is_enabled == True
                ).count(),
                db.query(MCPTool).join(MCPServer).filter(
                    MCPServer.user_id == user_id
                ).count()
            )
            
            return {
                "health_score": health_score,
                "status": "healthy" if health_score >= 80 else "warning" if health_score >= 60 else "critical",
                "issues": health_issues,
                "recommendations": recommendations,
                "last_check": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error performing health check for user {user_id}: {e}")
            return {
                "health_score": 0,
                "status": "error",
                "issues": [{"type": "system_error", "severity": "critical", "message": str(e)}],
                "recommendations": ["Contact system administrator"],
                "last_check": datetime.now(timezone.utc).isoformat()
            }
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cache entry is valid."""
        if cache_key not in self.metrics_cache:
            return False
        
        return (datetime.now(timezone.utc) - self.last_cache_update).seconds < self.cache_ttl
    
    def _get_start_time(self, time_range: str, end_time: datetime) -> datetime:
        """Get start time based on time range."""
        if time_range == "1h":
            return end_time - timedelta(hours=1)
        elif time_range == "24h":
            return end_time - timedelta(days=1)
        elif time_range == "7d":
            return end_time - timedelta(days=7)
        elif time_range == "30d":
            return end_time - timedelta(days=30)
        else:
            return end_time - timedelta(days=7)
    
    def _calculate_health_score(
        self, 
        total_servers: int, 
        active_servers: int, 
        error_servers: int,
        enabled_tools: int,
        total_tools: int
    ) -> int:
        """Calculate system health score (0-100)."""
        if total_servers == 0:
            return 100
        
        # Server health (60% weight)
        server_health = (active_servers / total_servers) * 60
        
        # Error penalty (20% weight)
        error_penalty = (error_servers / total_servers) * 20
        
        # Tool utilization (20% weight)
        tool_health = (enabled_tools / total_tools) * 20 if total_tools > 0 else 20
        
        health_score = max(0, min(100, server_health + tool_health - error_penalty))
        return int(health_score)
    
    async def _get_recent_activity(self, db: Session, user_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent activity for user's servers."""
        # This would typically query an activity log table
        # For now, we'll return recent tool usage
        recent_tools = db.query(MCPTool).join(MCPServer).filter(
            MCPServer.user_id == user_id,
            MCPTool.last_used_at.isnot(None)
        ).order_by(desc(MCPTool.last_used_at)).limit(limit).all()
        
        return [
            {
                "type": "tool_execution",
                "tool_name": tool.tool_name,
                "server_name": tool.server.name if hasattr(tool, 'server') else 'Unknown',
                "timestamp": tool.last_used_at.isoformat() if tool.last_used_at else None
            }
            for tool in recent_tools
        ]
    
    async def _get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics from monitoring system."""
        return perf_monitor.get_stats()
    
    async def _get_system_alerts(self, db: Session, user_id: int) -> List[Dict[str, Any]]:
        """Get system alerts for user."""
        alerts = []
        
        # Check for error servers
        error_servers = db.query(MCPServer).filter(
            MCPServer.user_id == user_id,
            MCPServer.status == MCPServerStatus.ERROR
        ).all()
        
        for server in error_servers:
            alerts.append({
                "type": "server_error",
                "severity": "high",
                "message": f"Server '{server.name}' is in error state",
                "server_id": server.id,
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
        
        return alerts
    
    def _calculate_usage_distribution(self, tool_stats: List[Dict[str, Any]]) -> Dict[str, int]:
        """Calculate usage distribution categories."""
        if not tool_stats:
            return {}
        
        total_usage = sum(tool["usage_count"] for tool in tool_stats)
        if total_usage == 0:
            return {"unused": len(tool_stats)}
        
        heavy_use = len([t for t in tool_stats if t["usage_count"] > total_usage * 0.1])
        moderate_use = len([t for t in tool_stats if total_usage * 0.01 < t["usage_count"] <= total_usage * 0.1])
        light_use = len([t for t in tool_stats if 0 < t["usage_count"] <= total_usage * 0.01])
        unused = len([t for t in tool_stats if t["usage_count"] == 0])
        
        return {
            "heavy_use": heavy_use,
            "moderate_use": moderate_use,
            "light_use": light_use,
            "unused": unused
        }
    
    def _calculate_uptime(self, server: MCPServer, start_time: datetime, end_time: datetime) -> float:
        """Calculate server uptime percentage (simplified)."""
        # This would typically use historical status data
        # For now, return based on current status
        if server.status == MCPServerStatus.ACTIVE:
            return 95.0  # Assume 95% uptime for active servers
        elif server.status == MCPServerStatus.ERROR:
            return 60.0  # Lower uptime for error servers
        else:
            return 80.0  # Default uptime
    
    def _get_average_response_time(self, server_id: str) -> float:
        """Get average response time for server."""
        # This would use performance monitoring data
        return 0.5  # Default 500ms
    
    def _calculate_error_rate(self, server_id: str, start_time: datetime, end_time: datetime) -> float:
        """Calculate error rate for server."""
        # This would use error tracking data
        return 2.0  # Default 2% error rate
    
    def _generate_usage_trends(self, tools: List[MCPTool], time_range: str) -> Dict[str, Any]:
        """Generate usage trends (simplified)."""
        # This would use historical data
        return {
            "trend": "stable",
            "change_percentage": 0.0,
            "peak_usage_time": "14:00",
            "data_points": []
        }
    
    def _categorize_tools(self, tools: List[MCPTool]) -> Dict[str, int]:
        """Categorize tools by type."""
        categories = defaultdict(int)
        
        for tool in tools:
            # Simple categorization based on tool name
            name = tool.tool_name.lower()
            if any(keyword in name for keyword in ["file", "read", "write", "directory"]):
                categories["file_management"] += 1
            elif any(keyword in name for keyword in ["http", "request", "api", "web"]):
                categories["web_integration"] += 1
            elif any(keyword in name for keyword in ["git", "github", "repository"]):
                categories["development"] += 1
            elif any(keyword in name for keyword in ["data", "query", "database"]):
                categories["data_access"] += 1
            else:
                categories["general"] += 1
        
        return dict(categories)


# Global monitoring service instance
mcp_monitoring_service = MCPMonitoringService()
