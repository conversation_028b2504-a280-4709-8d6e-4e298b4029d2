# Agent Coordination Configuration
# This file defines settings for agent-to-agent coordination and communication

# Timeout settings
timeouts:
  coordination_timeout_seconds: 30
  handoff_suggestion_timeout_seconds: 15
  specialist_consultation_timeout_seconds: 45

# Circuit breaker settings
circuit_breaker:
  failure_threshold: 5  # Number of failures before opening circuit
  recovery_timeout_seconds: 60  # Time to wait before attempting recovery
  half_open_max_calls: 3  # Max calls to allow in half-open state

# Resource limits
resource_limits:
  max_concurrent_coordinations_per_agent: 3
  max_coordination_queue_size: 10
  max_coordination_history_entries: 100

# Coordination patterns
coordination_types:
  specialist_consultation:
    enabled: true
    timeout_seconds: 30
    retry_attempts: 2
    fallback_enabled: true
  
  handoff_suggestion:
    enabled: true
    timeout_seconds: 15
    require_user_confirmation: true
    auto_timeout_seconds: 300  # Auto-cancel suggestion after 5 minutes
  
  background_coordination:
    enabled: true
    timeout_seconds: 45
    max_parallel_requests: 2

# Persona capabilities for coordination
persona_capabilities:
  concierge:
    coordination_role: "primary"
    can_coordinate_with: ["analysis", "marketing", "classification"]
    specialties: ["user_experience", "general_inquiry", "routing"]

  analysis:
    coordination_role: "specialist"
    can_coordinate_with: ["concierge", "marketing"]
    specialties: ["data_analysis", "research", "insights"]

  marketing:
    coordination_role: "specialist"
    can_coordinate_with: ["concierge", "analysis", "classification"]
    specialties: ["marketing_strategy", "campaigns", "branding"]

  classification:
    coordination_role: "specialist"
    can_coordinate_with: ["concierge", "analysis"]
    specialties: ["categorization", "organization", "classification"]

# Coordination insights configuration
insights:
  max_insights_per_coordination: 5
  insight_confidence_threshold: 0.6
  enable_insight_caching: true
  cache_ttl_seconds: 1800  # 30 minutes

# Audit and monitoring
audit:
  enable_coordination_audit: true
  audit_log_level: "INFO"
  include_request_details: true
  include_response_details: false  # For privacy
  max_audit_entries: 1000

# Performance monitoring
monitoring:
  enable_performance_metrics: true
  track_coordination_latency: true
  track_success_rates: true
  metrics_aggregation_window_seconds: 300  # 5 minutes

# Fallback behavior
fallback:
  enable_fallback_consultation: true
  fallback_timeout_seconds: 10
  fallback_confidence_score: 0.5
  max_fallback_insights: 3
