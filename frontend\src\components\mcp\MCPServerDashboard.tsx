/**
 * MCP Server Management Dashboard
 * 
 * Provides a comprehensive dashboard for managing MCP servers including:
 * - Server list with status indicators
 * - Server actions (start, stop, restart, test)
 * - Tool, resource, and prompt discovery
 * - Usage analytics
 * - Real-time status monitoring
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Play, 
  Square, 
  RotateCcw, 
  TestTube, 
  Search, 
  Plus, 
  Settings, 
  Trash2, 
  Eye,
  Activity,
  Zap,
  Database,
  FileText,
  AlertCircle,
  CheckCircle,
  Clock,
  Loader2
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { 
  MCPServer, 
  MCPServerStatus, 
  MCPTool, 
  MCPResource, 
  MCPPrompt,
  mcpServerApi 
} from '@/lib/mcpServerApi';

interface MCPServerDashboardProps {
  onCreateServer?: () => void;
  onEditServer?: (server: MCPServer) => void;
}

export const MCPServerDashboard: React.FC<MCPServerDashboardProps> = ({
  onCreateServer,
  onEditServer
}) => {
  const { toast } = useToast();
  const [servers, setServers] = useState<MCPServer[]>([]);
  const [filteredServers, setFilteredServers] = useState<MCPServer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedServer, setSelectedServer] = useState<MCPServer | null>(null);
  const [serverActions, setServerActions] = useState<Record<string, boolean>>({});

  // Load servers
  useEffect(() => {
    loadServers();
  }, []);

  // Filter servers
  useEffect(() => {
    let filtered = servers;
    
    if (searchTerm) {
      filtered = filtered.filter(server => 
        server.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        server.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    if (statusFilter !== 'all') {
      filtered = filtered.filter(server => server.status === statusFilter);
    }
    
    setFilteredServers(filtered);
  }, [servers, searchTerm, statusFilter]);

  const loadServers = async () => {
    try {
      const response = await mcpServerApi.listServers();
      setServers(response.servers);
    } catch (error: any) {
      toast({
        title: "Failed to Load Servers",
        description: error.message || "Could not load MCP servers.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleServerAction = async (serverId: string, action: string) => {
    setServerActions(prev => ({ ...prev, [serverId]: true }));
    
    try {
      const response = await mcpServerApi.performAction(serverId, { action: action as any });
      
      if (response.success) {
        toast({
          title: "Action Successful",
          description: response.message
        });
        
        // Reload servers to get updated status
        await loadServers();
      } else {
        toast({
          title: "Action Failed",
          description: response.message,
          variant: "destructive"
        });
      }
    } catch (error: any) {
      toast({
        title: "Action Failed",
        description: error.message || `Failed to ${action} server.`,
        variant: "destructive"
      });
    } finally {
      setServerActions(prev => ({ ...prev, [serverId]: false }));
    }
  };

  const handleDeleteServer = async (serverId: string) => {
    if (!confirm('Are you sure you want to delete this MCP server?')) {
      return;
    }
    
    try {
      await mcpServerApi.deleteServer(serverId);
      toast({
        title: "Server Deleted",
        description: "MCP server deleted successfully."
      });
      await loadServers();
    } catch (error: any) {
      toast({
        title: "Delete Failed",
        description: error.message || "Failed to delete server.",
        variant: "destructive"
      });
    }
  };

  const getStatusBadge = (status: MCPServerStatus) => {
    const variants = {
      active: { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
      inactive: { variant: 'secondary' as const, icon: Clock, color: 'text-gray-600' },
      error: { variant: 'destructive' as const, icon: AlertCircle, color: 'text-red-600' },
      connecting: { variant: 'outline' as const, icon: Loader2, color: 'text-blue-600' }
    };
    
    const config = variants[status];
    const Icon = config.icon;
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className={`w-3 h-3 ${config.color} ${status === 'connecting' ? 'animate-spin' : ''}`} />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getTransportIcon = (transportType: string) => {
    switch (transportType) {
      case 'http': return <Activity className="w-4 h-4" />;
      case 'stdio': return <Settings className="w-4 h-4" />;
      case 'sse': return <Zap className="w-4 h-4" />;
      case 'ws': return <Database className="w-4 h-4" />;
      default: return <Settings className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">MCP Servers</h2>
          <p className="text-muted-foreground">
            Manage your Model Context Protocol servers and their capabilities
          </p>
        </div>
        <Button onClick={onCreateServer}>
          <Plus className="w-4 h-4 mr-2" />
          Add Server
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search servers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-sm"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="error">Error</SelectItem>
                <SelectItem value="connecting">Connecting</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Server List */}
      <div className="grid gap-4">
        {filteredServers.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <Database className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No MCP Servers</h3>
                <p className="text-muted-foreground mb-4">
                  {servers.length === 0 
                    ? "Get started by adding your first MCP server."
                    : "No servers match your current filters."
                  }
                </p>
                {servers.length === 0 && (
                  <Button onClick={onCreateServer}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Your First Server
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ) : (
          filteredServers.map((server) => (
            <Card key={server.id} className="hover:shadow-md transition-shadow">
              <CardContent className="pt-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      {getTransportIcon(server.transport_type)}
                      <h3 className="text-lg font-semibold">{server.name}</h3>
                      {getStatusBadge(server.status)}
                    </div>
                    
                    {server.description && (
                      <p className="text-muted-foreground mb-3">{server.description}</p>
                    )}
                    
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Zap className="w-3 h-3" />
                        {server.tools.length} tools
                      </span>
                      <span className="flex items-center gap-1">
                        <Database className="w-3 h-3" />
                        {server.resources.length} resources
                      </span>
                      <span className="flex items-center gap-1">
                        <FileText className="w-3 h-3" />
                        {server.prompts.length} prompts
                      </span>
                      <span>Transport: {server.transport_type.toUpperCase()}</span>
                    </div>
                    
                    {server.error_message && (
                      <Alert className="mt-3" variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>{server.error_message}</AlertDescription>
                      </Alert>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {/* Server Actions */}
                    {server.status === 'inactive' && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleServerAction(server.id, 'start')}
                        disabled={serverActions[server.id]}
                      >
                        {serverActions[server.id] ? (
                          <Loader2 className="w-4 h-4 animate-spin" />
                        ) : (
                          <Play className="w-4 h-4" />
                        )}
                      </Button>
                    )}
                    
                    {server.status === 'active' && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleServerAction(server.id, 'stop')}
                        disabled={serverActions[server.id]}
                      >
                        {serverActions[server.id] ? (
                          <Loader2 className="w-4 h-4 animate-spin" />
                        ) : (
                          <Square className="w-4 h-4" />
                        )}
                      </Button>
                    )}
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleServerAction(server.id, 'restart')}
                      disabled={serverActions[server.id]}
                    >
                      {serverActions[server.id] ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <RotateCcw className="w-4 h-4" />
                      )}
                    </Button>
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleServerAction(server.id, 'test')}
                      disabled={serverActions[server.id]}
                    >
                      {serverActions[server.id] ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <TestTube className="w-4 h-4" />
                      )}
                    </Button>
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setSelectedServer(server)}
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onEditServer?.(server)}
                    >
                      <Settings className="w-4 h-4" />
                    </Button>
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDeleteServer(server.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Server Details Modal/Panel would go here */}
      {selectedServer && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getTransportIcon(selectedServer.transport_type)}
              {selectedServer.name} - Capabilities
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="tools">
              <TabsList>
                <TabsTrigger value="tools">Tools ({selectedServer.tools.length})</TabsTrigger>
                <TabsTrigger value="resources">Resources ({selectedServer.resources.length})</TabsTrigger>
                <TabsTrigger value="prompts">Prompts ({selectedServer.prompts.length})</TabsTrigger>
              </TabsList>
              
              <TabsContent value="tools" className="space-y-2">
                {selectedServer.tools.map((tool) => (
                  <div key={tool.id} className="p-3 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">{tool.tool_name}</h4>
                        {tool.tool_description && (
                          <p className="text-sm text-muted-foreground">{tool.tool_description}</p>
                        )}
                      </div>
                      <Badge variant={tool.is_enabled ? "default" : "secondary"}>
                        {tool.is_enabled ? "Enabled" : "Disabled"}
                      </Badge>
                    </div>
                  </div>
                ))}
              </TabsContent>
              
              <TabsContent value="resources" className="space-y-2">
                {selectedServer.resources.map((resource) => (
                  <div key={resource.id} className="p-3 border rounded-lg">
                    <h4 className="font-medium">{resource.resource_name}</h4>
                    <p className="text-sm text-muted-foreground">Type: {resource.resource_type}</p>
                    {resource.resource_description && (
                      <p className="text-sm text-muted-foreground">{resource.resource_description}</p>
                    )}
                  </div>
                ))}
              </TabsContent>
              
              <TabsContent value="prompts" className="space-y-2">
                {selectedServer.prompts.map((prompt) => (
                  <div key={prompt.id} className="p-3 border rounded-lg">
                    <h4 className="font-medium">{prompt.prompt_name}</h4>
                    {prompt.prompt_description && (
                      <p className="text-sm text-muted-foreground">{prompt.prompt_description}</p>
                    )}
                  </div>
                ))}
              </TabsContent>
            </Tabs>
            
            <div className="mt-4 flex justify-end">
              <Button variant="outline" onClick={() => setSelectedServer(null)}>
                Close
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
