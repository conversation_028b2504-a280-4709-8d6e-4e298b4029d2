#!/usr/bin/env python3
"""
Test script to verify Phase 3 and Phase 4 integration fixes.

This script tests that:
1. Phase 3 services handle None self_healing_system gracefully
2. CompiledGraph import issues are resolved
3. The workflow manager initializes without errors
"""

import asyncio
import logging
import sys
import os
from unittest.mock import MagicMock, AsyncMock, patch

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_phase3_service_none_handling():
    """Test that Phase 3 service handles None self_healing_system gracefully."""
    print("Testing Phase 3 service None handling...")
    
    try:
        from agents.langgraph.integrations.phase3_integration_service import Phase3IntegrationService
        from agents.langgraph.monitoring.workflow_monitor import WorkflowMonitor
        
        # Create a mock workflow monitor
        mock_monitor = MagicMock(spec=WorkflowMonitor)
        
        # Create Phase 3 service with disabled self-healing
        with patch('backend.config.phase3_config.get_phase3_config') as mock_config:
            mock_config.return_value = MagicMock(
                enable_predictive_optimization=False,
                enable_self_healing=False,  # Disabled - should be None
                enable_advanced_collaboration=False
            )
            
            with patch('backend.config.phase3_config.get_resource_limits') as mock_limits:
                mock_limits.return_value = {'max_concurrent_optimizations': 5}
                
                service = Phase3IntegrationService(mock_monitor)
                
                # Verify self_healing_system is None
                assert service.self_healing_system is None, "self_healing_system should be None when disabled"
                
                # Test initialization
                init_result = await service.initialize()
                assert init_result is True, "Initialization should succeed even with None self_healing_system"
                
                # Test starting services - this should not fail with None self_healing_system
                await service.start_services()
                
                print("✅ Phase 3 service None handling test passed")
                return True
        
    except Exception as e:
        print(f"❌ Phase 3 service None handling test failed: {e}")
        return False


async def test_compiled_graph_import_fixes():
    """Test that CompiledGraph import issues are resolved."""
    print("Testing CompiledGraph import fixes...")
    
    try:
        # Test template manager import
        from agents.langgraph.templates.template_manager import TemplateManager
        print("✅ TemplateManager imported successfully")
        
        # Test base template import
        from agents.langgraph.templates.base_template import WorkflowTemplate
        print("✅ WorkflowTemplate imported successfully")
        
        # Test business intelligence workflow import
        from agents.langgraph.workflows.business_intelligence import BusinessIntelligenceWorkflow
        print("✅ BusinessIntelligenceWorkflow imported successfully")
        
        print("✅ CompiledGraph import fixes test passed")
        return True
        
    except ImportError as e:
        print(f"❌ CompiledGraph import fixes test failed: {e}")
        return False
    except Exception as e:
        print(f"❌ CompiledGraph import fixes test failed with unexpected error: {e}")
        return False


async def test_workflow_manager_initialization():
    """Test that WorkflowManager initializes without errors."""
    print("Testing WorkflowManager initialization...")
    
    try:
        # Mock the configuration functions to avoid dependency issues
        with patch('agents.langgraph.core.workflow_manager.is_phase3_enabled', return_value=False):
            with patch('agents.langgraph.core.workflow_manager.get_phase3_config', return_value=None):
                from agents.langgraph.core.workflow_manager import WorkflowManager
                
                # Create workflow manager - this should not fail
                manager = WorkflowManager()
                
                # Verify basic attributes
                assert hasattr(manager, 'workflow_monitor'), "WorkflowManager should have workflow_monitor"
                assert hasattr(manager, 'phase3_enabled'), "WorkflowManager should have phase3_enabled"
                
                print("✅ WorkflowManager initialization test passed")
                return True
        
    except Exception as e:
        print(f"❌ WorkflowManager initialization test failed: {e}")
        return False


async def test_conversation_tool_dynamic_intents():
    """Test that ConversationTool handles dynamic intents properly."""
    print("Testing ConversationTool dynamic intent handling...")
    
    try:
        from agents.tools.mcp.conversation_tool import ConversationTool
        
        tool = ConversationTool()
        
        # Test with a known intent
        known_instruction = tool._get_user_prompt_instruction("general_question")
        assert "Datagenius Concierge" in known_instruction, "Should contain Datagenius Concierge"
        
        # Test with an unknown/dynamic intent
        dynamic_instruction = tool._get_user_prompt_instruction("custom_business_analysis")
        assert "custom_business_analysis" in dynamic_instruction, "Should mention the custom intent"
        assert "Datagenius Concierge" in dynamic_instruction, "Should still identify as Datagenius Concierge"
        
        print("✅ ConversationTool dynamic intent handling test passed")
        return True
        
    except Exception as e:
        print(f"❌ ConversationTool dynamic intent handling test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Starting Phase integration fixes tests...\n")
    
    tests = [
        test_phase3_service_none_handling,
        test_compiled_graph_import_fixes,
        test_workflow_manager_initialization,
        test_conversation_tool_dynamic_intents
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
        print()  # Add spacing between tests
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Phase integration fixes are working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
