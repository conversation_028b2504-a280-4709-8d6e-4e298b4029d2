"""
Integration Tests for Phase 2: Enhanced User Experience Features

This module provides integration tests that verify the complete workflow
of Phase 2 features working together in realistic scenarios.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
from typing import Dict, Any

from ..core.workflow_manager import WorkflowManager
from ..nodes.routing_node import AgentSwitchNode
from ..nodes.base_agent_node import BaseAgentNode
from ..states.unified_state import (
    UnifiedDatageniusState,
    create_unified_state,
    ConversationMode,
    MessageType
)


class MockAgent(BaseAgentNode):
    """Mock agent for integration testing."""
    
    def __init__(self, agent_id: str, agent_type: str = "test"):
        super().__init__(agent_id, agent_type, {})
        self.process_message_calls = []
    
    async def _process_message(self, state: UnifiedDatageniusState):
        """Mock message processing."""
        self.process_message_calls.append(state.get("current_message", {}).get("content", ""))
        
        # Add a response message
        response_message = {
            "id": f"response_{len(self.process_message_calls)}",
            "content": f"Response from {self.agent_id}: {state.get('current_message', {}).get('content', '')}",
            "type": MessageType.AGENT.value,
            "timestamp": datetime.now().isoformat(),
            "agent_id": self.agent_id
        }
        
        from ..states.unified_state import add_message
        state = add_message(state, response_message, MessageType.AGENT)
        return state


class TestPhase2Integration:
    """Integration tests for Phase 2 features."""
    
    @pytest.fixture
    def workflow_manager_with_mocks(self):
        """Create WorkflowManager with mock agents."""
        with patch('backend.agents.langgraph.core.workflow_manager.WorkflowManager._initialize_agents'):
            manager = WorkflowManager()
            
            # Add mock agents
            manager.agent_nodes = {
                "concierge-agent": MockAgent("concierge-agent", "concierge"),
                "analysis-agent": MockAgent("analysis-agent", "analysis"),
                "marketing-agent": MockAgent("marketing-agent", "marketing")
            }
            
            return manager
    
    @pytest.mark.asyncio
    async def test_complete_agent_switch_workflow(self, workflow_manager_with_mocks):
        """Test complete workflow from initial selection to agent switch."""
        manager = workflow_manager_with_mocks
        
        # Step 1: Create workflow with initial agent selection
        with patch.object(manager, '_build_workflow_graph') as mock_build_graph:
            mock_build_graph.return_value = Mock()
            
            workflow_id = await manager.create_workflow(
                user_id="test_user",
                conversation_id="test_conversation",
                message="Hello, I need help with analysis",
                selected_agent="concierge-agent"
            )
        
        # Verify workflow was created with correct initial agent
        assert workflow_id is not None
        
        # Step 2: Simulate agent switch request
        switch_state = manager._initialize_workflow_state(
            user_id="test_user",
            conversation_id="test_conversation",
            selected_agent="concierge-agent",
            context={"message": "switch to analysis"}
        )
        
        # Process switch request through AgentSwitchNode
        agent_switch_node = AgentSwitchNode(manager.agent_nodes)
        switch_state["current_message"] = {
            "content": "switch to analysis",
            "timestamp": datetime.now().isoformat()
        }
        
        result_state = await agent_switch_node.execute(switch_state)
        
        # Verify switch occurred
        assert result_state["selected_agent"] == "analysis-agent"
        assert result_state["conversation_mode"] == ConversationMode.AGENT_SWITCH
        
        # Verify confirmation message
        messages = result_state["messages"]
        confirmation_msg = next(
            (msg for msg in messages if "Switching you to analysis-agent" in msg.get("content", "")),
            None
        )
        assert confirmation_msg is not None
    
    @pytest.mark.asyncio
    async def test_agent_coordination_workflow(self, workflow_manager_with_mocks):
        """Test agent coordination and handoff suggestion workflow."""
        manager = workflow_manager_with_mocks
        
        # Initialize state with concierge agent
        state = manager._initialize_workflow_state(
            user_id="test_user",
            conversation_id="test_conversation",
            selected_agent="concierge-agent",
            context={"message": "I need complex data analysis"}
        )
        
        concierge_agent = manager.agent_nodes["concierge-agent"]
        
        # Step 1: Concierge coordinates with analysis specialist
        coordination_result = await concierge_agent._coordinate_with_specialist(
            state,
            "analysis-agent",
            {"query_type": "complex_analysis"}
        )
        
        assert coordination_result["success"] is True
        assert coordination_result["specialist_agent"] == "analysis-agent"
        assert len(coordination_result["insights"]) > 0
        
        # Step 2: Concierge suggests handoff based on coordination
        state = await concierge_agent._suggest_handoff(
            state,
            "analysis-agent",
            "the analysis specialist can provide more detailed insights",
            confidence=0.9
        )
        
        # Verify handoff suggestion
        assert "pending_handoff_suggestion" in state
        suggestion = state["pending_handoff_suggestion"]
        assert suggestion["target_agent"] == "analysis-agent"
        assert suggestion["suggesting_agent"] == "concierge-agent"
        
        # Verify suggestion message was added
        messages = state["messages"]
        suggestion_msg = messages[-1]
        assert "analysis-agent specialist might be better suited" in suggestion_msg["content"]
    
    @pytest.mark.asyncio
    async def test_workflow_initialization_with_context(self, workflow_manager_with_mocks):
        """Test workflow initialization with rich context."""
        manager = workflow_manager_with_mocks
        
        context = {
            "message": "Analyze my business data",
            "business_profile_id": "business_123",
            "attached_files": ["data.csv", "report.pdf"],
            "user_preferences": {"analysis_type": "detailed"},
            "conversation_context": {"previous_topic": "marketing"}
        }
        
        state = manager._initialize_workflow_state(
            user_id="test_user",
            conversation_id="test_conversation",
            selected_agent="analysis-agent",
            context=context
        )
        
        # Verify all context was preserved
        assert state["selected_agent"] == "analysis-agent"
        assert state["business_profile_id"] == "business_123"
        assert "data.csv" in state["attached_files"]
        assert "report.pdf" in state["attached_files"]
        assert state["user_preferences"]["analysis_type"] == "detailed"
        assert state["conversation_context"]["previous_topic"] == "marketing"
        
        # Verify initialization was logged
        coordination_log = state["agent_coordination_log"]
        init_record = coordination_log[0]
        assert init_record["action"] == "workflow_initialized"
        assert init_record["initial_agent"] == "analysis-agent"
    
    @pytest.mark.asyncio
    async def test_fallback_behavior(self, workflow_manager_with_mocks):
        """Test fallback behavior when selected agent is unavailable."""
        manager = workflow_manager_with_mocks
        
        # Try to select non-existent agent
        state = manager._initialize_workflow_state(
            user_id="test_user",
            conversation_id="test_conversation",
            selected_agent="nonexistent-agent",
            context={"message": "Hello"}
        )
        
        # Should fallback to concierge
        assert state["selected_agent"] == "concierge-agent"
        
        # Verify fallback was logged
        coordination_log = state["agent_coordination_log"]
        init_record = coordination_log[0]
        assert init_record["initial_agent"] == "concierge-agent"
        assert init_record["selected_agent"] == "nonexistent-agent"  # Original request preserved
    
    @pytest.mark.asyncio
    async def test_agent_switch_with_invalid_target(self, workflow_manager_with_mocks):
        """Test agent switch request with invalid target agent."""
        manager = workflow_manager_with_mocks
        
        state = manager._initialize_workflow_state(
            user_id="test_user",
            conversation_id="test_conversation",
            selected_agent="concierge-agent",
            context={"message": "switch to invalid-agent"}
        )
        
        # Process invalid switch request
        agent_switch_node = AgentSwitchNode(manager.agent_nodes)
        state["current_message"] = {
            "content": "switch to invalid-agent",
            "timestamp": datetime.now().isoformat()
        }
        
        result_state = await agent_switch_node.execute(state)
        
        # Should remain with original agent
        assert result_state["selected_agent"] == "concierge-agent"
        
        # Should have error message
        messages = result_state["messages"]
        error_msg = next(
            (msg for msg in messages if "couldn't find an agent named" in msg.get("content", "")),
            None
        )
        assert error_msg is not None
        assert "invalid-agent" in error_msg["content"]
    
    def test_workflow_graph_construction_includes_agent_switch(self, workflow_manager_with_mocks):
        """Test that workflow graph construction includes AgentSwitchNode."""
        manager = workflow_manager_with_mocks
        
        # Create a sample state for graph building
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation",
            workflow_type="default"
        )
        
        # Mock the graph building process
        with patch('langgraph.graph.StateGraph') as mock_state_graph:
            mock_workflow = Mock()
            mock_state_graph.return_value = mock_workflow
            
            # This would normally be called during workflow creation
            # We're testing the structure here
            asyncio.run(manager._build_workflow_graph(state))
            
            # Verify AgentSwitchNode was added
            add_node_calls = mock_workflow.add_node.call_args_list
            node_names = [call[0][0] for call in add_node_calls]
            
            assert "agent_switch" in node_names
            assert "routing" in node_names  # Backward compatibility
            
            # Verify agent nodes were added
            for agent_id in manager.agent_nodes.keys():
                assert f"agent_{agent_id}" in node_names


class TestPhase2ErrorHandling:
    """Test error handling in Phase 2 features."""
    
    @pytest.fixture
    def agent_switch_node(self):
        """Create AgentSwitchNode for error testing."""
        return AgentSwitchNode({"concierge-agent": Mock()})
    
    @pytest.mark.asyncio
    async def test_agent_switch_with_empty_state(self, agent_switch_node):
        """Test AgentSwitchNode with empty state."""
        result = await agent_switch_node.execute({})
        assert result == {}  # Should return empty state gracefully
    
    @pytest.mark.asyncio
    async def test_agent_switch_with_none_state(self, agent_switch_node):
        """Test AgentSwitchNode with None state."""
        result = await agent_switch_node.execute(None)
        assert result == {}  # Should return empty state gracefully
    
    @pytest.mark.asyncio
    async def test_coordination_error_handling(self):
        """Test error handling in agent coordination."""
        class ErrorAgent(BaseAgentNode):
            async def _process_message(self, state):
                return state
            
            async def _simulate_specialist_consultation(self, *args, **kwargs):
                raise Exception("Consultation failed")
        
        agent = ErrorAgent("error-agent", "test", {})
        state = create_unified_state("user", "conv", "default")
        state["available_agents"] = ["specialist-agent"]
        
        # Should handle error gracefully
        result = await agent._coordinate_with_specialist(state, "specialist-agent")
        assert result["success"] is False
        assert "error" in result


if __name__ == "__main__":
    pytest.main([__file__])
