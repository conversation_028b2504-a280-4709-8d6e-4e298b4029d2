"""
Comprehensive validation utilities for Datagenius MCP integration.

This module provides validation functions for MCP server configurations,
input sanitization, and security checks.
"""

import re
import json
import logging
from typing import Dict, Any, List, Optional, Tuple
from urllib.parse import urlparse
import ipaddress

from ..models.mcp_server import MCPServerCreate, MCPJSONConfiguration, MCPServerDefinition
from ..security.mcp_security import mcp_security_validator

logger = logging.getLogger(__name__)


class ValidationError(Exception):
    """Custom exception for validation errors."""
    
    def __init__(self, message: str, field: Optional[str] = None, code: Optional[str] = None):
        self.message = message
        self.field = field
        self.code = code
        super().__init__(message)


class MCPValidator:
    """Comprehensive validator for MCP server configurations."""
    
    def __init__(self):
        """Initialize the MCP validator."""
        self.logger = logging.getLogger(__name__)
    
    async def validate_server_creation(
        self, 
        server_data: MCPServerC<PERSON>, 
        user_id: int
    ) -> Tuple[bool, List[str]]:
        """
        Validate MCP server creation request.
        
        Args:
            server_data: Server creation data
            user_id: User ID for authorization checks
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        
        try:
            # Basic field validation
            basic_errors = self._validate_basic_fields(server_data)
            errors.extend(basic_errors)
            
            # Configuration-specific validation
            if server_data.config_type == "json" and server_data.json_config:
                config_errors = await self._validate_json_configuration(
                    server_data.json_config, user_id
                )
                errors.extend(config_errors)
            elif server_data.config_type == "form" and server_data.server_config:
                form_errors = self._validate_form_configuration(server_data.server_config)
                errors.extend(form_errors)
            else:
                errors.append("Invalid configuration: must provide either json_config or server_config")
            
            # Business profile validation
            if server_data.business_profile_id:
                profile_errors = await self._validate_business_profile_access(
                    server_data.business_profile_id, user_id
                )
                errors.extend(profile_errors)
            
            return len(errors) == 0, errors
            
        except Exception as e:
            self.logger.error(f"Error validating server creation: {e}")
            return False, [f"Validation error: {str(e)}"]
    
    def _validate_basic_fields(self, server_data: MCPServerCreate) -> List[str]:
        """Validate basic server fields."""
        errors = []
        
        # Name validation
        if not server_data.name or not server_data.name.strip():
            errors.append("Server name is required")
        elif len(server_data.name) > 100:
            errors.append("Server name must be 100 characters or less")
        elif not re.match(r'^[a-zA-Z0-9\s\-_\.]+$', server_data.name):
            errors.append("Server name contains invalid characters")
        
        # Description validation
        if server_data.description and len(server_data.description) > 500:
            errors.append("Server description must be 500 characters or less")
        
        # Config type validation
        if server_data.config_type not in ["json", "form"]:
            errors.append("Config type must be 'json' or 'form'")
        
        return errors
    
    async def _validate_json_configuration(
        self, 
        json_config: MCPJSONConfiguration, 
        user_id: int
    ) -> List[str]:
        """Validate JSON configuration."""
        try:
            # Use security validator for comprehensive validation
            is_valid, errors = await mcp_security_validator.validate_server_configuration(
                json_config, user_id
            )
            return errors
            
        except Exception as e:
            self.logger.error(f"Error validating JSON configuration: {e}")
            return [f"JSON configuration validation failed: {str(e)}"]
    
    def _validate_form_configuration(self, server_config: Any) -> List[str]:
        """Validate form-based configuration."""
        errors = []
        
        try:
            # Transport type validation
            if not hasattr(server_config, 'transport_type'):
                errors.append("Transport type is required")
            
            # URL validation for HTTP/WebSocket servers
            if hasattr(server_config, 'url') and server_config.url:
                url_errors = self._validate_url(server_config.url)
                errors.extend(url_errors)
            
            # Command validation for stdio servers
            if hasattr(server_config, 'command') and server_config.command:
                command_errors = self._validate_command(server_config.command)
                errors.extend(command_errors)
            
            return errors
            
        except Exception as e:
            self.logger.error(f"Error validating form configuration: {e}")
            return [f"Form configuration validation failed: {str(e)}"]
    
    def _validate_url(self, url: str) -> List[str]:
        """Validate URL format and security."""
        errors = []
        
        try:
            parsed = urlparse(url)
            
            # Scheme validation
            if parsed.scheme not in ['http', 'https', 'ws', 'wss']:
                errors.append(f"Invalid URL scheme: {parsed.scheme}")
            
            # Hostname validation
            if not parsed.hostname:
                errors.append("URL must include a hostname")
            else:
                # Check for blocked domains/IPs
                hostname = parsed.hostname.lower()
                blocked_domains = ['localhost', '127.0.0.1', '0.0.0.0']
                if hostname in blocked_domains:
                    errors.append(f"Blocked hostname: {hostname}")
            
            # Port validation
            if parsed.port and (parsed.port < 1 or parsed.port > 65535):
                errors.append(f"Invalid port: {parsed.port}")
            
        except Exception:
            errors.append("Invalid URL format")
        
        return errors
    
    def _validate_command(self, command: str) -> List[str]:
        """Validate command for stdio servers."""
        errors = []
        
        # Basic command validation
        if not command.strip():
            errors.append("Command cannot be empty")
            return errors
        
        # Check for dangerous commands
        dangerous_patterns = [
            r'rm\s+-rf',
            r'sudo',
            r'chmod',
            r'chown',
            r'[;&|`$()]'
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, command, re.IGNORECASE):
                errors.append(f"Command contains potentially dangerous pattern: {pattern}")
        
        return errors
    
    async def _validate_business_profile_access(
        self, 
        business_profile_id: str, 
        user_id: int
    ) -> List[str]:
        """Validate user access to business profile."""
        # This would integrate with your business profile access control
        # For now, we'll do basic validation
        errors = []
        
        if not business_profile_id.strip():
            errors.append("Business profile ID cannot be empty")
        
        # Add more validation as needed based on your business profile system
        
        return errors
    
    def validate_input_variable(
        self, 
        variable_id: str, 
        variable_type: str, 
        value: str
    ) -> List[str]:
        """Validate input variable data."""
        errors = []
        
        # Variable ID validation
        if not variable_id or not variable_id.strip():
            errors.append("Variable ID is required")
        elif not re.match(r'^[a-zA-Z0-9_-]+$', variable_id):
            errors.append("Variable ID contains invalid characters")
        
        # Variable type validation
        valid_types = ["promptString", "promptChoice", "env"]
        if variable_type not in valid_types:
            errors.append(f"Invalid variable type: {variable_type}")
        
        # Value validation
        if not value:
            errors.append("Variable value is required")
        elif len(value) > 1000:
            errors.append("Variable value is too long (max 1000 characters)")
        
        return errors
    
    def sanitize_input(self, input_data: str) -> str:
        """Sanitize user input to prevent injection attacks."""
        if not input_data:
            return ""
        
        # Remove potentially dangerous characters
        sanitized = re.sub(r'[<>"\';\\]', '', input_data)
        
        # Limit length
        sanitized = sanitized[:1000]
        
        return sanitized.strip()
    
    def validate_json_structure(self, json_data: str) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        """Validate JSON structure and parse safely."""
        try:
            # Check for reasonable size
            if len(json_data) > 100000:  # 100KB limit
                return False, None, "JSON data too large (max 100KB)"
            
            # Parse JSON
            parsed_data = json.loads(json_data)
            
            # Basic structure validation
            if not isinstance(parsed_data, dict):
                return False, None, "JSON must be an object"
            
            return True, parsed_data, None
            
        except json.JSONDecodeError as e:
            return False, None, f"Invalid JSON format: {str(e)}"
        except Exception as e:
            return False, None, f"JSON validation error: {str(e)}"


class RequestValidator:
    """Validator for API request parameters."""
    
    @staticmethod
    def validate_pagination(skip: int, limit: int) -> List[str]:
        """Validate pagination parameters."""
        errors = []
        
        if skip < 0:
            errors.append("Skip parameter must be non-negative")
        
        if limit < 1:
            errors.append("Limit parameter must be positive")
        elif limit > 1000:
            errors.append("Limit parameter cannot exceed 1000")
        
        return errors
    
    @staticmethod
    def validate_server_id(server_id: str) -> List[str]:
        """Validate server ID format."""
        errors = []
        
        if not server_id or not server_id.strip():
            errors.append("Server ID is required")
        elif not re.match(r'^[a-zA-Z0-9\-]+$', server_id):
            errors.append("Invalid server ID format")
        elif len(server_id) > 50:
            errors.append("Server ID too long")
        
        return errors
    
    @staticmethod
    def validate_action(action: str) -> List[str]:
        """Validate server action."""
        errors = []
        
        valid_actions = ["start", "stop", "restart", "test", "discover"]
        if action not in valid_actions:
            errors.append(f"Invalid action: {action}. Valid actions: {', '.join(valid_actions)}")
        
        return errors


# Global validator instances
mcp_validator = MCPValidator()
request_validator = RequestValidator()
