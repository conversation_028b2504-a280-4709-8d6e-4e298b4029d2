"""
Runtime Configuration for LangGraph Integration.

This module provides runtime configuration management for dynamic
settings that can be updated during application execution.
"""

import logging
import threading
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timezone
from pydantic import BaseModel, Field, field_validator
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class ConfigurationChange:
    """Represents a configuration change event."""
    key: str
    old_value: Any
    new_value: Any
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    source: str = "runtime"


class RuntimeConfig(BaseModel):
    """
    Runtime configuration for LangGraph integration.
    
    This class manages dynamic configuration settings that can be
    updated during application execution without requiring a restart.
    """
    
    # Feature toggles
    enable_langgraph: bool = Field(
        default=True,
        description="Enable LangGraph integration"
    )
    
    enable_monitoring: bool = Field(
        default=True,
        description="Enable runtime monitoring"
    )
    
    enable_caching: bool = Field(
        default=True,
        description="Enable result caching"
    )
    
    # Performance settings
    max_workflow_duration: int = Field(
        default=3600,
        description="Maximum workflow duration in seconds"
    )
    
    max_agent_transitions: int = Field(
        default=20,
        description="Maximum agent transitions per workflow"
    )
    
    max_tool_executions: int = Field(
        default=50,
        description="Maximum tool executions per workflow"
    )
    
    # Cache settings
    cache_ttl_seconds: int = Field(
        default=300,
        description="Cache TTL in seconds"
    )
    
    max_cache_size: int = Field(
        default=1000,
        description="Maximum cache size"
    )
    
    # Monitoring settings
    monitoring_interval_seconds: int = Field(
        default=60,
        description="Monitoring interval in seconds"
    )
    
    metrics_retention_hours: int = Field(
        default=24,
        description="Metrics retention in hours"
    )
    
    # Recovery settings
    enable_auto_recovery: bool = Field(
        default=True,
        description="Enable automatic recovery"
    )
    
    max_recovery_attempts: int = Field(
        default=3,
        description="Maximum recovery attempts"
    )
    
    recovery_timeout_seconds: int = Field(
        default=300,
        description="Recovery timeout in seconds"
    )
    
    # Migration settings
    rollout_percentage: float = Field(
        default=100.0,
        description="Feature rollout percentage"
    )
    
    @field_validator('rollout_percentage')
    @classmethod
    def validate_rollout_percentage(cls, v):
        """Validate rollout percentage."""
        if not 0.0 <= v <= 100.0:
            raise ValueError("rollout_percentage must be between 0.0 and 100.0")
        return v
    
    @field_validator('max_workflow_duration', 'max_agent_transitions', 'max_tool_executions')
    @classmethod
    def validate_positive_integers(cls, v):
        """Validate positive integer values."""
        if v <= 0:
            raise ValueError("Value must be positive")
        return v
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return self.model_dump()
    
    def update_from_dict(self, config_dict: Dict[str, Any]) -> List[ConfigurationChange]:
        """Update configuration from dictionary and return changes."""
        changes = []
        
        for key, new_value in config_dict.items():
            if hasattr(self, key):
                old_value = getattr(self, key)
                if old_value != new_value:
                    setattr(self, key, new_value)
                    changes.append(ConfigurationChange(
                        key=key,
                        old_value=old_value,
                        new_value=new_value
                    ))
            else:
                logger.warning(f"Unknown configuration key: {key}")
        
        return changes


class RuntimeConfigManager:
    """
    Manager for runtime configuration with thread-safe updates
    and change notifications.
    """
    
    def __init__(self, initial_config: Optional[RuntimeConfig] = None):
        """Initialize the runtime configuration manager."""
        self._config = initial_config or RuntimeConfig()
        self._lock = threading.RLock()
        self._change_listeners: List[Callable[[List[ConfigurationChange]], None]] = []
        self._change_history: List[ConfigurationChange] = []
        self._max_history_size = 1000
        
        logger.info("RuntimeConfigManager initialized")
    
    def get_config(self) -> RuntimeConfig:
        """Get the current configuration."""
        with self._lock:
            return self._config.model_copy()
    
    def update_config(self, updates: Dict[str, Any]) -> List[ConfigurationChange]:
        """Update configuration with new values."""
        with self._lock:
            changes = self._config.update_from_dict(updates)
            
            if changes:
                # Add to history
                self._change_history.extend(changes)
                
                # Trim history if needed
                if len(self._change_history) > self._max_history_size:
                    self._change_history = self._change_history[-self._max_history_size:]
                
                # Notify listeners
                self._notify_listeners(changes)
                
                logger.info(f"Configuration updated with {len(changes)} changes")
            
            return changes
    
    def get_value(self, key: str, default: Any = None) -> Any:
        """Get a specific configuration value."""
        with self._lock:
            return getattr(self._config, key, default)
    
    def set_value(self, key: str, value: Any) -> Optional[ConfigurationChange]:
        """Set a specific configuration value."""
        return self.update_config({key: value})
    
    def add_change_listener(self, listener: Callable[[List[ConfigurationChange]], None]) -> None:
        """Add a change listener."""
        with self._lock:
            self._change_listeners.append(listener)
    
    def remove_change_listener(self, listener: Callable[[List[ConfigurationChange]], None]) -> None:
        """Remove a change listener."""
        with self._lock:
            if listener in self._change_listeners:
                self._change_listeners.remove(listener)
    
    def get_change_history(self, limit: Optional[int] = None) -> List[ConfigurationChange]:
        """Get configuration change history."""
        with self._lock:
            if limit:
                return self._change_history[-limit:]
            return self._change_history.copy()
    
    def _notify_listeners(self, changes: List[ConfigurationChange]) -> None:
        """Notify all change listeners."""
        for listener in self._change_listeners:
            try:
                listener(changes)
            except Exception as e:
                logger.error(f"Error notifying configuration change listener: {e}")
    
    def reset_to_defaults(self) -> List[ConfigurationChange]:
        """Reset configuration to default values."""
        with self._lock:
            old_config = self._config.model_copy()
            self._config = RuntimeConfig()
            
            # Generate changes
            changes = []
            for key in old_config.model_fields:
                old_value = getattr(old_config, key)
                new_value = getattr(self._config, key)
                if old_value != new_value:
                    changes.append(ConfigurationChange(
                        key=key,
                        old_value=old_value,
                        new_value=new_value,
                        source="reset"
                    ))
            
            if changes:
                self._change_history.extend(changes)
                self._notify_listeners(changes)
                logger.info("Configuration reset to defaults")
            
            return changes


# Global runtime configuration manager
_runtime_config_manager: Optional[RuntimeConfigManager] = None


def get_runtime_config_manager() -> RuntimeConfigManager:
    """Get the global runtime configuration manager."""
    global _runtime_config_manager
    if _runtime_config_manager is None:
        _runtime_config_manager = RuntimeConfigManager()
    return _runtime_config_manager


def get_runtime_config() -> RuntimeConfig:
    """Get the current runtime configuration."""
    return get_runtime_config_manager().get_config()


def update_runtime_config(updates: Dict[str, Any]) -> List[ConfigurationChange]:
    """Update the runtime configuration."""
    return get_runtime_config_manager().update_config(updates)


def get_runtime_config_value(key: str, default: Any = None) -> Any:
    """Get a specific runtime configuration value."""
    return get_runtime_config_manager().get_value(key, default)
