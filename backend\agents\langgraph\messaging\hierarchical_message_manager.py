"""
Hierarchical Message Manager for LangGraph-based Datagenius System.

This module provides comprehensive message management with hierarchical structure,
editing capabilities, conversation branching, and resubmission functionality.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Set
from datetime import datetime
from dataclasses import dataclass, field
import uuid
from enum import Enum

logger = logging.getLogger(__name__)


class MessageType(Enum):
    """Types of messages in the system."""
    USER_INPUT = "user_input"
    AGENT_RESPONSE = "agent_response"
    SYSTEM_MESSAGE = "system_message"
    TOOL_EXECUTION = "tool_execution"
    ERROR_MESSAGE = "error_message"
    EDIT_MESSAGE = "edit_message"


class MessageStatus(Enum):
    """Status of messages."""
    ACTIVE = "active"
    EDITED = "edited"
    DELETED = "deleted"
    ARCHIVED = "archived"


@dataclass
class MessageThreadNode:
    """Node in the message thread hierarchy."""
    thread_id: str
    conversation_id: str
    parent_message_id: Optional[str]
    message_content: Dict[str, Any]
    message_type: MessageType
    message_status: MessageStatus
    created_by: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    children: List[str] = field(default_factory=list)


@dataclass
class EditRecord:
    """Record of message edit."""
    edit_id: str
    original_message_id: str
    edited_message_id: str
    edit_type: str
    edited_by: str
    edited_at: datetime
    edit_reason: Optional[str] = None
    diff_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ResubmissionContext:
    """Context for conversation resubmission."""
    resubmission_id: str
    original_conversation_id: str
    branch_point_message_id: str
    branch_messages: List[MessageThreadNode]
    original_workflow_context: Dict[str, Any]
    resubmission_time: datetime
    resubmitted_by: str
    resubmission_reason: Optional[str] = None


class MessageTree:
    """Tree structure for managing hierarchical messages."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.nodes: Dict[str, MessageThreadNode] = {}
        self.conversation_roots: Dict[str, List[str]] = {}
    
    async def add_thread(self, thread_node: MessageThreadNode) -> None:
        """Add thread node to the tree."""
        try:
            # Store the node
            self.nodes[thread_node.thread_id] = thread_node
            
            # Update parent-child relationships
            if thread_node.parent_message_id:
                parent_node = self.nodes.get(thread_node.parent_message_id)
                if parent_node:
                    parent_node.children.append(thread_node.thread_id)
            else:
                # Root message for conversation
                if thread_node.conversation_id not in self.conversation_roots:
                    self.conversation_roots[thread_node.conversation_id] = []
                self.conversation_roots[thread_node.conversation_id].append(thread_node.thread_id)
            
            self.logger.debug(f"Added thread node: {thread_node.thread_id}")
            
        except Exception as e:
            self.logger.error(f"Error adding thread node: {e}")
            raise
    
    async def get_message(self, message_id: str) -> Optional[MessageThreadNode]:
        """Get message by ID."""
        return self.nodes.get(message_id)
    
    async def get_conversation_messages(self, conversation_id: str) -> List[MessageThreadNode]:
        """Get all messages in a conversation."""
        messages = []
        root_ids = self.conversation_roots.get(conversation_id, [])
        
        for root_id in root_ids:
            messages.extend(await self._get_subtree_messages(root_id))
        
        # Sort by creation time
        messages.sort(key=lambda x: x.created_at)
        return messages
    
    async def get_branch_from_message(self, message_id: str) -> List[MessageThreadNode]:
        """Get message branch starting from specific message."""
        branch_messages = []
        
        # Get the starting message
        start_message = await self.get_message(message_id)
        if not start_message:
            return branch_messages
        
        # Traverse up to find the root
        current_message = start_message
        path_to_root = [current_message]
        
        while current_message.parent_message_id:
            parent = await self.get_message(current_message.parent_message_id)
            if parent:
                path_to_root.insert(0, parent)
                current_message = parent
            else:
                break
        
        return path_to_root
    
    async def _get_subtree_messages(self, root_id: str) -> List[MessageThreadNode]:
        """Get all messages in a subtree."""
        messages = []
        root_node = self.nodes.get(root_id)
        
        if root_node:
            messages.append(root_node)
            
            # Recursively get children
            for child_id in root_node.children:
                child_messages = await self._get_subtree_messages(child_id)
                messages.extend(child_messages)
        
        return messages
    
    async def update_message_status(self, message_id: str, status: MessageStatus) -> bool:
        """Update message status."""
        try:
            message = self.nodes.get(message_id)
            if message:
                message.message_status = status
                message.updated_at = datetime.now()
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"Error updating message status: {e}")
            return False


class EditHistory:
    """Manages message edit history."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.edit_records: Dict[str, EditRecord] = {}
        self.message_edits: Dict[str, List[str]] = {}  # message_id -> list of edit_ids
    
    async def add_edit(self, edit_record: EditRecord) -> None:
        """Add edit record."""
        try:
            # Store edit record
            self.edit_records[edit_record.edit_id] = edit_record
            
            # Update message edit tracking
            original_id = edit_record.original_message_id
            if original_id not in self.message_edits:
                self.message_edits[original_id] = []
            self.message_edits[original_id].append(edit_record.edit_id)
            
            self.logger.debug(f"Added edit record: {edit_record.edit_id}")
            
        except Exception as e:
            self.logger.error(f"Error adding edit record: {e}")
            raise
    
    async def get_message_edit_history(self, message_id: str) -> List[EditRecord]:
        """Get edit history for a message."""
        edit_ids = self.message_edits.get(message_id, [])
        return [self.edit_records[edit_id] for edit_id in edit_ids if edit_id in self.edit_records]
    
    async def get_edit_record(self, edit_id: str) -> Optional[EditRecord]:
        """Get specific edit record."""
        return self.edit_records.get(edit_id)


class ResubmissionHandler:
    """Handles conversation resubmission and branching."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.resubmissions: Dict[str, ResubmissionContext] = {}
    
    async def process_resubmission(self, resubmission_context: ResubmissionContext) -> str:
        """Process conversation resubmission."""
        try:
            # Store resubmission context
            self.resubmissions[resubmission_context.resubmission_id] = resubmission_context
            
            # Create new conversation for resubmission
            new_conversation_id = str(uuid.uuid4())
            
            # Copy branch messages to new conversation
            await self._copy_branch_to_new_conversation(
                resubmission_context.branch_messages,
                new_conversation_id
            )
            
            self.logger.info(f"Processed resubmission: {resubmission_context.resubmission_id}")
            return new_conversation_id
            
        except Exception as e:
            self.logger.error(f"Error processing resubmission: {e}")
            raise
    
    async def _copy_branch_to_new_conversation(
        self, 
        branch_messages: List[MessageThreadNode],
        new_conversation_id: str
    ) -> None:
        """Copy branch messages to new conversation."""
        # Implementation would copy messages with new IDs
        # and update conversation references
        pass


class HierarchicalMessageManager:
    """
    Manages hierarchical message structure with editing and resubmission capabilities.
    
    Features:
    - Hierarchical message threading
    - Message editing with history tracking
    - Conversation branching and resubmission
    - Message status management
    - Audit trail and versioning
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.message_tree = MessageTree()
        self.edit_history = EditHistory()
        self.resubmission_handler = ResubmissionHandler()
        
        # Database integration (would be implemented)
        self.db_manager = None

    async def initialize(self) -> None:
        """Initialize the hierarchical message manager."""
        try:
            self.logger.info("Initializing HierarchicalMessageManager...")

            # Initialize sub-components
            if hasattr(self.message_tree, 'initialize'):
                await self.message_tree.initialize()

            if hasattr(self.edit_history, 'initialize'):
                await self.edit_history.initialize()

            if hasattr(self.resubmission_handler, 'initialize'):
                await self.resubmission_handler.initialize()

            self.logger.info("HierarchicalMessageManager initialized successfully")
        except Exception as e:
            self.logger.error(f"Error initializing HierarchicalMessageManager: {e}")
            raise

    async def create_message_thread(
        self, 
        conversation_id: str, 
        message_content: Dict[str, Any],
        message_type: MessageType,
        created_by: str,
        parent_message_id: Optional[str] = None
    ) -> str:
        """Create new message thread in conversation hierarchy."""
        try:
            thread_id = str(uuid.uuid4())
            
            # Create thread node
            thread_node = MessageThreadNode(
                thread_id=thread_id,
                conversation_id=conversation_id,
                parent_message_id=parent_message_id,
                message_content=message_content,
                message_type=message_type,
                message_status=MessageStatus.ACTIVE,
                created_by=created_by,
                created_at=datetime.now()
            )
            
            # Add to message tree
            await self.message_tree.add_thread(thread_node)
            
            # Persist to database if available
            if self.db_manager:
                await self.db_manager.save_message_thread(thread_node)
            
            self.logger.info(f"Created message thread: {thread_id}")
            return thread_id
            
        except Exception as e:
            self.logger.error(f"Error creating message thread: {e}")
            raise
    
    async def edit_message(
        self, 
        message_id: str, 
        new_content: Dict[str, Any],
        user_id: str,
        edit_reason: Optional[str] = None
    ) -> str:
        """Edit existing message and create new branch."""
        try:
            # Get original message
            original_message = await self.message_tree.get_message(message_id)
            if not original_message:
                raise ValueError(f"Message not found: {message_id}")
            
            # Create new message with edited content
            new_message_id = await self.create_message_thread(
                conversation_id=original_message.conversation_id,
                message_content=new_content,
                message_type=MessageType.EDIT_MESSAGE,
                created_by=user_id,
                parent_message_id=original_message.parent_message_id
            )
            
            # Create edit record
            edit_record = EditRecord(
                edit_id=str(uuid.uuid4()),
                original_message_id=message_id,
                edited_message_id=new_message_id,
                edit_type="content_edit",
                edited_by=user_id,
                edited_at=datetime.now(),
                edit_reason=edit_reason,
                diff_data=self._calculate_diff(original_message.message_content, new_content)
            )
            
            # Store edit history
            await self.edit_history.add_edit(edit_record)
            
            # Update original message status
            await self.message_tree.update_message_status(message_id, MessageStatus.EDITED)
            
            self.logger.info(f"Edited message {message_id}, created new message {new_message_id}")
            return new_message_id
            
        except Exception as e:
            self.logger.error(f"Error editing message: {e}")
            raise
    
    async def resubmit_conversation_branch(
        self, 
        message_id: str,
        user_id: str,
        workflow_context: Dict[str, Any],
        resubmission_reason: Optional[str] = None
    ) -> str:
        """Resubmit conversation from specific message point."""
        try:
            # Get message branch
            message_branch = await self.message_tree.get_branch_from_message(message_id)
            if not message_branch:
                raise ValueError(f"No branch found for message: {message_id}")
            
            # Create resubmission context
            resubmission_context = ResubmissionContext(
                resubmission_id=str(uuid.uuid4()),
                original_conversation_id=message_branch[0].conversation_id,
                branch_point_message_id=message_id,
                branch_messages=message_branch,
                original_workflow_context=workflow_context,
                resubmission_time=datetime.now(),
                resubmitted_by=user_id,
                resubmission_reason=resubmission_reason
            )
            
            # Process resubmission
            new_conversation_id = await self.resubmission_handler.process_resubmission(
                resubmission_context
            )
            
            self.logger.info(f"Resubmitted conversation branch from {message_id}")
            return new_conversation_id
            
        except Exception as e:
            self.logger.error(f"Error resubmitting conversation branch: {e}")
            raise
    
    async def get_conversation_history(self, conversation_id: str) -> List[MessageThreadNode]:
        """Get complete conversation history."""
        return await self.message_tree.get_conversation_messages(conversation_id)
    
    async def get_message_edit_history(self, message_id: str) -> List[EditRecord]:
        """Get edit history for a message."""
        return await self.edit_history.get_message_edit_history(message_id)
    
    async def delete_message(self, message_id: str, user_id: str) -> bool:
        """Soft delete a message."""
        try:
            success = await self.message_tree.update_message_status(message_id, MessageStatus.DELETED)
            if success:
                self.logger.info(f"Deleted message {message_id} by user {user_id}")
            return success
            
        except Exception as e:
            self.logger.error(f"Error deleting message: {e}")
            return False
    
    def _calculate_diff(self, original: Dict[str, Any], edited: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate difference between original and edited content."""
        diff = {
            "added": {},
            "removed": {},
            "modified": {}
        }
        
        # Simple diff implementation
        for key, value in edited.items():
            if key not in original:
                diff["added"][key] = value
            elif original[key] != value:
                diff["modified"][key] = {"old": original[key], "new": value}
        
        for key, value in original.items():
            if key not in edited:
                diff["removed"][key] = value
        
        return diff


# Global instance
hierarchical_message_manager = HierarchicalMessageManager()
