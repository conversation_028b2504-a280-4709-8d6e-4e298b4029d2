"""
Unified Persona Adapter

This module provides the core adapter for the unified persona system.
No legacy compatibility - pure unified persona system only.
"""

import logging
from typing import Dict, Any, Optional, List
from pathlib import Path

from ..config.dynamic_config_loader import get_dynamic_config_loader
from ..nodes.unified_persona_node import UnifiedPersonaNode, create_unified_persona_node
from ..states.unified_datagenius_state import UnifiedDatageniusState

logger = logging.getLogger(__name__)


class UnifiedPersonaAdapter:
    """
    Adapter for the unified persona system.
    Provides seamless integration for all persona types through configuration.
    """
    
    def __init__(self):
        """Initialize the unified persona adapter."""
        self.logger = logging.getLogger(__name__)
        
        # Unified persona mappings
        self.persona_mappings = {
            "analysis": {
                "persona_type": "analysis",
                "config_file": "personas/analysis.yaml"
            },
            "marketing": {
                "persona_type": "marketing",
                "config_file": "personas/marketing.yaml"
            },
            "concierge": {
                "persona_type": "concierge",
                "config_file": "personas/concierge.yaml"
            },
            "classification": {
                "persona_type": "classification",
                "config_file": "personas/classification.yaml"
            }
        }
        
        # Cache for created persona nodes
        self.persona_cache: Dict[str, UnifiedPersonaNode] = {}
        
        # Configuration loader
        self.config_loader = None
        
        self.logger.info("Initialized UnifiedPersonaAdapter")
    
    async def initialize(self) -> None:
        """Initialize the persona adapter."""
        try:
            self.config_loader = await get_dynamic_config_loader()
            self.logger.info("UnifiedPersonaAdapter initialized successfully")
        except Exception as e:
            self.logger.error(f"Error initializing UnifiedPersonaAdapter: {e}")
            raise
    
    def is_unified_persona(self, persona_id: str) -> bool:
        """Check if a persona ID corresponds to a unified persona."""
        return persona_id in self.persona_mappings
    
    def get_persona_type(self, persona_id: str) -> Optional[str]:
        """Get the persona type for a persona ID."""
        mapping = self.persona_mappings.get(persona_id)
        return mapping["persona_type"] if mapping else None
    
    def get_config_file(self, persona_id: str) -> Optional[str]:
        """Get the config file path for a persona ID."""
        mapping = self.persona_mappings.get(persona_id)
        return mapping["config_file"] if mapping else None
    
    async def create_persona_node(self, persona_id: str) -> Optional[UnifiedPersonaNode]:
        """
        Create a unified persona node for the given persona ID.
        
        Args:
            persona_id: Persona identifier
            
        Returns:
            UnifiedPersonaNode instance or None if creation fails
        """
        try:
            # Check cache first
            if persona_id in self.persona_cache:
                return self.persona_cache[persona_id]
            
            if not self.config_loader:
                await self.initialize()
            
            # Get persona type
            persona_type = self.get_persona_type(persona_id)
            if not persona_type:
                self.logger.error(f"Unknown persona ID: {persona_id}")
                return None
            
            # Create unified persona node
            persona_node = await create_unified_persona_node(persona_type)
            
            # Cache the node
            self.persona_cache[persona_id] = persona_node
            
            self.logger.info(f"Created unified persona node for {persona_id}")
            return persona_node
            
        except Exception as e:
            self.logger.error(f"Error creating persona node for {persona_id}: {e}")
            return None
    
    async def get_persona_capabilities(self, persona_id: str) -> List[str]:
        """
        Get capabilities for a persona from its configuration.
        
        Args:
            persona_id: Persona identifier
            
        Returns:
            List of capability strings
        """
        try:
            if not self.config_loader:
                await self.initialize()
            
            persona_type = self.get_persona_type(persona_id)
            if not persona_type:
                return []
            
            config = await self.config_loader.load_persona_config(persona_type)
            return config.get("capabilities", [])
            
        except Exception as e:
            self.logger.error(f"Error getting capabilities for {persona_id}: {e}")
            return []
    
    async def get_persona_tools(self, persona_id: str) -> List[str]:
        """
        Get tools for a persona from its configuration.
        
        Args:
            persona_id: Persona identifier
            
        Returns:
            List of tool names
        """
        try:
            if not self.config_loader:
                await self.initialize()
            
            persona_type = self.get_persona_type(persona_id)
            if not persona_type:
                return []
            
            config = await self.config_loader.load_persona_config(persona_type)
            return config.get("tools", [])
            
        except Exception as e:
            self.logger.error(f"Error getting tools for {persona_id}: {e}")
            return []
    
    async def process_message(
        self,
        persona_id: str,
        user_id: int,
        message: str,
        conversation_id: str,
        business_profile_id: Optional[int] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process a message using the unified persona system.
        
        Args:
            persona_id: Persona identifier
            user_id: User ID
            message: User message
            conversation_id: Conversation ID
            business_profile_id: Optional business profile ID
            context: Optional additional context
            
        Returns:
            Response dictionary
        """
        try:
            # Create persona node
            persona_node = await self.create_persona_node(persona_id)
            if not persona_node:
                return {
                    "message": f"Persona {persona_id} is not available.",
                    "success": False,
                    "error": "persona_not_found"
                }
            
            # Create initial state
            state = UnifiedDatageniusState(
                user_id=user_id,
                conversation_id=conversation_id,
                business_profile_id=business_profile_id,
                messages=[{"role": "user", "content": message}],
                current_agent=persona_id,
                context=context or {}
            )
            
            # Process with persona node
            result_state = await persona_node.process(state)
            
            # Convert to response format
            return self._convert_to_response(result_state, persona_id)
            
        except Exception as e:
            self.logger.error(f"Error processing message for {persona_id}: {e}")
            return {
                "message": "I encountered an error while processing your request. Please try again.",
                "success": False,
                "error": str(e)
            }
    
    def _convert_to_response(
        self,
        state: UnifiedDatageniusState,
        persona_id: str
    ) -> Dict[str, Any]:
        """
        Convert unified state to response format.
        
        Args:
            state: Unified state
            persona_id: Persona identifier
            
        Returns:
            Response dictionary
        """
        try:
            # Get the last assistant message
            assistant_messages = [
                msg for msg in state.messages 
                if msg.get("role") == "assistant"
            ]
            
            response_message = "I'm ready to help you!"
            if assistant_messages:
                response_message = assistant_messages[-1].get("content", response_message)
            
            return {
                "message": response_message,
                "success": True,
                "persona_id": persona_id,
                "conversation_id": state.conversation_id,
                "context": state.context,
                "tools_used": state.tools_used,
                "metadata": {
                    "processing_time": getattr(state, 'processing_time', None),
                    "tokens_used": getattr(state, 'tokens_used', None),
                    "persona_type": self.get_persona_type(persona_id)
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error converting state to response: {e}")
            return {
                "message": "Response processing completed.",
                "success": True,
                "persona_id": persona_id,
                "error": f"Response conversion error: {str(e)}"
            }
    
    async def get_available_personas(self) -> List[Dict[str, Any]]:
        """
        Get list of available personas.
        
        Returns:
            List of persona information dictionaries
        """
        try:
            personas = []
            for persona_id, mapping in self.persona_mappings.items():
                capabilities = await self.get_persona_capabilities(persona_id)
                tools = await self.get_persona_tools(persona_id)
                
                personas.append({
                    "persona_id": persona_id,
                    "persona_type": mapping["persona_type"],
                    "config_file": mapping["config_file"],
                    "capabilities": capabilities,
                    "tools": tools
                })
            
            return personas
            
        except Exception as e:
            self.logger.error(f"Error getting available personas: {e}")
            return []


# Global adapter instance
_unified_persona_adapter: Optional[UnifiedPersonaAdapter] = None


async def get_unified_persona_adapter() -> UnifiedPersonaAdapter:
    """Get the global unified persona adapter instance."""
    global _unified_persona_adapter
    
    if _unified_persona_adapter is None:
        _unified_persona_adapter = UnifiedPersonaAdapter()
        await _unified_persona_adapter.initialize()

    return _unified_persona_adapter
