"""Add hierarchical message IDs

Revision ID: add_hierarchical_message_ids
Revises: previous_revision
Create Date: 2024-01-23 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_hierarchical_message_ids'
down_revision = 'add_industry_selection_fields'
branch_labels = None
depends_on = None


def upgrade():
    """Add hierarchical message ID columns to messages table."""
    # Add new columns for hierarchical messaging (nullable first)
    op.add_column('messages', sa.Column('parent_message_id', sa.String(36), nullable=True))
    op.add_column('messages', sa.Column('thread_id', sa.String(36), nullable=True))
    op.add_column('messages', sa.Column('message_sequence', sa.Integer(), nullable=True))
    op.add_column('messages', sa.Column('thread_sequence', sa.Integer(), nullable=True))

    # Add columns for message editing
    op.add_column('messages', sa.Column('is_edited', sa.<PERSON>(), nullable=True))
    op.add_column('messages', sa.Column('original_message_id', sa.String(36), nullable=True))
    op.add_column('messages', sa.Column('edit_timestamp', sa.DateTime(timezone=True), nullable=True))
    op.add_column('messages', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    
    # Create indexes for performance
    op.create_index('ix_messages_parent_message_id', 'messages', ['parent_message_id'])
    op.create_index('ix_messages_thread_id', 'messages', ['thread_id'])
    op.create_index('ix_messages_message_sequence', 'messages', ['message_sequence'])
    op.create_index('ix_messages_thread_sequence', 'messages', ['thread_sequence'])
    op.create_index('ix_messages_original_message_id', 'messages', ['original_message_id'])
    
    # Create foreign key constraints
    op.create_foreign_key('fk_messages_parent_message_id', 'messages', 'messages', ['parent_message_id'], ['id'])
    
    # Populate existing messages with sequence numbers and default values
    connection = op.get_bind()

    # First, set default values for all existing messages
    connection.execute(sa.text("""
        UPDATE messages
        SET
            message_sequence = 0,
            thread_sequence = 0,
            is_edited = false,
            updated_at = created_at
        WHERE message_sequence IS NULL
    """))

    # Get all conversations and populate message sequences
    conversations = connection.execute(sa.text("SELECT DISTINCT conversation_id FROM messages")).fetchall()

    for conversation in conversations:
        conversation_id = conversation[0]

        # Get messages for this conversation ordered by created_at
        messages = connection.execute(sa.text("""
            SELECT id, created_at FROM messages
            WHERE conversation_id = :conversation_id
            ORDER BY created_at ASC
        """), {"conversation_id": conversation_id}).fetchall()

        # Update message sequences
        for i, message in enumerate(messages, 1):
            message_id = message[0]

            # Update message sequence and set thread_id to message_id (each message is its own thread initially)
            connection.execute(sa.text("""
                UPDATE messages
                SET message_sequence = :sequence, thread_id = :thread_id, thread_sequence = 0
                WHERE id = :message_id
            """), {
                "sequence": i,
                "thread_id": message_id,
                "message_id": message_id
            })

    connection.commit()

    # Now make the columns NOT NULL with proper defaults
    op.alter_column('messages', 'message_sequence', nullable=False, server_default='0')
    op.alter_column('messages', 'thread_sequence', nullable=False, server_default='0')
    op.alter_column('messages', 'is_edited', nullable=False, server_default='false')
    op.alter_column('messages', 'updated_at', nullable=False, server_default=sa.func.now())


def downgrade():
    """Remove hierarchical message ID columns from messages table."""
    # Drop foreign key constraints
    op.drop_constraint('fk_messages_parent_message_id', 'messages', type_='foreignkey')
    
    # Drop indexes
    op.drop_index('ix_messages_original_message_id', 'messages')
    op.drop_index('ix_messages_thread_sequence', 'messages')
    op.drop_index('ix_messages_message_sequence', 'messages')
    op.drop_index('ix_messages_thread_id', 'messages')
    op.drop_index('ix_messages_parent_message_id', 'messages')
    
    # Drop columns
    op.drop_column('messages', 'updated_at')
    op.drop_column('messages', 'edit_timestamp')
    op.drop_column('messages', 'original_message_id')
    op.drop_column('messages', 'is_edited')
    op.drop_column('messages', 'thread_sequence')
    op.drop_column('messages', 'message_sequence')
    op.drop_column('messages', 'thread_id')
    op.drop_column('messages', 'parent_message_id')
