"""
Database migration for enhanced marketplace architecture.

This migration creates the necessary tables for:
- Enhanced persona configurations
- Plugin registry and management
- Hierarchical message structure
- Message edit history
- Workflow adaptation tracking
"""

import logging
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

logger = logging.getLogger(__name__)


def upgrade():
    """Create enhanced marketplace tables."""
    
    # Enhanced persona configuration table
    op.create_table(
        'persona_configurations',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('persona_id', sa.String(255), nullable=False),
        sa.Column('configuration', postgresql.JSONB, nullable=False),
        sa.Column('industry_specialization', sa.String(100), nullable=True),
        sa.Column('methodology_framework', sa.String(100), nullable=True),
        sa.Column('enable_cross_agent_intelligence', sa.<PERSON>, default=True),
        sa.Column('specialized_tools', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('compliance_requirements', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('workflow_patterns', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('performance_optimization', postgresql.JSONB, default=sa.text("'{}'")),
        sa.Column('created_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.Column('updated_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.Index('idx_persona_configurations_persona_id', 'persona_id'),
        sa.Index('idx_persona_configurations_industry', 'industry_specialization')
    )
    
    # Plugin registry table
    op.create_table(
        'agent_plugins',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('plugin_id', sa.String(255), unique=True, nullable=False),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('version', sa.String(50), nullable=False),
        sa.Column('author', sa.String(255), nullable=True),
        sa.Column('description', sa.Text, nullable=True),
        sa.Column('plugin_package', postgresql.JSONB, nullable=False),
        sa.Column('security_validation', postgresql.JSONB, nullable=True),
        sa.Column('capability_validation', postgresql.JSONB, nullable=True),
        sa.Column('marketplace_listing_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('status', sa.String(50), default='pending'),
        sa.Column('supported_industries', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('required_tools', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('compliance_certifications', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('installation_config', postgresql.JSONB, default=sa.text("'{}'")),
        sa.Column('created_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.Column('updated_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.Index('idx_agent_plugins_plugin_id', 'plugin_id'),
        sa.Index('idx_agent_plugins_status', 'status'),
        sa.Index('idx_agent_plugins_author', 'author')
    )
    
    # Hierarchical message structure with LTREE support
    op.execute('CREATE EXTENSION IF NOT EXISTS ltree')
    
    op.create_table(
        'message_threads',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('conversation_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('parent_message_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('thread_path', sa.Text, nullable=True),  # LTREE path
        sa.Column('message_content', postgresql.JSONB, nullable=False),
        sa.Column('message_type', sa.String(50), nullable=False),
        sa.Column('message_status', sa.String(50), default='active'),
        sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.Column('updated_at', sa.TIMESTAMP, nullable=True),
        sa.Column('metadata', postgresql.JSONB, default=sa.text("'{}'")),
        sa.ForeignKeyConstraint(['conversation_id'], ['conversations.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['parent_message_id'], ['message_threads.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ondelete='CASCADE'),
        sa.Index('idx_message_threads_conversation_id', 'conversation_id'),
        sa.Index('idx_message_threads_parent_id', 'parent_message_id'),
        sa.Index('idx_message_threads_type', 'message_type'),
        sa.Index('idx_message_threads_status', 'message_status'),
        sa.Index('idx_message_threads_created_by', 'created_by'),
        sa.Index('idx_message_threads_path', 'thread_path', postgresql_using='gist')
    )
    
    # Message edit history
    op.create_table(
        'message_edit_history',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('edit_id', sa.String(255), unique=True, nullable=False),
        sa.Column('original_message_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('edited_message_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('edit_type', sa.String(50), nullable=False),
        sa.Column('edited_by', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('edited_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.Column('edit_reason', sa.Text, nullable=True),
        sa.Column('diff_data', postgresql.JSONB, default=sa.text("'{}'")),
        sa.ForeignKeyConstraint(['original_message_id'], ['message_threads.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['edited_message_id'], ['message_threads.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['edited_by'], ['users.id'], ondelete='CASCADE'),
        sa.Index('idx_message_edit_history_original_id', 'original_message_id'),
        sa.Index('idx_message_edit_history_edited_id', 'edited_message_id'),
        sa.Index('idx_message_edit_history_edited_by', 'edited_by'),
        sa.Index('idx_message_edit_history_edit_type', 'edit_type')
    )
    
    # Conversation resubmission tracking
    op.create_table(
        'conversation_resubmissions',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('resubmission_id', sa.String(255), unique=True, nullable=False),
        sa.Column('original_conversation_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('new_conversation_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('branch_point_message_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('resubmitted_by', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('resubmission_time', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.Column('resubmission_reason', sa.Text, nullable=True),
        sa.Column('original_workflow_context', postgresql.JSONB, default=sa.text("'{}'")),
        sa.ForeignKeyConstraint(['original_conversation_id'], ['conversations.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['new_conversation_id'], ['conversations.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['branch_point_message_id'], ['message_threads.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['resubmitted_by'], ['users.id'], ondelete='CASCADE'),
        sa.Index('idx_conversation_resubmissions_original_id', 'original_conversation_id'),
        sa.Index('idx_conversation_resubmissions_new_id', 'new_conversation_id'),
        sa.Index('idx_conversation_resubmissions_resubmitted_by', 'resubmitted_by')
    )
    
    # Industry templates
    op.create_table(
        'industry_templates',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('industry', sa.String(100), unique=True, nullable=False),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('description', sa.Text, nullable=True),
        sa.Column('specialized_nodes', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('workflow_patterns', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('compliance_requirements', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('data_requirements', postgresql.JSONB, default=sa.text("'{}'")),
        sa.Column('security_requirements', postgresql.JSONB, default=sa.text("'{}'")),
        sa.Column('performance_requirements', postgresql.JSONB, default=sa.text("'{}'")),
        sa.Column('created_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.Column('updated_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.Index('idx_industry_templates_industry', 'industry')
    )
    
    # Workflow adaptation tracking
    op.create_table(
        'workflow_adaptations',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('adaptation_id', sa.String(255), unique=True, nullable=False),
        sa.Column('workflow_id', sa.String(255), nullable=False),
        sa.Column('persona_id', sa.String(255), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('adaptation_type', sa.String(100), nullable=False),
        sa.Column('adaptation_triggers', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('performance_data', postgresql.JSONB, default=sa.text("'{}'")),
        sa.Column('adaptation_config', postgresql.JSONB, default=sa.text("'{}'")),
        sa.Column('success_metrics', postgresql.JSONB, default=sa.text("'{}'")),
        sa.Column('created_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.Index('idx_workflow_adaptations_workflow_id', 'workflow_id'),
        sa.Index('idx_workflow_adaptations_persona_id', 'persona_id'),
        sa.Index('idx_workflow_adaptations_user_id', 'user_id'),
        sa.Index('idx_workflow_adaptations_type', 'adaptation_type')
    )
    
    # Workflow experiments (A/B testing)
    op.create_table(
        'workflow_experiments',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('experiment_id', sa.String(255), unique=True, nullable=False),
        sa.Column('experiment_name', sa.String(255), nullable=False),
        sa.Column('description', sa.Text, nullable=True),
        sa.Column('control_workflow_config', postgresql.JSONB, nullable=False),
        sa.Column('variation_workflows_config', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('traffic_split', postgresql.JSONB, default=sa.text("'{}'")),
        sa.Column('success_metrics', postgresql.JSONB, default=sa.text("'[]'")),
        sa.Column('experiment_status', sa.String(50), default='draft'),
        sa.Column('start_time', sa.TIMESTAMP, nullable=True),
        sa.Column('end_time', sa.TIMESTAMP, nullable=True),
        sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.Column('updated_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ondelete='CASCADE'),
        sa.Index('idx_workflow_experiments_experiment_id', 'experiment_id'),
        sa.Index('idx_workflow_experiments_status', 'experiment_status'),
        sa.Index('idx_workflow_experiments_created_by', 'created_by')
    )
    
    # Experiment participation tracking
    op.create_table(
        'experiment_participations',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('experiment_id', sa.String(255), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('variation_assigned', sa.String(100), nullable=False),
        sa.Column('assignment_time', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.Column('metrics_data', postgresql.JSONB, default=sa.text("'{}'")),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.Index('idx_experiment_participations_experiment_id', 'experiment_id'),
        sa.Index('idx_experiment_participations_user_id', 'user_id'),
        sa.Index('idx_experiment_participations_variation', 'variation_assigned'),
        sa.UniqueConstraint('experiment_id', 'user_id', name='uq_experiment_user')
    )
    
    # Performance metrics tracking
    op.create_table(
        'workflow_performance_metrics',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('workflow_id', sa.String(255), nullable=False),
        sa.Column('persona_id', sa.String(255), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('conversation_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('execution_time', sa.Float, nullable=True),
        sa.Column('memory_usage', sa.Float, nullable=True),
        sa.Column('success_rate', sa.Float, nullable=True),
        sa.Column('user_satisfaction', sa.Float, nullable=True),
        sa.Column('business_relevance', sa.Float, nullable=True),
        sa.Column('custom_metrics', postgresql.JSONB, default=sa.text("'{}'")),
        sa.Column('recorded_at', sa.TIMESTAMP, default=sa.func.current_timestamp()),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['conversation_id'], ['conversations.id'], ondelete='CASCADE'),
        sa.Index('idx_workflow_performance_workflow_id', 'workflow_id'),
        sa.Index('idx_workflow_performance_persona_id', 'persona_id'),
        sa.Index('idx_workflow_performance_user_id', 'user_id'),
        sa.Index('idx_workflow_performance_recorded_at', 'recorded_at')
    )
    
    logger.info("Enhanced marketplace tables created successfully")


def downgrade():
    """Drop enhanced marketplace tables."""
    
    # Drop tables in reverse order to handle foreign key constraints
    op.drop_table('workflow_performance_metrics')
    op.drop_table('experiment_participations')
    op.drop_table('workflow_experiments')
    op.drop_table('workflow_adaptations')
    op.drop_table('industry_templates')
    op.drop_table('conversation_resubmissions')
    op.drop_table('message_edit_history')
    op.drop_table('message_threads')
    op.drop_table('agent_plugins')
    op.drop_table('persona_configurations')
    
    # Drop LTREE extension if no other tables use it
    op.execute('DROP EXTENSION IF EXISTS ltree')
    
    logger.info("Enhanced marketplace tables dropped successfully")
