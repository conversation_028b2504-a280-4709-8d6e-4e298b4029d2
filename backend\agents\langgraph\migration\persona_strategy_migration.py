"""
Migration script for transitioning from hardcoded persona strategies to configuration-driven system.

This script helps migrate existing implementations to use the new ConfigurablePersonaStrategy
system with YAML configurations.
"""

import logging
import os
import yaml
from typing import Dict, Any, List
from pathlib import Path

logger = logging.getLogger(__name__)


class PersonaStrategyMigrationTool:
    """Tool to help migrate from hardcoded strategies to configuration-driven system."""

    def __init__(self, config_dir: str = "backend/agents/langgraph/config/personas"):
        """
        Initialize the migration tool.
        
        Args:
            config_dir: Directory containing persona configurations
        """
        self.config_dir = Path(config_dir)
        self.migration_log = []

    def validate_migration(self) -> Dict[str, Any]:
        """
        Validate that the migration to configuration-driven system is complete.
        
        Returns:
            Validation results
        """
        results = {
            "status": "success",
            "issues": [],
            "warnings": [],
            "personas_validated": 0,
            "configurations_found": []
        }

        try:
            # Check that all persona configurations use ConfigurablePersonaStrategy
            persona_configs = self._load_all_persona_configs()
            
            for persona_id, config in persona_configs.items():
                results["personas_validated"] += 1
                results["configurations_found"].append(persona_id)
                
                # Validate strategy class
                strategy_class = config.get("strategy_class")
                if strategy_class != "agents.langgraph.strategies.extensible_strategy_system.ConfigurablePersonaStrategy":
                    results["issues"].append(
                        f"Persona '{persona_id}' uses incorrect strategy class: {strategy_class}"
                    )
                
                # Validate required configuration sections
                required_sections = [
                    "llm_config",
                    "prompt_templates", 
                    "response_templates",
                    "processing_rules"
                ]
                
                for section in required_sections:
                    if section not in config:
                        results["issues"].append(
                            f"Persona '{persona_id}' missing required section: {section}"
                        )
                
                # Validate processing pipeline
                processing_rules = config.get("processing_rules", {})
                pipeline = processing_rules.get("processing_pipeline", [])
                
                if not pipeline:
                    results["warnings"].append(
                        f"Persona '{persona_id}' has empty processing pipeline"
                    )
                else:
                    # Check for response generation step
                    has_response_generation = any(
                        step.get("type") == "response_generation" 
                        for step in pipeline
                    )
                    
                    if not has_response_generation:
                        results["issues"].append(
                            f"Persona '{persona_id}' missing response_generation step in pipeline"
                        )

            # Check for any remaining hardcoded strategy references
            hardcoded_references = self._check_for_hardcoded_references()
            if hardcoded_references:
                results["issues"].extend(hardcoded_references)

            if results["issues"]:
                results["status"] = "failed"
            elif results["warnings"]:
                results["status"] = "warning"

        except Exception as e:
            results["status"] = "error"
            results["issues"].append(f"Migration validation failed: {str(e)}")

        return results

    def _load_all_persona_configs(self) -> Dict[str, Dict[str, Any]]:
        """Load all persona configurations from the config directory."""
        configs = {}
        
        if not self.config_dir.exists():
            raise FileNotFoundError(f"Config directory not found: {self.config_dir}")
        
        for config_file in self.config_dir.glob("*.yaml"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    persona_id = config.get("persona_id", config_file.stem)
                    configs[persona_id] = config
            except Exception as e:
                logger.error(f"Error loading config {config_file}: {e}")
        
        return configs

    def _check_for_hardcoded_references(self) -> List[str]:
        """Check for any remaining hardcoded strategy class references."""
        issues = []
        
        # Define hardcoded strategy class names to search for
        hardcoded_classes = [
            "ConciergePersonaStrategy",
            "AnalysisPersonaStrategy", 
            "MarketingPersonaStrategy",
            "ClassificationPersonaStrategy",
            "DefaultPersonaStrategy"
        ]
        
        # Search in key directories
        search_dirs = [
            "backend/agents/langgraph/nodes",
            "backend/agents/langgraph/strategies",
            "backend/agents/langgraph/tests"
        ]
        
        for search_dir in search_dirs:
            search_path = Path(search_dir)
            if search_path.exists():
                for py_file in search_path.rglob("*.py"):
                    try:
                        with open(py_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                        for class_name in hardcoded_classes:
                            if class_name in content:
                                # Skip if it's in a comment or test that expects it to not exist
                                lines = content.split('\n')
                                for i, line in enumerate(lines):
                                    if class_name in line and not line.strip().startswith('#'):
                                        # Check if it's in a test that expects AttributeError
                                        if "pytest.raises(AttributeError)" not in lines[max(0, i-3):i+3]:
                                            issues.append(
                                                f"Found hardcoded reference to {class_name} in {py_file}:{i+1}"
                                            )
                    except Exception as e:
                        logger.warning(f"Could not check file {py_file}: {e}")
        
        return issues

    def generate_migration_report(self) -> str:
        """Generate a comprehensive migration report."""
        validation_results = self.validate_migration()
        
        report = []
        report.append("=" * 80)
        report.append("PERSONA STRATEGY MIGRATION REPORT")
        report.append("=" * 80)
        report.append("")
        
        # Status summary
        status = validation_results["status"]
        report.append(f"Migration Status: {status.upper()}")
        report.append(f"Personas Validated: {validation_results['personas_validated']}")
        report.append("")
        
        # Configurations found
        if validation_results["configurations_found"]:
            report.append("Persona Configurations Found:")
            for persona_id in validation_results["configurations_found"]:
                report.append(f"  ✓ {persona_id}")
            report.append("")
        
        # Issues
        if validation_results["issues"]:
            report.append("ISSUES FOUND:")
            for issue in validation_results["issues"]:
                report.append(f"  ❌ {issue}")
            report.append("")
        
        # Warnings
        if validation_results["warnings"]:
            report.append("WARNINGS:")
            for warning in validation_results["warnings"]:
                report.append(f"  ⚠️  {warning}")
            report.append("")
        
        # Migration success
        if status == "success":
            report.append("✅ MIGRATION COMPLETED SUCCESSFULLY!")
            report.append("")
            report.append("All personas are now using the configuration-driven system:")
            report.append("- ConfigurablePersonaStrategy for all persona types")
            report.append("- YAML-based configuration with LLM integration")
            report.append("- Response generation through processing pipelines")
            report.append("- No hardcoded strategy classes remaining")
            report.append("")
            report.append("Benefits achieved:")
            report.append("- Eliminated infinite loops from incomplete hardcoded strategies")
            report.append("- Maintainable system where new personas are added through configuration")
            report.append("- Consistent LLM integration across all personas")
            report.append("- Extensible processing pipelines")
        
        report.append("=" * 80)
        
        return "\n".join(report)

    def run_migration_validation(self) -> bool:
        """
        Run the complete migration validation and print results.
        
        Returns:
            True if migration is successful, False otherwise
        """
        print("Running persona strategy migration validation...")
        print()
        
        report = self.generate_migration_report()
        print(report)
        
        validation_results = self.validate_migration()
        return validation_results["status"] == "success"


def main():
    """Main function to run migration validation."""
    migration_tool = PersonaStrategyMigrationTool()
    success = migration_tool.run_migration_validation()
    
    if success:
        print("\n🎉 Migration validation passed! The system is ready to use.")
        return 0
    else:
        print("\n❌ Migration validation failed. Please address the issues above.")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
