"""
MCP Security Module for Datagenius.

This module provides comprehensive security validation, sandboxing,
and access controls for MCP server integrations.
"""

import json
import logging
import re
from typing import Dict, Any, List, Optional, Tuple
from urllib.parse import urlparse
import ipaddress

from ..models.mcp_server import MCP<PERSON><PERSON><PERSON>onfiguration, MCPServerDefinition
from .. import config

logger = logging.getLogger(__name__)


class MCPSecurityValidator:
    """
    Security validator for MCP server configurations and operations.
    
    Provides validation, sandboxing, and access controls for MCP integrations
    to prevent security vulnerabilities and unauthorized access.
    """
    
    def __init__(self):
        """Initialize the MCP security validator."""
        self.blocked_domains = self._load_blocked_domains()
        self.blocked_ips = self._load_blocked_ips()
        self.allowed_commands = self._load_allowed_commands()
        self.max_execution_time = getattr(config, 'MCP_MAX_EXECUTION_TIME', 30)
        self.max_memory_mb = getattr(config, 'MCP_MAX_MEMORY_MB', 512)
        self.sandbox_enabled = getattr(config, 'MCP_SANDBOX_ENABLED', True)
    
    def _load_blocked_domains(self) -> List[str]:
        """Load blocked domains from configuration."""
        default_blocked = [
            'localhost',
            '127.0.0.1',
            '0.0.0.0',
            'internal',
            'admin',
            'root'
        ]
        return getattr(config, 'MCP_BLOCKED_DOMAINS', default_blocked)
    
    def _load_blocked_ips(self) -> List[str]:
        """Load blocked IP ranges from configuration."""
        return getattr(config, 'MCP_BLOCKED_IPS', [
            '*********/8',      # Loopback
            '10.0.0.0/8',       # Private Class A
            '**********/12',    # Private Class B
            '***********/16',   # Private Class C
            '***********/16',   # Link-local
            '*********/4',      # Multicast
            '240.0.0.0/4'       # Reserved
        ])
    
    def _load_allowed_commands(self) -> List[str]:
        """Load allowed commands for stdio servers."""
        return getattr(config, 'MCP_ALLOWED_COMMANDS', [
            'node',
            'npx',
            'python',
            'python3',
            'pip',
            'npm',
            'yarn',
            'docker'
        ])
    
    async def validate_server_configuration(
        self, 
        config: MCPJSONConfiguration,
        user_id: int
    ) -> Tuple[bool, List[str]]:
        """
        Validate an MCP server configuration for security issues.
        
        Args:
            config: MCP server configuration
            user_id: User ID for authorization checks
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        
        try:
            # Validate input variables
            if config.inputs:
                input_errors = self._validate_input_variables(config.inputs)
                errors.extend(input_errors)
            
            # Validate server definitions
            for server_name, server_def in config.servers.items():
                server_errors = await self._validate_server_definition(server_name, server_def)
                errors.extend(server_errors)
            
            # Check for variable injection attacks
            config_str = json.dumps(config.model_dump())
            injection_errors = self._check_variable_injection(config_str)
            errors.extend(injection_errors)
            
            # Validate user permissions
            permission_errors = await self._validate_user_permissions(user_id, config)
            errors.extend(permission_errors)
            
            return len(errors) == 0, errors
            
        except Exception as e:
            logger.error(f"Error validating MCP configuration: {e}")
            return False, [f"Configuration validation failed: {str(e)}"]
    
    def _validate_input_variables(self, inputs: List[Dict[str, Any]]) -> List[str]:
        """Validate input variable definitions."""
        errors = []
        
        for input_var in inputs:
            var_id = input_var.get("id", "")
            var_type = input_var.get("type", "")
            
            # Validate variable ID
            if not re.match(r'^[a-zA-Z0-9_-]+$', var_id):
                errors.append(f"Invalid input variable ID: {var_id}")
            
            # Validate variable type
            if var_type not in ["promptString", "promptChoice", "env"]:
                errors.append(f"Invalid input variable type: {var_type}")
            
            # Check for suspicious descriptions
            description = input_var.get("description", "")
            if any(keyword in description.lower() for keyword in 
                   ["password", "secret", "key", "token", "credential"]):
                if not input_var.get("password", False):
                    errors.append(f"Sensitive input variable '{var_id}' should be marked as password")
        
        return errors
    
    async def _validate_server_definition(
        self, 
        server_name: str, 
        server_def: MCPServerDefinition
    ) -> List[str]:
        """Validate a server definition."""
        errors = []
        
        # Validate server name
        if not re.match(r'^[a-zA-Z0-9_-]+$', server_name):
            errors.append(f"Invalid server name: {server_name}")
        
        # Validate based on server type
        if server_def.type == "stdio":
            stdio_errors = await self._validate_stdio_server(server_def)
            errors.extend(stdio_errors)
        else:
            # HTTP/SSE/WebSocket servers
            url_errors = await self._validate_url_server(server_def)
            errors.extend(url_errors)
        
        # Validate environment variables
        if server_def.env:
            env_errors = self._validate_environment_variables(server_def.env)
            errors.extend(env_errors)
        
        return errors
    
    async def _validate_stdio_server(self, server_def: MCPServerDefinition) -> List[str]:
        """Validate stdio server configuration."""
        errors = []
        
        command = server_def.command
        if not command:
            errors.append("stdio server missing command")
            return errors
        
        # Check if command is allowed
        base_command = command.split()[0] if ' ' in command else command
        if base_command not in self.allowed_commands:
            errors.append(f"Command not allowed: {base_command}")
        
        # Validate arguments
        if server_def.args:
            for arg in server_def.args:
                if any(dangerous in str(arg) for dangerous in 
                       [';', '&&', '||', '|', '>', '<', '`', '$(']):
                    errors.append(f"Potentially dangerous argument: {arg}")
        
        # Check for command injection
        full_command = f"{command} {' '.join(server_def.args or [])}"
        if self._check_command_injection(full_command):
            errors.append("Potential command injection detected")
        
        return errors
    
    async def _validate_url_server(self, server_def: MCPServerDefinition) -> List[str]:
        """Validate URL-based server configuration."""
        errors = []
        
        url = server_def.url
        if not url:
            errors.append("URL server missing URL")
            return errors
        
        try:
            parsed_url = urlparse(url)
            
            # Validate scheme
            if parsed_url.scheme not in ['http', 'https', 'ws', 'wss']:
                errors.append(f"Invalid URL scheme: {parsed_url.scheme}")
            
            # Validate hostname
            hostname = parsed_url.hostname
            if hostname:
                # Check blocked domains
                if hostname.lower() in self.blocked_domains:
                    errors.append(f"Blocked domain: {hostname}")
                
                # Check IP addresses
                try:
                    ip = ipaddress.ip_address(hostname)
                    if self._is_blocked_ip(ip):
                        errors.append(f"Blocked IP address: {hostname}")
                except ValueError:
                    # Not an IP address, continue with domain validation
                    pass
            
            # Validate port
            port = parsed_url.port
            if port and (port < 1 or port > 65535):
                errors.append(f"Invalid port: {port}")
            
            # Check for suspicious URLs
            if any(suspicious in url.lower() for suspicious in 
                   ['admin', 'internal', 'localhost', '127.0.0.1']):
                errors.append(f"Suspicious URL detected: {url}")
                
        except Exception:
            errors.append(f"Invalid URL format: {url}")
        
        return errors
    
    def _validate_environment_variables(self, env_vars: Dict[str, str]) -> List[str]:
        """Validate environment variables."""
        errors = []
        
        dangerous_vars = ['PATH', 'LD_LIBRARY_PATH', 'PYTHONPATH', 'HOME', 'USER']
        
        for var_name, var_value in env_vars.items():
            # Check for dangerous environment variables
            if var_name.upper() in dangerous_vars:
                errors.append(f"Dangerous environment variable: {var_name}")
            
            # Check for command injection in values
            if self._check_command_injection(str(var_value)):
                errors.append(f"Potential command injection in env var {var_name}")
        
        return errors
    
    def _check_command_injection(self, command: str) -> bool:
        """Check for potential command injection."""
        dangerous_patterns = [
            r'[;&|`$()]',           # Shell metacharacters
            r'\.\./',               # Directory traversal
            r'/etc/',               # System directories
            r'/proc/',              # Process information
            r'rm\s+-rf',            # Dangerous commands
            r'sudo',                # Privilege escalation
            r'chmod',               # Permission changes
            r'chown',               # Ownership changes
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, command, re.IGNORECASE):
                return True
        
        return False
    
    def _check_variable_injection(self, config_str: str) -> List[str]:
        """Check for variable injection attacks."""
        errors = []
        
        # Look for suspicious variable references
        var_refs = re.findall(r'\$\{[^}]+\}', config_str)
        
        for var_ref in var_refs:
            # Check for command injection in variable references
            if any(dangerous in var_ref for dangerous in [';', '&&', '||', '`', '$(']):
                errors.append(f"Suspicious variable reference: {var_ref}")
        
        return errors
    
    def _is_blocked_ip(self, ip: ipaddress.IPv4Address) -> bool:
        """Check if an IP address is in blocked ranges."""
        for blocked_range in self.blocked_ips:
            try:
                network = ipaddress.ip_network(blocked_range, strict=False)
                if ip in network:
                    return True
            except ValueError:
                continue
        
        return False
    
    async def _validate_user_permissions(
        self, 
        user_id: int, 
        config: MCPJSONConfiguration
    ) -> List[str]:
        """Validate user permissions for the configuration."""
        errors = []
        
        # Check if user has permission to create MCP servers
        # This would integrate with your user permission system
        # For now, we'll do basic validation
        
        # Check server count limits
        server_count = len(config.servers)
        max_servers = getattr(config, 'MCP_MAX_SERVERS_PER_USER', 10)
        
        if server_count > max_servers:
            errors.append(f"Too many servers: {server_count} (max: {max_servers})")
        
        return errors
    
    async def validate_tool_execution(
        self, 
        tool_name: str, 
        arguments: Dict[str, Any],
        user_id: int
    ) -> Tuple[bool, Optional[str]]:
        """
        Validate tool execution request.
        
        Args:
            tool_name: Name of the tool to execute
            arguments: Tool arguments
            user_id: User ID
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Validate tool name
            if not re.match(r'^[a-zA-Z0-9_-]+$', tool_name):
                return False, f"Invalid tool name: {tool_name}"
            
            # Validate arguments
            if not isinstance(arguments, dict):
                return False, "Tool arguments must be a dictionary"
            
            # Check for dangerous argument values
            for key, value in arguments.items():
                if isinstance(value, str):
                    if self._check_command_injection(value):
                        return False, f"Dangerous value in argument {key}"
            
            # Check execution limits (this would integrate with rate limiting)
            # For now, just validate the request structure
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error validating tool execution: {e}")
            return False, f"Validation error: {str(e)}"
    
    def create_sandbox_config(self) -> Dict[str, Any]:
        """
        Create sandbox configuration for MCP server execution.
        
        Returns:
            Sandbox configuration dictionary
        """
        return {
            "enabled": self.sandbox_enabled,
            "max_execution_time": self.max_execution_time,
            "max_memory_mb": self.max_memory_mb,
            "network_access": "restricted",
            "file_system_access": "restricted",
            "allowed_directories": ["/tmp", "/var/tmp"],
            "blocked_directories": ["/etc", "/proc", "/sys", "/dev"],
            "environment_isolation": True,
            "resource_limits": {
                "cpu_percent": 50,
                "memory_mb": self.max_memory_mb,
                "disk_mb": 100,
                "network_connections": 10
            }
        }


class MCPErrorHandler:
    """
    Comprehensive error handler for MCP operations.
    
    Provides structured error handling, logging, and user feedback
    for MCP server operations and tool executions.
    """
    
    def __init__(self):
        """Initialize the MCP error handler."""
        self.logger = logging.getLogger(__name__)
        self.error_categories = {
            "configuration": "Configuration Error",
            "connection": "Connection Error", 
            "authentication": "Authentication Error",
            "authorization": "Authorization Error",
            "execution": "Execution Error",
            "timeout": "Timeout Error",
            "resource": "Resource Error",
            "validation": "Validation Error",
            "security": "Security Error"
        }
    
    def handle_configuration_error(
        self, 
        error: Exception, 
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Handle configuration-related errors."""
        return self._create_error_response(
            category="configuration",
            error=error,
            context=context,
            user_message="There was an issue with the MCP server configuration. Please check your settings and try again.",
            suggestions=[
                "Verify the server configuration format",
                "Check that all required fields are provided",
                "Ensure variable references are correct"
            ]
        )
    
    def handle_connection_error(
        self, 
        error: Exception, 
        server_info: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Handle connection-related errors."""
        return self._create_error_response(
            category="connection",
            error=error,
            context={"server_info": server_info},
            user_message="Unable to connect to the MCP server. Please check the server status and network connectivity.",
            suggestions=[
                "Verify the server URL or command is correct",
                "Check network connectivity",
                "Ensure the server is running and accessible",
                "Verify authentication credentials"
            ]
        )
    
    def handle_execution_error(
        self, 
        error: Exception, 
        tool_name: str = None,
        arguments: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Handle tool execution errors."""
        return self._create_error_response(
            category="execution",
            error=error,
            context={"tool_name": tool_name, "arguments": arguments},
            user_message=f"Failed to execute tool '{tool_name}'. Please check the tool parameters and try again.",
            suggestions=[
                "Verify tool parameters are correct",
                "Check that the tool is available and enabled",
                "Ensure you have permission to use this tool",
                "Try with different parameters"
            ]
        )
    
    def handle_security_error(
        self, 
        error: Exception, 
        security_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Handle security-related errors."""
        return self._create_error_response(
            category="security",
            error=error,
            context=security_context,
            user_message="Security validation failed. The operation was blocked for safety reasons.",
            suggestions=[
                "Review the configuration for security issues",
                "Remove any potentially dangerous commands or URLs",
                "Contact support if you believe this is an error"
            ]
        )
    
    def handle_timeout_error(
        self, 
        error: Exception, 
        operation: str = None,
        timeout_duration: int = None
    ) -> Dict[str, Any]:
        """Handle timeout errors."""
        return self._create_error_response(
            category="timeout",
            error=error,
            context={"operation": operation, "timeout_duration": timeout_duration},
            user_message=f"The operation timed out after {timeout_duration} seconds. Please try again or contact support.",
            suggestions=[
                "Try the operation again",
                "Check if the server is responding slowly",
                "Consider using simpler parameters",
                "Contact support if the issue persists"
            ]
        )
    
    def _create_error_response(
        self,
        category: str,
        error: Exception,
        context: Dict[str, Any] = None,
        user_message: str = None,
        suggestions: List[str] = None
    ) -> Dict[str, Any]:
        """Create a structured error response."""
        error_id = f"mcp_{category}_{hash(str(error)) % 10000:04d}"
        
        response = {
            "success": False,
            "error_id": error_id,
            "category": self.error_categories.get(category, "Unknown Error"),
            "message": user_message or "An error occurred during MCP operation",
            "technical_details": str(error),
            "suggestions": suggestions or [],
            "context": context or {},
            "timestamp": __import__('time').time()
        }
        
        # Log the error
        self.logger.error(
            f"MCP {category} error [{error_id}]: {error}",
            extra={"context": context, "category": category}
        )
        
        return response


# Global instances
mcp_security_validator = MCPSecurityValidator()
mcp_error_handler = MCPErrorHandler()
