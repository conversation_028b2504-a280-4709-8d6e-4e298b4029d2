"""
Agent MCP Integration Service for Datagenius.

This service integrates MCP server tools with the existing agent system,
making them available to all AI personas through the standard tool interface.
"""

import logging
from contextlib import contextmanager
from typing import Dict, Any, List, Optional, TYPE_CHECKING
from sqlalchemy.orm import Session

from ..database import get_db
from ..models.mcp_server import MCPServerStatus
from .mcp_agent_integration import mcp_agent_integration

if TYPE_CHECKING:
    from agents.tools.mcp.agent_integration import Agent<PERSON><PERSON><PERSON>ool
    from agents.components.base_component import AgentContext

logger = logging.getLogger(__name__)


class AgentMCPIntegrationService:
    """
    Service that integrates MCP tools with the Datagenius agent system.
    
    This service acts as a bridge between the MCP server system and the
    agent tool registry, ensuring MCP tools are available to all personas.
    """
    
    def __init__(self):
        """Initialize the agent MCP integration service."""
        self.initialized_profiles: Dict[str, bool] = {}
        self.tool_cache: Dict[str, List[Dict[str, Any]]] = {}
        self.logger = logging.getLogger(__name__)

    @contextmanager
    def _get_database_session(self):
        """Get a properly managed database session."""
        db = next(get_db())
        try:
            yield db
        finally:
            db.close()
    
    async def initialize_mcp_tools_for_agent(
        self, 
        agent_id: str, 
        user_id: int, 
        business_profile_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Initialize MCP tools for a specific agent.
        
        Args:
            agent_id: Agent identifier
            user_id: User ID
            business_profile_id: Optional business profile ID
            
        Returns:
            List of available MCP tools for the agent
        """
        self.logger.info(f"Initializing MCP tools for agent {agent_id}, user {user_id}")
        
        try:
            # Use proper database session management
            with self._get_database_session() as db:
                # Get available MCP tools for the user/business profile
                available_tools = await mcp_agent_integration.get_available_tools_for_agent(
                    db=db,
                    user_id=user_id,
                    business_profile_id=business_profile_id
                )

                # Register tools with the agent tool manager
                for tool_info in available_tools:
                    await self._register_tool_with_agent_system(tool_info, db)

                # Cache tools for this profile
                cache_key = f"{user_id}_{business_profile_id or 'default'}"
                self.tool_cache[cache_key] = available_tools

                self.logger.info(f"Initialized {len(available_tools)} MCP tools for agent {agent_id}")
                return available_tools

        except Exception as e:
            self.logger.error(f"Error initializing MCP tools for agent {agent_id}: {e}")
            return []
    
    async def _register_tool_with_agent_system(
        self,
        tool_info: Dict[str, Any],
        db: Session
    ) -> None:
        """
        Register an MCP tool with the agent system.

        Args:
            tool_info: Tool information dictionary
            db: Database session (unused but kept for interface consistency)
        """
        try:
            # Import here to avoid circular imports
            from agents.tools.mcp.agent_integration import AgentMCPTool, mcp_tool_manager

            # Create agent-compatible MCP tool
            agent_tool = AgentMCPTool(
                name=tool_info["name"],
                description=tool_info["description"],
                server_id=tool_info["server_id"],
                tool_name=tool_info["name"],
                parameters=tool_info.get("parameters", {}),
                integration_service=mcp_agent_integration
            )

            # Determine tool category based on capabilities
            category = self._determine_tool_category(tool_info)

            # Register with the MCP tool manager
            mcp_tool_manager.register_tool(agent_tool, category)

            self.logger.debug(f"Registered MCP tool {tool_info['name']} in category {category}")

        except Exception as e:
            self.logger.error(f"Error registering tool {tool_info.get('name', 'unknown')}: {e}")
    
    def _determine_tool_category(self, tool_info: Dict[str, Any]) -> str:
        """
        Determine the appropriate category for an MCP tool.
        
        Args:
            tool_info: Tool information dictionary
            
        Returns:
            Tool category string
        """
        name = tool_info.get("name", "").lower()
        description = tool_info.get("description", "").lower()
        capabilities = tool_info.get("capabilities", {})
        
        # Data-related tools
        if any(keyword in name or keyword in description for keyword in 
               ["data", "query", "database", "sql", "analysis", "chart", "visualization"]):
            return "data_analysis"
        
        # File system tools
        if any(keyword in name or keyword in description for keyword in 
               ["file", "filesystem", "read", "write", "directory", "folder"]):
            return "file_management"
        
        # Web and API tools
        if any(keyword in name or keyword in description for keyword in 
               ["web", "http", "api", "request", "fetch", "search"]):
            return "web_integration"
        
        # Development tools
        if any(keyword in name or keyword in description for keyword in 
               ["git", "github", "code", "repository", "commit", "branch"]):
            return "development"
        
        # Communication tools
        if any(keyword in name or keyword in description for keyword in 
               ["email", "slack", "message", "notification", "send"]):
            return "communication"
        
        # Default category
        return "general"
    
    async def get_mcp_tools_for_context(
        self, 
        context: AgentContext
    ) -> List[Dict[str, Any]]:
        """
        Get MCP tools available for the current agent context.
        
        Args:
            context: Agent processing context
            
        Returns:
            List of available MCP tools
        """
        try:
            user_id = int(context.user_id)
            business_profile_id = context.get_field("business_profile_id")
            
            # Check cache first
            cache_key = f"{user_id}_{business_profile_id or 'default'}"
            if cache_key in self.tool_cache:
                return self.tool_cache[cache_key]
            
            # Get tools from integration service
            db = context.get_field("db_session")
            if not db:
                db = next(get_db())
            
            available_tools = await mcp_agent_integration.get_available_tools_for_agent(
                db=db,
                user_id=user_id,
                business_profile_id=business_profile_id
            )
            
            # Update cache
            self.tool_cache[cache_key] = available_tools
            
            return available_tools
            
        except Exception as e:
            self.logger.error(f"Error getting MCP tools for context: {e}")
            return []
    
    async def execute_mcp_tool_from_context(
        self, 
        context: AgentContext, 
        tool_name: str, 
        arguments: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Execute an MCP tool from agent context.
        
        Args:
            context: Agent processing context
            tool_name: Name of the tool to execute
            arguments: Tool arguments
            
        Returns:
            Tool execution result
        """
        try:
            user_id = int(context.user_id)
            
            # Get database session
            db = context.get_field("db_session")
            if not db:
                db = next(get_db())
            
            # Find the tool ID
            available_tools = await self.get_mcp_tools_for_context(context)
            tool_info = None
            
            for tool in available_tools:
                if tool["name"] == tool_name:
                    tool_info = tool
                    break
            
            if not tool_info:
                return {
                    "success": False,
                    "error": f"MCP tool '{tool_name}' not found or not available"
                }
            
            # Execute the tool
            tool_id = tool_info["id"]
            result = await mcp_agent_integration.execute_mcp_tool(
                db=db,
                tool_id=tool_id,
                arguments=arguments,
                user_id=user_id
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error executing MCP tool {tool_name}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_mcp_resources_for_context(
        self, 
        context: AgentContext
    ) -> List[Dict[str, Any]]:
        """
        Get MCP resources available for the current agent context.
        
        Args:
            context: Agent processing context
            
        Returns:
            List of available MCP resources
        """
        try:
            user_id = int(context.user_id)
            business_profile_id = context.get_field("business_profile_id")
            
            # Get database session
            db = context.get_field("db_session")
            if not db:
                db = next(get_db())
            
            resources = await mcp_agent_integration.get_mcp_resources(
                db=db,
                user_id=user_id,
                business_profile_id=business_profile_id
            )
            
            return resources
            
        except Exception as e:
            self.logger.error(f"Error getting MCP resources for context: {e}")
            return []
    
    async def get_mcp_prompts_for_context(
        self, 
        context: AgentContext
    ) -> List[Dict[str, Any]]:
        """
        Get MCP prompts available for the current agent context.
        
        Args:
            context: Agent processing context
            
        Returns:
            List of available MCP prompts
        """
        try:
            user_id = int(context.user_id)
            business_profile_id = context.get_field("business_profile_id")
            
            # Get database session
            db = context.get_field("db_session")
            if not db:
                db = next(get_db())
            
            prompts = await mcp_agent_integration.get_mcp_prompts(
                db=db,
                user_id=user_id,
                business_profile_id=business_profile_id
            )
            
            return prompts
            
        except Exception as e:
            self.logger.error(f"Error getting MCP prompts for context: {e}")
            return []
    
    async def refresh_tools_for_business_profile(
        self, 
        business_profile_id: str, 
        user_id: int
    ) -> None:
        """
        Refresh MCP tools for a specific business profile.
        
        Args:
            business_profile_id: Business profile ID
            user_id: User ID
        """
        try:
            # Clear cache
            cache_key = f"{user_id}_{business_profile_id}"
            if cache_key in self.tool_cache:
                del self.tool_cache[cache_key]
            
            # Re-initialize tools
            db = next(get_db())
            capabilities = await mcp_agent_integration.initialize_for_business_profile(
                db=db,
                business_profile_id=business_profile_id
            )
            
            # Register new tools
            for tool_info in capabilities.get("tools", []):
                await self._register_tool_with_agent_system(tool_info, db)
            
            self.logger.info(f"Refreshed MCP tools for business profile {business_profile_id}")
            
        except Exception as e:
            self.logger.error(f"Error refreshing tools for business profile {business_profile_id}: {e}")
    
    async def cleanup_inactive_tools(self) -> None:
        """Clean up tools from inactive MCP servers."""
        try:
            db = next(get_db())
            await mcp_agent_integration.cleanup_inactive_servers(db)
            
            # Clear tool cache to force refresh
            self.tool_cache.clear()
            
            self.logger.info("Cleaned up inactive MCP tools")
            
        except Exception as e:
            self.logger.error(f"Error cleaning up inactive tools: {e}")
    
    def get_tool_usage_statistics(self) -> Dict[str, Any]:
        """
        Get usage statistics for MCP tools.

        Returns:
            Usage statistics dictionary
        """
        try:
            from agents.tools.mcp.agent_integration import mcp_tool_manager
            return mcp_tool_manager.get_usage_statistics()
        except ImportError:
            return {"error": "MCP tool manager not available"}


# Global service instance
agent_mcp_integration_service = AgentMCPIntegrationService()
