"""
Workflow Pattern Recognition System

Advanced pattern recognition system for identifying, learning, and suggesting
optimal workflow patterns based on historical data and performance metrics.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
import json
import numpy as np
from collections import defaultdict

from ..events.event_bus import event_bus, LangGraphEvent
from ..monitoring.metrics import MetricsCollector

logger = logging.getLogger(__name__)


class PatternType(Enum):
    """Types of workflow patterns."""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    CONDITIONAL = "conditional"
    ITERATIVE = "iterative"
    HIERARCHICAL = "hierarchical"
    HYBRID = "hybrid"


class PatternCategory(Enum):
    """Categories of workflow patterns."""
    DATA_PROCESSING = "data_processing"
    CONTENT_CREATION = "content_creation"
    DECISION_MAKING = "decision_making"
    AUTOMATION = "automation"
    INTEGRATION = "integration"
    ANALYSIS = "analysis"
    OPTIMIZATION = "optimization"
    MONITORING = "monitoring"


@dataclass
class WorkflowPattern:
    """Represents a recognized workflow pattern."""
    pattern_id: str
    name: str
    description: str
    pattern_type: PatternType
    category: PatternCategory
    
    # Pattern structure
    node_sequence: List[str] = field(default_factory=list)
    edge_patterns: List[Dict[str, Any]] = field(default_factory=list)
    agent_roles: Dict[str, str] = field(default_factory=dict)
    
    # Performance metrics
    success_rate: float = 0.0
    average_execution_time: float = 0.0
    average_cost: float = 0.0
    quality_score: float = 0.0
    
    # Usage statistics
    usage_count: int = 0
    last_used: Optional[datetime] = None
    created_at: datetime = field(default_factory=datetime.now)
    
    # Pattern characteristics
    complexity_score: float = 0.0
    adaptability_score: float = 0.0
    reliability_score: float = 0.0
    
    # Metadata
    tags: List[str] = field(default_factory=list)
    business_domains: List[str] = field(default_factory=list)
    prerequisites: List[str] = field(default_factory=list)
    variations: List[str] = field(default_factory=list)


@dataclass
class PatternMatch:
    """Represents a pattern match for a workflow."""
    pattern_id: str
    confidence_score: float
    similarity_score: float
    applicability_score: float
    
    # Match details
    matched_nodes: List[str] = field(default_factory=list)
    matched_edges: List[Dict[str, Any]] = field(default_factory=list)
    suggested_modifications: List[Dict[str, Any]] = field(default_factory=list)
    
    # Performance predictions
    predicted_success_rate: float = 0.0
    predicted_execution_time: float = 0.0
    predicted_cost: float = 0.0
    
    # Metadata
    match_timestamp: datetime = field(default_factory=datetime.now)
    match_context: Dict[str, Any] = field(default_factory=dict)


class WorkflowPatternRecognition:
    """
    Advanced workflow pattern recognition system.
    
    Features:
    - Pattern discovery and learning
    - Similarity matching algorithms
    - Performance-based pattern ranking
    - Adaptive pattern evolution
    - Intelligent pattern suggestions
    """
    
    def __init__(self):
        self.patterns: Dict[str, WorkflowPattern] = {}
        self.pattern_index: Dict[PatternCategory, Set[str]] = {}
        self.metrics = MetricsCollector("pattern_recognition")
        
        # Pattern learning data
        self.workflow_history: List[Dict[str, Any]] = []
        self.pattern_performance: Dict[str, List[float]] = defaultdict(list)
        self.pattern_usage_trends: Dict[str, List[Tuple[datetime, int]]] = defaultdict(list)
        
        # Configuration
        self.min_pattern_occurrences = 3
        self.similarity_threshold = 0.7
        self.learning_window = timedelta(days=30)
        self.pattern_evolution_threshold = 0.1
        
        # Initialize pattern categories
        for category in PatternCategory:
            self.pattern_index[category] = set()
    
    async def initialize(self):
        """Initialize the pattern recognition system."""
        try:
            # Load existing patterns
            await self._load_patterns()
            
            # Setup event handlers
            self._setup_event_handlers()
            
            # Start background tasks
            asyncio.create_task(self._pattern_learning_loop())
            asyncio.create_task(self._pattern_evolution_loop())
            asyncio.create_task(self._pattern_cleanup_loop())
            
            logger.info("Pattern recognition system initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize pattern recognition system: {e}")
            raise
    
    def _setup_event_handlers(self):
        """Setup event handlers for pattern recognition."""
        event_bus.subscribe("workflow.executed", self._handle_workflow_execution)
        event_bus.subscribe("workflow.composed", self._handle_workflow_composition)
        event_bus.subscribe("pattern.usage", self._handle_pattern_usage)
    
    async def recognize_patterns(self, workflow_structure: Dict[str, Any],
                               context: Optional[Dict[str, Any]] = None) -> List[PatternMatch]:
        """
        Recognize patterns in a workflow structure.
        
        Args:
            workflow_structure: Structure of the workflow to analyze
            context: Optional context for pattern matching
            
        Returns:
            List of pattern matches sorted by confidence
        """
        try:
            matches = []
            
            # Extract workflow features
            features = self._extract_workflow_features(workflow_structure)
            
            # Compare against known patterns
            for pattern_id, pattern in self.patterns.items():
                similarity = self._calculate_similarity(features, pattern)
                
                if similarity >= self.similarity_threshold:
                    # Calculate comprehensive match score
                    match = await self._create_pattern_match(
                        pattern, similarity, workflow_structure, context
                    )
                    matches.append(match)
            
            # Sort by confidence score
            matches.sort(key=lambda x: x.confidence_score, reverse=True)
            
            # Update metrics
            self.metrics.increment("pattern_recognitions")
            self.metrics.set_gauge("patterns_matched", len(matches))
            
            logger.info(f"Recognized {len(matches)} patterns for workflow")
            return matches
            
        except Exception as e:
            logger.error(f"Failed to recognize patterns: {e}")
            raise
    
    async def suggest_pattern_improvements(self, workflow_structure: Dict[str, Any],
                                         current_performance: Dict[str, float]) -> List[Dict[str, Any]]:
        """
        Suggest pattern-based improvements for a workflow.
        
        Args:
            workflow_structure: Current workflow structure
            current_performance: Current performance metrics
            
        Returns:
            List of improvement suggestions
        """
        try:
            suggestions = []
            
            # Find similar high-performing patterns
            similar_patterns = await self._find_similar_high_performing_patterns(
                workflow_structure, current_performance
            )
            
            for pattern in similar_patterns:
                # Analyze differences and suggest improvements
                improvements = await self._analyze_pattern_differences(
                    workflow_structure, pattern, current_performance
                )
                
                if improvements:
                    suggestions.extend(improvements)
            
            # Rank suggestions by potential impact
            suggestions.sort(key=lambda x: x.get("impact_score", 0), reverse=True)
            
            # Update metrics
            self.metrics.increment("improvement_suggestions")
            
            logger.info(f"Generated {len(suggestions)} pattern-based improvement suggestions")
            return suggestions[:10]  # Return top 10 suggestions
            
        except Exception as e:
            logger.error(f"Failed to suggest pattern improvements: {e}")
            raise
    
    async def learn_new_pattern(self, workflow_data: Dict[str, Any],
                              performance_data: Dict[str, float]) -> Optional[str]:
        """
        Learn a new pattern from workflow execution data.
        
        Args:
            workflow_data: Workflow structure and execution data
            performance_data: Performance metrics for the workflow
            
        Returns:
            Pattern ID if a new pattern was learned, None otherwise
        """
        try:
            # Check if this represents a new pattern
            is_new_pattern = await self._is_novel_pattern(workflow_data)
            
            if not is_new_pattern:
                # Update existing pattern statistics
                await self._update_existing_pattern_stats(workflow_data, performance_data)
                return None
            
            # Create new pattern
            pattern = await self._create_pattern_from_workflow(workflow_data, performance_data)
            
            # Validate pattern quality
            if not self._validate_pattern_quality(pattern):
                logger.info("Pattern quality validation failed, not learning pattern")
                return None
            
            # Store pattern
            self.patterns[pattern.pattern_id] = pattern
            self.pattern_index[pattern.category].add(pattern.pattern_id)
            
            # Publish pattern learning event
            await event_bus.publish(LangGraphEvent(
                event_type="pattern.learned",
                timestamp=datetime.now(),
                source="pattern_recognition",
                data={
                    "pattern_id": pattern.pattern_id,
                    "pattern_name": pattern.name,
                    "category": pattern.category.value,
                    "success_rate": pattern.success_rate,
                    "complexity_score": pattern.complexity_score
                }
            ))
            
            # Update metrics
            self.metrics.increment("patterns_learned")
            
            logger.info(f"Learned new pattern: {pattern.name} ({pattern.pattern_id})")
            return pattern.pattern_id
            
        except Exception as e:
            logger.error(f"Failed to learn new pattern: {e}")
            raise
    
    async def get_pattern_analytics(self) -> Dict[str, Any]:
        """
        Get comprehensive pattern analytics.
        
        Returns:
            Analytics data for patterns
        """
        try:
            analytics = {
                "overview": {
                    "total_patterns": len(self.patterns),
                    "patterns_by_category": self._get_patterns_by_category(),
                    "patterns_by_type": self._get_patterns_by_type(),
                    "average_success_rate": self._calculate_average_success_rate()
                },
                "performance": {
                    "top_performing_patterns": self._get_top_performing_patterns(),
                    "most_used_patterns": self._get_most_used_patterns(),
                    "pattern_evolution_trends": await self._get_pattern_evolution_trends(),
                    "success_rate_trends": await self._get_success_rate_trends()
                },
                "learning": {
                    "patterns_learned_recently": self._get_recently_learned_patterns(),
                    "learning_rate": self._calculate_learning_rate(),
                    "pattern_diversity": self._calculate_pattern_diversity(),
                    "adaptation_effectiveness": await self._calculate_adaptation_effectiveness()
                },
                "usage": {
                    "pattern_usage_distribution": self._get_pattern_usage_distribution(),
                    "seasonal_patterns": await self._identify_seasonal_patterns(),
                    "emerging_patterns": await self._identify_emerging_patterns(),
                    "declining_patterns": await self._identify_declining_patterns()
                }
            }
            
            # Update metrics
            self.metrics.increment("analytics_requests")
            
            return analytics

        except Exception as e:
            logger.error(f"Failed to generate pattern analytics: {e}")
            raise

    async def get_all_patterns(self) -> List[WorkflowPattern]:
        """
        Get all workflow patterns.

        Returns:
            List of all workflow patterns
        """
        try:
            patterns = list(self.patterns.values())

            # Update metrics
            self.metrics.increment("pattern_retrievals")
            self.metrics.set_gauge("total_patterns_retrieved", len(patterns))

            logger.debug(f"Retrieved {len(patterns)} workflow patterns")
            return patterns

        except Exception as e:
            logger.error(f"Failed to retrieve all patterns: {e}")
            raise

    async def get_pattern_by_id(self, pattern_id: str) -> Optional[WorkflowPattern]:
        """
        Get a specific pattern by ID.

        Args:
            pattern_id: ID of the pattern to retrieve

        Returns:
            WorkflowPattern if found, None otherwise
        """
        try:
            pattern = self.patterns.get(pattern_id)

            if pattern:
                self.metrics.increment("pattern_retrievals")
                logger.debug(f"Retrieved pattern: {pattern_id}")
            else:
                logger.warning(f"Pattern not found: {pattern_id}")

            return pattern

        except Exception as e:
            logger.error(f"Failed to retrieve pattern {pattern_id}: {e}")
            raise

    # Pattern Analysis Methods
    def _extract_workflow_features(self, workflow_structure: Dict[str, Any]) -> Dict[str, Any]:
        """Extract features from workflow structure for pattern matching."""
        features = {
            "node_count": len(workflow_structure.get("nodes", [])),
            "edge_count": len(workflow_structure.get("edges", [])),
            "node_types": [node.get("type", "") for node in workflow_structure.get("nodes", [])],
            "edge_types": [edge.get("type", "regular") for edge in workflow_structure.get("edges", [])],
            "has_conditional_edges": any(edge.get("conditional", False) for edge in workflow_structure.get("edges", [])),
            "has_parallel_paths": self._detect_parallel_paths(workflow_structure),
            "complexity_score": self._calculate_workflow_complexity(workflow_structure),
            "entry_points": [workflow_structure.get("entry_point", "")],
            "exit_points": self._find_exit_points(workflow_structure)
        }
        
        return features
    
    def _calculate_similarity(self, features: Dict[str, Any], pattern: WorkflowPattern) -> float:
        """Calculate similarity between workflow features and a pattern."""
        similarity_scores = []
        
        # Node count similarity
        node_count_diff = abs(features["node_count"] - len(pattern.node_sequence))
        node_similarity = max(0, 1 - (node_count_diff / max(features["node_count"], len(pattern.node_sequence))))
        similarity_scores.append(node_similarity * 0.2)
        
        # Node type similarity
        feature_types = set(features["node_types"])
        pattern_types = set(pattern.node_sequence)
        type_intersection = len(feature_types.intersection(pattern_types))
        type_union = len(feature_types.union(pattern_types))
        type_similarity = type_intersection / type_union if type_union > 0 else 0
        similarity_scores.append(type_similarity * 0.3)
        
        # Structure similarity
        structure_similarity = self._calculate_structure_similarity(features, pattern)
        similarity_scores.append(structure_similarity * 0.3)
        
        # Complexity similarity
        complexity_diff = abs(features["complexity_score"] - pattern.complexity_score)
        complexity_similarity = max(0, 1 - complexity_diff)
        similarity_scores.append(complexity_similarity * 0.2)
        
        return sum(similarity_scores)
    
    def _calculate_structure_similarity(self, features: Dict[str, Any], pattern: WorkflowPattern) -> float:
        """Calculate structural similarity between features and pattern."""
        # Simple structural comparison
        structure_score = 0.0
        
        # Check for conditional edges
        has_conditional = features.get("has_conditional_edges", False)
        pattern_has_conditional = any(edge.get("conditional", False) for edge in pattern.edge_patterns)
        if has_conditional == pattern_has_conditional:
            structure_score += 0.3
        
        # Check for parallel paths
        has_parallel = features.get("has_parallel_paths", False)
        pattern_has_parallel = pattern.pattern_type in [PatternType.PARALLEL, PatternType.HYBRID]
        if has_parallel == pattern_has_parallel:
            structure_score += 0.4
        
        # Check complexity alignment
        if abs(features["complexity_score"] - pattern.complexity_score) < 0.2:
            structure_score += 0.3
        
        return structure_score
    
    def _detect_parallel_paths(self, workflow_structure: Dict[str, Any]) -> bool:
        """Detect if workflow has parallel execution paths."""
        # Simple detection based on edge patterns
        edges = workflow_structure.get("edges", [])
        nodes = {node["id"] for node in workflow_structure.get("nodes", [])}
        
        # Build adjacency list
        adjacency = defaultdict(list)
        for edge in edges:
            adjacency[edge["from"]].append(edge["to"])
        
        # Check for nodes with multiple outgoing edges
        for node, targets in adjacency.items():
            if len(targets) > 1:
                return True
        
        return False
    
    def _calculate_workflow_complexity(self, workflow_structure: Dict[str, Any]) -> float:
        """Calculate complexity score for a workflow."""
        nodes = workflow_structure.get("nodes", [])
        edges = workflow_structure.get("edges", [])
        
        # Base complexity from node and edge count
        base_complexity = (len(nodes) + len(edges)) / 20.0  # Normalize to 0-1 range
        
        # Add complexity for conditional edges
        conditional_edges = sum(1 for edge in edges if edge.get("conditional", False))
        conditional_complexity = conditional_edges * 0.1
        
        # Add complexity for parallel paths
        parallel_complexity = 0.1 if self._detect_parallel_paths(workflow_structure) else 0
        
        return min(base_complexity + conditional_complexity + parallel_complexity, 1.0)
    
    def _find_exit_points(self, workflow_structure: Dict[str, Any]) -> List[str]:
        """Find exit points in the workflow."""
        edges = workflow_structure.get("edges", [])
        
        # Find nodes that have edges to END or no outgoing edges
        exit_points = []
        outgoing_nodes = {edge["from"] for edge in edges}
        all_nodes = {node["id"] for node in workflow_structure.get("nodes", [])}
        
        # Nodes with no outgoing edges
        exit_points.extend(all_nodes - outgoing_nodes)
        
        # Nodes with edges to END
        for edge in edges:
            if edge["to"] == "END":
                exit_points.append(edge["from"])
        
        return list(set(exit_points))

    async def _create_pattern_match(self, pattern: WorkflowPattern, similarity: float,
                                  workflow_structure: Dict[str, Any],
                                  context: Optional[Dict[str, Any]]) -> PatternMatch:
        """Create a pattern match object."""
        # Calculate confidence based on similarity and pattern reliability
        confidence = similarity * pattern.reliability_score

        # Calculate applicability based on context
        applicability = await self._calculate_applicability(pattern, context)

        # Predict performance based on pattern history
        predicted_success = pattern.success_rate
        predicted_time = pattern.average_execution_time
        predicted_cost = pattern.average_cost

        # Find matched nodes and edges
        matched_nodes = self._find_matched_nodes(workflow_structure, pattern)
        matched_edges = self._find_matched_edges(workflow_structure, pattern)

        # Generate suggestions for modifications
        suggestions = await self._generate_modification_suggestions(
            workflow_structure, pattern, similarity
        )

        return PatternMatch(
            pattern_id=pattern.pattern_id,
            confidence_score=confidence,
            similarity_score=similarity,
            applicability_score=applicability,
            matched_nodes=matched_nodes,
            matched_edges=matched_edges,
            suggested_modifications=suggestions,
            predicted_success_rate=predicted_success,
            predicted_execution_time=predicted_time,
            predicted_cost=predicted_cost,
            match_context=context or {}
        )

    async def _calculate_applicability(self, pattern: WorkflowPattern,
                                     context: Optional[Dict[str, Any]]) -> float:
        """Calculate how applicable a pattern is to the current context."""
        if not context:
            return 0.5  # Neutral applicability

        applicability_score = 0.5

        # Check business domain alignment
        if "business_domain" in context:
            domain = context["business_domain"]
            if domain in pattern.business_domains:
                applicability_score += 0.3

        # Check complexity requirements
        if "complexity_preference" in context:
            pref = context["complexity_preference"]
            if pref == "simple" and pattern.complexity_score < 0.3:
                applicability_score += 0.2
            elif pref == "complex" and pattern.complexity_score > 0.7:
                applicability_score += 0.2

        return min(applicability_score, 1.0)

    def _find_matched_nodes(self, workflow_structure: Dict[str, Any],
                           pattern: WorkflowPattern) -> List[str]:
        """Find nodes that match the pattern."""
        workflow_nodes = [node.get("type", "") for node in workflow_structure.get("nodes", [])]
        pattern_nodes = pattern.node_sequence

        matched = []
        for i, node_type in enumerate(workflow_nodes):
            if i < len(pattern_nodes) and node_type == pattern_nodes[i]:
                matched.append(workflow_structure["nodes"][i]["id"])

        return matched

    def _find_matched_edges(self, workflow_structure: Dict[str, Any],
                           pattern: WorkflowPattern) -> List[Dict[str, Any]]:
        """Find edges that match the pattern."""
        # Simple edge matching based on structure
        workflow_edges = workflow_structure.get("edges", [])
        return workflow_edges[:len(pattern.edge_patterns)]  # Simplified matching

    async def _generate_modification_suggestions(self, workflow_structure: Dict[str, Any],
                                               pattern: WorkflowPattern,
                                               similarity: float) -> List[Dict[str, Any]]:
        """Generate suggestions for modifying workflow to better match pattern."""
        suggestions = []

        if similarity < 0.9:  # Only suggest if not already very similar
            # Suggest node additions/modifications
            workflow_types = [node.get("type", "") for node in workflow_structure.get("nodes", [])]
            pattern_types = pattern.node_sequence

            missing_types = set(pattern_types) - set(workflow_types)
            for missing_type in missing_types:
                suggestions.append({
                    "type": "add_node",
                    "description": f"Consider adding a {missing_type} node",
                    "impact": "medium",
                    "estimated_improvement": 0.1
                })

            # Suggest structural improvements
            if pattern.pattern_type == PatternType.PARALLEL and not self._detect_parallel_paths(workflow_structure):
                suggestions.append({
                    "type": "add_parallelism",
                    "description": "Consider parallelizing independent tasks",
                    "impact": "high",
                    "estimated_improvement": 0.2
                })

        return suggestions

    # Pattern Learning Methods
    async def _is_novel_pattern(self, workflow_data: Dict[str, Any]) -> bool:
        """Check if workflow represents a novel pattern."""
        features = self._extract_workflow_features(workflow_data)

        # Check similarity against existing patterns
        for pattern in self.patterns.values():
            similarity = self._calculate_similarity(features, pattern)
            if similarity > 0.8:  # Very similar to existing pattern
                return False

        return True

    async def _update_existing_pattern_stats(self, workflow_data: Dict[str, Any],
                                           performance_data: Dict[str, float]):
        """Update statistics for existing patterns."""
        features = self._extract_workflow_features(workflow_data)

        # Find best matching pattern
        best_match = None
        best_similarity = 0

        for pattern in self.patterns.values():
            similarity = self._calculate_similarity(features, pattern)
            if similarity > best_similarity:
                best_similarity = similarity
                best_match = pattern

        if best_match and best_similarity > 0.7:
            # Update pattern statistics
            best_match.usage_count += 1
            best_match.last_used = datetime.now()

            # Update performance metrics (running average)
            alpha = 0.1  # Learning rate
            best_match.success_rate = (1 - alpha) * best_match.success_rate + alpha * performance_data.get("success_rate", 0)
            best_match.average_execution_time = (1 - alpha) * best_match.average_execution_time + alpha * performance_data.get("execution_time", 0)
            best_match.average_cost = (1 - alpha) * best_match.average_cost + alpha * performance_data.get("cost", 0)

            logger.info(f"Updated statistics for pattern {best_match.pattern_id}")

    async def _create_pattern_from_workflow(self, workflow_data: Dict[str, Any],
                                          performance_data: Dict[str, float]) -> WorkflowPattern:
        """Create a new pattern from workflow data."""
        features = self._extract_workflow_features(workflow_data)

        # Determine pattern type
        pattern_type = self._determine_pattern_type(features)

        # Determine category
        category = self._determine_pattern_category(workflow_data)

        # Create pattern
        pattern = WorkflowPattern(
            pattern_id=f"pattern_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            name=self._generate_pattern_name(features, category),
            description=f"Auto-learned pattern from workflow execution",
            pattern_type=pattern_type,
            category=category,
            node_sequence=features["node_types"],
            edge_patterns=workflow_data.get("edges", []),
            success_rate=performance_data.get("success_rate", 0.8),
            average_execution_time=performance_data.get("execution_time", 0.0),
            average_cost=performance_data.get("cost", 0.0),
            quality_score=performance_data.get("quality_score", 0.8),
            usage_count=1,
            last_used=datetime.now(),
            complexity_score=features["complexity_score"],
            adaptability_score=0.5,  # Initial value
            reliability_score=performance_data.get("success_rate", 0.8),
            tags=self._generate_pattern_tags(features, workflow_data),
            business_domains=workflow_data.get("business_domains", [])
        )

        return pattern

    def _determine_pattern_type(self, features: Dict[str, Any]) -> PatternType:
        """Determine pattern type from features."""
        if features.get("has_parallel_paths", False):
            if features.get("has_conditional_edges", False):
                return PatternType.HYBRID
            else:
                return PatternType.PARALLEL
        elif features.get("has_conditional_edges", False):
            return PatternType.CONDITIONAL
        else:
            return PatternType.SEQUENTIAL

    def _determine_pattern_category(self, workflow_data: Dict[str, Any]) -> PatternCategory:
        """Determine pattern category from workflow data."""
        # Simple heuristic based on node types
        node_types = [node.get("type", "") for node in workflow_data.get("nodes", [])]

        if any("data" in node_type.lower() for node_type in node_types):
            return PatternCategory.DATA_PROCESSING
        elif any("content" in node_type.lower() for node_type in node_types):
            return PatternCategory.CONTENT_CREATION
        elif any("decision" in node_type.lower() for node_type in node_types):
            return PatternCategory.DECISION_MAKING
        elif any("automat" in node_type.lower() for node_type in node_types):
            return PatternCategory.AUTOMATION
        elif any("integrat" in node_type.lower() for node_type in node_types):
            return PatternCategory.INTEGRATION
        elif any("analy" in node_type.lower() for node_type in node_types):
            return PatternCategory.ANALYSIS
        elif any("optim" in node_type.lower() for node_type in node_types):
            return PatternCategory.OPTIMIZATION
        elif any("monitor" in node_type.lower() for node_type in node_types):
            return PatternCategory.MONITORING
        else:
            return PatternCategory.AUTOMATION  # Default

    def _generate_pattern_name(self, features: Dict[str, Any], category: PatternCategory) -> str:
        """Generate a descriptive name for the pattern."""
        type_desc = "Sequential"
        if features.get("has_parallel_paths", False):
            type_desc = "Parallel"
        if features.get("has_conditional_edges", False):
            type_desc = "Conditional" if type_desc == "Sequential" else "Hybrid"

        category_name = category.value.replace("_", " ").title()
        node_count = features["node_count"]

        return f"{type_desc} {category_name} Pattern ({node_count} nodes)"

    def _generate_pattern_tags(self, features: Dict[str, Any], workflow_data: Dict[str, Any]) -> List[str]:
        """Generate tags for the pattern."""
        tags = []

        # Add structural tags
        if features.get("has_parallel_paths", False):
            tags.append("parallel")
        if features.get("has_conditional_edges", False):
            tags.append("conditional")

        # Add complexity tags
        if features["complexity_score"] < 0.3:
            tags.append("simple")
        elif features["complexity_score"] > 0.7:
            tags.append("complex")
        else:
            tags.append("moderate")

        # Add size tags
        if features["node_count"] < 5:
            tags.append("small")
        elif features["node_count"] > 10:
            tags.append("large")
        else:
            tags.append("medium")

        return tags

    def _validate_pattern_quality(self, pattern: WorkflowPattern) -> bool:
        """Validate if a pattern meets quality criteria."""
        # Check minimum success rate
        if pattern.success_rate < 0.6:
            return False

        # Check minimum complexity (avoid trivial patterns)
        if pattern.complexity_score < 0.1:
            return False

        # Check minimum node count
        if len(pattern.node_sequence) < 2:
            return False

        return True

    # Event Handlers
    async def _handle_workflow_execution(self, event: LangGraphEvent):
        """Handle workflow execution events for pattern learning."""
        try:
            workflow_data = event.data.get("workflow_structure", {})
            performance_data = event.data.get("performance", {})

            if workflow_data and performance_data:
                # Add to workflow history
                self.workflow_history.append({
                    "timestamp": datetime.now(),
                    "workflow": workflow_data,
                    "performance": performance_data
                })

                # Learn from execution
                await self.learn_new_pattern(workflow_data, performance_data)

        except Exception as e:
            logger.error(f"Error handling workflow execution: {e}")

    async def _handle_workflow_composition(self, event: LangGraphEvent):
        """Handle workflow composition events."""
        try:
            workflow_id = event.data.get("workflow_id")
            workflow_structure = event.data.get("workflow_structure", {})

            if workflow_structure:
                # Recognize patterns in composed workflow
                patterns = await self.recognize_patterns(workflow_structure)

                if patterns:
                    logger.info(f"Recognized {len(patterns)} patterns in composed workflow {workflow_id}")

        except Exception as e:
            logger.error(f"Error handling workflow composition: {e}")

    async def _handle_pattern_usage(self, event: LangGraphEvent):
        """Handle pattern usage events."""
        try:
            pattern_id = event.data.get("pattern_id")
            usage_context = event.data.get("context", {})

            if pattern_id in self.patterns:
                pattern = self.patterns[pattern_id]
                pattern.usage_count += 1
                pattern.last_used = datetime.now()

                # Track usage trends
                self.pattern_usage_trends[pattern_id].append((datetime.now(), 1))

        except Exception as e:
            logger.error(f"Error handling pattern usage: {e}")

    # Background Tasks
    async def _pattern_learning_loop(self):
        """Background task for continuous pattern learning."""
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour

                # Analyze recent workflow history for new patterns
                await self._analyze_recent_workflows()

                # Update pattern performance metrics
                await self._update_pattern_metrics()

            except Exception as e:
                logger.error(f"Error in pattern learning loop: {e}")

    async def _pattern_evolution_loop(self):
        """Background task for pattern evolution and adaptation."""
        while True:
            try:
                await asyncio.sleep(7200)  # Run every 2 hours

                # Evolve patterns based on usage and performance
                await self._evolve_patterns()

                # Merge similar patterns
                await self._merge_similar_patterns()

            except Exception as e:
                logger.error(f"Error in pattern evolution loop: {e}")

    async def _pattern_cleanup_loop(self):
        """Background task for pattern cleanup and maintenance."""
        while True:
            try:
                await asyncio.sleep(86400)  # Run daily

                # Remove obsolete patterns
                await self._cleanup_obsolete_patterns()

                # Archive old workflow history
                await self._archive_old_history()

            except Exception as e:
                logger.error(f"Error in pattern cleanup loop: {e}")

    async def _analyze_recent_workflows(self):
        """Analyze recent workflows for pattern learning."""
        cutoff_time = datetime.now() - self.learning_window
        recent_workflows = [
            wf for wf in self.workflow_history
            if wf["timestamp"] > cutoff_time
        ]

        # Group similar workflows
        workflow_groups = self._group_similar_workflows(recent_workflows)

        # Learn patterns from groups with sufficient occurrences
        for group in workflow_groups:
            if len(group) >= self.min_pattern_occurrences:
                await self._learn_pattern_from_group(group)

    def _group_similar_workflows(self, workflows: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """Group similar workflows together."""
        groups = []

        for workflow in workflows:
            features = self._extract_workflow_features(workflow["workflow"])

            # Find matching group
            matched_group = None
            for group in groups:
                if group:
                    group_features = self._extract_workflow_features(group[0]["workflow"])
                    similarity = self._calculate_feature_similarity(features, group_features)
                    if similarity > 0.8:
                        matched_group = group
                        break

            if matched_group:
                matched_group.append(workflow)
            else:
                groups.append([workflow])

        return groups

    def _calculate_feature_similarity(self, features1: Dict[str, Any], features2: Dict[str, Any]) -> float:
        """Calculate similarity between two feature sets."""
        # Simple similarity calculation
        similarity_scores = []

        # Node count similarity
        if features1["node_count"] > 0 and features2["node_count"] > 0:
            node_sim = 1 - abs(features1["node_count"] - features2["node_count"]) / max(features1["node_count"], features2["node_count"])
            similarity_scores.append(node_sim * 0.3)

        # Node type similarity
        types1 = set(features1["node_types"])
        types2 = set(features2["node_types"])
        if types1 or types2:
            type_sim = len(types1.intersection(types2)) / len(types1.union(types2))
            similarity_scores.append(type_sim * 0.4)

        # Complexity similarity
        complexity_sim = 1 - abs(features1["complexity_score"] - features2["complexity_score"])
        similarity_scores.append(complexity_sim * 0.3)

        return sum(similarity_scores) if similarity_scores else 0.0

    async def _learn_pattern_from_group(self, workflow_group: List[Dict[str, Any]]):
        """Learn a pattern from a group of similar workflows."""
        if not workflow_group:
            return

        # Calculate average performance
        avg_performance = {}
        for metric in ["success_rate", "execution_time", "cost", "quality_score"]:
            values = [wf["performance"].get(metric, 0) for wf in workflow_group if metric in wf["performance"]]
            avg_performance[metric] = sum(values) / len(values) if values else 0

        # Use first workflow as template
        template_workflow = workflow_group[0]["workflow"]

        # Create pattern
        pattern = await self._create_pattern_from_workflow(template_workflow, avg_performance)
        pattern.usage_count = len(workflow_group)

        # Validate and store
        if self._validate_pattern_quality(pattern):
            self.patterns[pattern.pattern_id] = pattern
            self.pattern_index[pattern.category].add(pattern.pattern_id)
            logger.info(f"Learned pattern from group of {len(workflow_group)} workflows: {pattern.name}")

    async def _update_pattern_metrics(self):
        """Update performance metrics for all patterns."""
        for pattern in self.patterns.values():
            # Update reliability score based on recent performance
            if pattern.pattern_id in self.pattern_performance:
                recent_scores = self.pattern_performance[pattern.pattern_id][-10:]  # Last 10 executions
                if recent_scores:
                    pattern.reliability_score = sum(recent_scores) / len(recent_scores)

    async def _evolve_patterns(self):
        """Evolve patterns based on usage and performance."""
        for pattern in self.patterns.values():
            # Check if pattern needs evolution
            if self._should_evolve_pattern(pattern):
                await self._evolve_pattern(pattern)

    def _should_evolve_pattern(self, pattern: WorkflowPattern) -> bool:
        """Check if a pattern should be evolved."""
        # Evolve if performance has degraded significantly
        if pattern.reliability_score < pattern.success_rate - self.pattern_evolution_threshold:
            return True

        # Evolve if not used recently but has high usage count
        if pattern.usage_count > 10 and pattern.last_used:
            days_since_use = (datetime.now() - pattern.last_used).days
            if days_since_use > 30:
                return True

        return False

    async def _evolve_pattern(self, pattern: WorkflowPattern):
        """Evolve a pattern based on recent data."""
        # Simple evolution: adjust adaptability score
        pattern.adaptability_score = min(pattern.adaptability_score + 0.1, 1.0)
        logger.info(f"Evolved pattern {pattern.pattern_id}")

    async def _merge_similar_patterns(self):
        """Merge patterns that have become very similar."""
        patterns_list = list(self.patterns.values())

        for i, pattern1 in enumerate(patterns_list):
            for j, pattern2 in enumerate(patterns_list[i+1:], i+1):
                if pattern1.category == pattern2.category:
                    similarity = self._calculate_pattern_similarity(pattern1, pattern2)
                    if similarity > 0.95:  # Very similar patterns
                        await self._merge_patterns(pattern1, pattern2)
                        break

    def _calculate_pattern_similarity(self, pattern1: WorkflowPattern, pattern2: WorkflowPattern) -> float:
        """Calculate similarity between two patterns."""
        # Simple similarity based on node sequences
        seq1 = set(pattern1.node_sequence)
        seq2 = set(pattern2.node_sequence)

        if not seq1 and not seq2:
            return 1.0
        if not seq1 or not seq2:
            return 0.0

        intersection = len(seq1.intersection(seq2))
        union = len(seq1.union(seq2))

        return intersection / union

    async def _merge_patterns(self, pattern1: WorkflowPattern, pattern2: WorkflowPattern):
        """Merge two similar patterns."""
        # Keep the pattern with higher usage count
        if pattern1.usage_count >= pattern2.usage_count:
            primary, secondary = pattern1, pattern2
        else:
            primary, secondary = pattern2, pattern1

        # Merge statistics
        total_usage = primary.usage_count + secondary.usage_count
        primary.success_rate = (primary.success_rate * primary.usage_count +
                               secondary.success_rate * secondary.usage_count) / total_usage
        primary.usage_count = total_usage
        primary.last_used = max(primary.last_used or datetime.min,
                               secondary.last_used or datetime.min)

        # Remove secondary pattern
        if secondary.pattern_id in self.patterns:
            del self.patterns[secondary.pattern_id]
            self.pattern_index[secondary.category].discard(secondary.pattern_id)

        logger.info(f"Merged patterns {primary.pattern_id} and {secondary.pattern_id}")

    async def _cleanup_obsolete_patterns(self):
        """Remove obsolete patterns."""
        cutoff_date = datetime.now() - timedelta(days=90)
        obsolete_patterns = []

        for pattern_id, pattern in self.patterns.items():
            # Mark as obsolete if not used recently and has low success rate
            if (pattern.last_used and pattern.last_used < cutoff_date and
                pattern.success_rate < 0.5 and pattern.usage_count < 5):
                obsolete_patterns.append(pattern_id)

        # Remove obsolete patterns
        for pattern_id in obsolete_patterns:
            pattern = self.patterns[pattern_id]
            del self.patterns[pattern_id]
            self.pattern_index[pattern.category].discard(pattern_id)
            logger.info(f"Removed obsolete pattern {pattern_id}")

    async def _archive_old_history(self):
        """Archive old workflow history."""
        cutoff_date = datetime.now() - timedelta(days=60)
        self.workflow_history = [
            wf for wf in self.workflow_history
            if wf["timestamp"] > cutoff_date
        ]

    async def _load_patterns(self):
        """Load existing patterns from storage."""
        # This would load from database in production
        logger.info("Pattern data loaded")

    # Analytics Helper Methods
    def _get_patterns_by_category(self) -> Dict[str, int]:
        """Get pattern count by category."""
        return {category.value: len(pattern_ids) for category, pattern_ids in self.pattern_index.items()}

    def _get_patterns_by_type(self) -> Dict[str, int]:
        """Get pattern count by type."""
        type_counts = {}
        for pattern in self.patterns.values():
            type_name = pattern.pattern_type.value
            type_counts[type_name] = type_counts.get(type_name, 0) + 1
        return type_counts

    def _calculate_average_success_rate(self) -> float:
        """Calculate average success rate across all patterns."""
        if not self.patterns:
            return 0.0
        return sum(p.success_rate for p in self.patterns.values()) / len(self.patterns)

    def _get_top_performing_patterns(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top performing patterns."""
        sorted_patterns = sorted(self.patterns.values(), key=lambda p: p.success_rate, reverse=True)
        return [
            {
                "pattern_id": p.pattern_id,
                "name": p.name,
                "success_rate": p.success_rate,
                "usage_count": p.usage_count
            }
            for p in sorted_patterns[:limit]
        ]

    def _get_most_used_patterns(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get most used patterns."""
        sorted_patterns = sorted(self.patterns.values(), key=lambda p: p.usage_count, reverse=True)
        return [
            {
                "pattern_id": p.pattern_id,
                "name": p.name,
                "usage_count": p.usage_count,
                "success_rate": p.success_rate
            }
            for p in sorted_patterns[:limit]
        ]

    # Placeholder methods for advanced analytics
    async def _get_pattern_evolution_trends(self) -> Dict[str, Any]:
        """Get pattern evolution trends."""
        return {"trend": "stable", "evolution_rate": 0.05}

    async def _get_success_rate_trends(self) -> Dict[str, Any]:
        """Get success rate trends."""
        return {"trend": "improving", "average_improvement": 0.02}

    def _get_recently_learned_patterns(self, days: int = 7) -> List[Dict[str, Any]]:
        """Get recently learned patterns."""
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_patterns = [p for p in self.patterns.values() if p.created_at > cutoff_date]
        return [{"pattern_id": p.pattern_id, "name": p.name, "created_at": p.created_at.isoformat()}
                for p in recent_patterns]

    def _calculate_learning_rate(self) -> float:
        """Calculate pattern learning rate."""
        recent_patterns = self._get_recently_learned_patterns(30)  # Last 30 days
        return len(recent_patterns) / 30.0  # Patterns per day

    def _calculate_pattern_diversity(self) -> float:
        """Calculate pattern diversity score."""
        if not self.patterns:
            return 0.0

        categories = set(p.category for p in self.patterns.values())
        types = set(p.pattern_type for p in self.patterns.values())

        # Simple diversity metric
        return (len(categories) / len(PatternCategory)) * (len(types) / len(PatternType))

    async def _calculate_adaptation_effectiveness(self) -> float:
        """Calculate adaptation effectiveness."""
        return 0.8  # Mock value

    def _get_pattern_usage_distribution(self) -> Dict[str, int]:
        """Get pattern usage distribution."""
        return {p.pattern_id: p.usage_count for p in self.patterns.values()}

    async def _identify_seasonal_patterns(self) -> List[Dict[str, Any]]:
        """Identify seasonal usage patterns based on historical data."""
        try:
            seasonal_patterns = []

            # Group patterns by time periods to identify seasonal trends
            time_grouped_usage = {}
            current_time = datetime.now()

            for pattern in self.patterns.values():
                # Analyze usage over different time periods
                for usage_record in getattr(pattern, 'usage_history', []):
                    timestamp = usage_record.get('timestamp', current_time)

                    # Group by month and day of week
                    month_key = timestamp.strftime("%m")
                    weekday_key = timestamp.strftime("%w")  # 0=Sunday, 6=Saturday
                    hour_key = timestamp.strftime("%H")

                    # Initialize tracking structures
                    if pattern.pattern_id not in time_grouped_usage:
                        time_grouped_usage[pattern.pattern_id] = {
                            'monthly': {},
                            'weekly': {},
                            'hourly': {}
                        }

                    # Track usage by time periods
                    time_grouped_usage[pattern.pattern_id]['monthly'][month_key] = \
                        time_grouped_usage[pattern.pattern_id]['monthly'].get(month_key, 0) + 1
                    time_grouped_usage[pattern.pattern_id]['weekly'][weekday_key] = \
                        time_grouped_usage[pattern.pattern_id]['weekly'].get(weekday_key, 0) + 1
                    time_grouped_usage[pattern.pattern_id]['hourly'][hour_key] = \
                        time_grouped_usage[pattern.pattern_id]['hourly'].get(hour_key, 0) + 1

            # Identify patterns with significant seasonal variation
            for pattern_id, time_data in time_grouped_usage.items():
                pattern = self.patterns.get(pattern_id)
                if not pattern:
                    continue

                # Calculate seasonal metrics
                seasonal_info = self._calculate_seasonal_metrics(time_data)

                if seasonal_info['has_seasonal_pattern']:
                    seasonal_patterns.append({
                        'pattern_id': pattern_id,
                        'pattern_name': pattern.name,
                        'seasonal_type': seasonal_info['type'],
                        'peak_periods': seasonal_info['peaks'],
                        'low_periods': seasonal_info['lows'],
                        'seasonal_strength': seasonal_info['strength'],
                        'recommendations': seasonal_info['recommendations']
                    })

            logger.debug(f"Identified {len(seasonal_patterns)} seasonal patterns")
            return seasonal_patterns

        except Exception as e:
            logger.error(f"Error identifying seasonal patterns: {e}")
            return []

    async def _identify_emerging_patterns(self) -> List[Dict[str, Any]]:
        """Identify emerging patterns based on recent usage trends."""
        try:
            emerging_patterns = []
            current_time = datetime.now()
            recent_threshold = current_time - timedelta(days=30)  # Last 30 days

            # Analyze patterns for recent growth trends
            for pattern in self.patterns.values():
                # Calculate recent vs historical usage
                recent_usage = 0
                historical_usage = 0
                total_usage = pattern.usage_count

                # Count recent usage (would use actual timestamps in production)
                if hasattr(pattern, 'usage_history'):
                    for usage_record in pattern.usage_history:
                        timestamp = usage_record.get('timestamp', current_time)
                        if timestamp >= recent_threshold:
                            recent_usage += 1
                        else:
                            historical_usage += 1
                else:
                    # Estimate based on creation date and usage count
                    days_since_creation = (current_time - pattern.created_at).days
                    if days_since_creation > 30:
                        estimated_daily_usage = total_usage / days_since_creation
                        recent_usage = estimated_daily_usage * 30
                        historical_usage = total_usage - recent_usage
                    else:
                        recent_usage = total_usage
                        historical_usage = 0

                # Calculate growth metrics
                if historical_usage > 0:
                    growth_rate = (recent_usage - (historical_usage / max(1, (current_time - pattern.created_at).days - 30) * 30)) / (historical_usage / max(1, (current_time - pattern.created_at).days - 30) * 30)
                else:
                    growth_rate = float('inf') if recent_usage > 0 else 0

                # Identify emerging patterns (high recent growth)
                if growth_rate > 0.5 and recent_usage >= 3:  # At least 50% growth and minimum usage
                    emerging_patterns.append({
                        'pattern_id': pattern.pattern_id,
                        'pattern_name': pattern.name,
                        'growth_rate': growth_rate,
                        'recent_usage': recent_usage,
                        'historical_usage': historical_usage,
                        'confidence_score': pattern.confidence_score,
                        'category': pattern.category.value if pattern.category else 'unknown',
                        'emergence_strength': min(1.0, growth_rate / 2.0),  # Normalize to 0-1
                        'recommendations': [
                            f"Monitor pattern {pattern.name} for continued growth",
                            f"Consider optimizing resources for {pattern.category.value if pattern.category else 'this'} category",
                            "Analyze what makes this pattern successful for replication"
                        ]
                    })

            # Sort by emergence strength
            emerging_patterns.sort(key=lambda x: x['emergence_strength'], reverse=True)

            logger.debug(f"Identified {len(emerging_patterns)} emerging patterns")
            return emerging_patterns[:10]  # Return top 10

        except Exception as e:
            logger.error(f"Error identifying emerging patterns: {e}")
            return []

    async def _identify_declining_patterns(self) -> List[Dict[str, Any]]:
        """Identify declining patterns that may need attention."""
        try:
            declining_patterns = []
            current_time = datetime.now()
            recent_threshold = current_time - timedelta(days=30)

            for pattern in self.patterns.values():
                # Skip very new patterns
                if (current_time - pattern.created_at).days < 60:
                    continue

                # Calculate recent vs historical usage
                recent_usage = 0
                historical_usage = 0

                if hasattr(pattern, 'usage_history'):
                    for usage_record in pattern.usage_history:
                        timestamp = usage_record.get('timestamp', current_time)
                        if timestamp >= recent_threshold:
                            recent_usage += 1
                        else:
                            historical_usage += 1
                else:
                    # Estimate based on pattern age and usage
                    days_since_creation = (current_time - pattern.created_at).days
                    estimated_daily_usage = pattern.usage_count / days_since_creation
                    recent_usage = estimated_daily_usage * 30
                    historical_usage = pattern.usage_count - recent_usage

                # Calculate decline metrics
                if historical_usage > 0:
                    expected_recent_usage = (historical_usage / max(1, (current_time - pattern.created_at).days - 30)) * 30
                    decline_rate = (expected_recent_usage - recent_usage) / expected_recent_usage if expected_recent_usage > 0 else 0
                else:
                    decline_rate = 0

                # Identify declining patterns
                if decline_rate > 0.3 and historical_usage >= 5:  # At least 30% decline and minimum historical usage
                    # Analyze potential reasons for decline
                    decline_reasons = self._analyze_decline_reasons(pattern, recent_usage, historical_usage)

                    declining_patterns.append({
                        'pattern_id': pattern.pattern_id,
                        'pattern_name': pattern.name,
                        'decline_rate': decline_rate,
                        'recent_usage': recent_usage,
                        'historical_usage': historical_usage,
                        'expected_usage': expected_recent_usage,
                        'confidence_score': pattern.confidence_score,
                        'category': pattern.category.value if pattern.category else 'unknown',
                        'decline_severity': min(1.0, decline_rate),
                        'potential_reasons': decline_reasons,
                        'recommendations': [
                            f"Investigate why {pattern.name} usage is declining",
                            "Consider updating or optimizing this pattern",
                            "Analyze if newer patterns are replacing this one",
                            "Review pattern performance metrics for issues"
                        ]
                    })

            # Sort by decline severity
            declining_patterns.sort(key=lambda x: x['decline_severity'], reverse=True)

            logger.debug(f"Identified {len(declining_patterns)} declining patterns")
            return declining_patterns[:10]  # Return top 10

        except Exception as e:
            logger.error(f"Error identifying declining patterns: {e}")
            return []

    def _calculate_seasonal_metrics(self, time_data: Dict[str, Dict[str, int]]) -> Dict[str, Any]:
        """Calculate seasonal metrics from time-grouped usage data."""
        seasonal_info = {
            'has_seasonal_pattern': False,
            'type': 'none',
            'peaks': [],
            'lows': [],
            'strength': 0.0,
            'recommendations': []
        }

        try:
            # Analyze monthly patterns
            monthly_data = time_data.get('monthly', {})
            if len(monthly_data) >= 3:  # Need at least 3 months of data
                monthly_values = list(monthly_data.values())
                monthly_avg = sum(monthly_values) / len(monthly_values)
                monthly_variance = sum((x - monthly_avg) ** 2 for x in monthly_values) / len(monthly_values)

                if monthly_variance > monthly_avg * 0.5:  # Significant variance
                    seasonal_info['has_seasonal_pattern'] = True
                    seasonal_info['type'] = 'monthly'
                    seasonal_info['strength'] = min(1.0, monthly_variance / (monthly_avg * 2))

                    # Identify peaks and lows
                    sorted_months = sorted(monthly_data.items(), key=lambda x: x[1], reverse=True)
                    seasonal_info['peaks'] = [month for month, count in sorted_months[:3] if count > monthly_avg]
                    seasonal_info['lows'] = [month for month, count in sorted_months[-3:] if count < monthly_avg]

            # Analyze weekly patterns
            weekly_data = time_data.get('weekly', {})
            if len(weekly_data) >= 5:  # Need at least 5 days of data
                weekly_values = list(weekly_data.values())
                weekly_avg = sum(weekly_values) / len(weekly_values)
                weekly_variance = sum((x - weekly_avg) ** 2 for x in weekly_values) / len(weekly_values)

                if weekly_variance > weekly_avg * 0.3:  # Significant variance
                    if not seasonal_info['has_seasonal_pattern'] or seasonal_info['strength'] < weekly_variance / (weekly_avg * 2):
                        seasonal_info['has_seasonal_pattern'] = True
                        seasonal_info['type'] = 'weekly'
                        seasonal_info['strength'] = min(1.0, weekly_variance / (weekly_avg * 2))

                        # Map weekday numbers to names
                        weekday_names = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
                        sorted_days = sorted(weekly_data.items(), key=lambda x: x[1], reverse=True)
                        seasonal_info['peaks'] = [weekday_names[int(day)] for day, count in sorted_days[:2] if count > weekly_avg]
                        seasonal_info['lows'] = [weekday_names[int(day)] for day, count in sorted_days[-2:] if count < weekly_avg]

            # Generate recommendations based on seasonal patterns
            if seasonal_info['has_seasonal_pattern']:
                if seasonal_info['type'] == 'monthly':
                    seasonal_info['recommendations'] = [
                        f"Peak usage in months: {', '.join(seasonal_info['peaks'])}",
                        f"Consider resource scaling during peak months",
                        f"Low usage in months: {', '.join(seasonal_info['lows'])}"
                    ]
                elif seasonal_info['type'] == 'weekly':
                    seasonal_info['recommendations'] = [
                        f"Peak usage on: {', '.join(seasonal_info['peaks'])}",
                        f"Consider scheduling maintenance during: {', '.join(seasonal_info['lows'])}",
                        "Optimize resource allocation based on weekly patterns"
                    ]

        except Exception as e:
            logger.error(f"Error calculating seasonal metrics: {e}")

        return seasonal_info

    def _analyze_decline_reasons(self, pattern: 'WorkflowPattern', recent_usage: int, historical_usage: int) -> List[str]:
        """Analyze potential reasons for pattern decline."""
        reasons = []

        try:
            # Performance-related reasons
            if hasattr(pattern, 'performance_metrics'):
                success_rate = pattern.performance_metrics.get('success_rate', 1.0)
                if success_rate < 0.8:
                    reasons.append(f"Low success rate ({success_rate:.1%}) may be deterring usage")

                avg_execution_time = pattern.performance_metrics.get('avg_execution_time', 0)
                if avg_execution_time > 3600:  # More than 1 hour
                    reasons.append("Long execution time may be causing users to choose alternatives")

            # Age-related reasons
            pattern_age = (datetime.now() - pattern.created_at).days
            if pattern_age > 365:  # More than 1 year old
                reasons.append("Pattern may be outdated and replaced by newer alternatives")

            # Complexity-related reasons
            if hasattr(pattern, 'complexity_level'):
                if pattern.complexity_level in ['complex', 'enterprise']:
                    reasons.append("High complexity may be limiting adoption")

            # Category-related reasons
            if hasattr(pattern, 'category'):
                # This would analyze if the entire category is declining
                reasons.append(f"May be related to declining interest in {pattern.category.value} workflows")

            # Usage pattern analysis
            if recent_usage == 0:
                reasons.append("Complete abandonment - pattern may be broken or obsolete")
            elif recent_usage < historical_usage * 0.1:
                reasons.append("Severe decline suggests major issues or better alternatives available")

            # Default reason if no specific reasons found
            if not reasons:
                reasons.append("Decline reason unclear - requires detailed investigation")

        except Exception as e:
            logger.error(f"Error analyzing decline reasons: {e}")
            reasons.append("Error analyzing decline - manual investigation needed")

        return reasons

    # Advanced pattern analysis methods
    async def _find_similar_high_performing_patterns(self, workflow_structure: Dict[str, Any],
                                                   current_performance: Dict[str, float]) -> List[WorkflowPattern]:
        """Find similar high-performing patterns that outperform the current workflow."""
        try:
            high_performing_patterns = []
            current_success_rate = current_performance.get('success_rate', 0.0)
            current_execution_time = current_performance.get('avg_execution_time', float('inf'))

            # Extract features from current workflow
            current_features = self._extract_workflow_features(workflow_structure)

            for pattern in self.patterns.values():
                try:
                    # Check if pattern has better performance
                    pattern_success_rate = pattern.performance_metrics.get('success_rate', 0.0)
                    pattern_execution_time = pattern.performance_metrics.get('avg_execution_time', float('inf'))

                    # Performance criteria for "high-performing"
                    is_better_success = pattern_success_rate > current_success_rate + 0.05  # At least 5% better
                    is_faster = pattern_execution_time < current_execution_time * 0.9  # At least 10% faster

                    if is_better_success or is_faster:
                        # Check similarity to current workflow
                        pattern_features = self._extract_workflow_features(pattern.workflow_structure)
                        similarity = self._calculate_feature_similarity(current_features, pattern_features)

                        if similarity >= 0.6:  # At least 60% similar
                            # Calculate overall performance score
                            performance_improvement = (
                                (pattern_success_rate - current_success_rate) * 0.6 +
                                (max(0, current_execution_time - pattern_execution_time) / max(current_execution_time, 1)) * 0.4
                            )

                            pattern.similarity_score = similarity
                            pattern.performance_improvement = performance_improvement
                            high_performing_patterns.append(pattern)

                except Exception as e:
                    logger.error(f"Error evaluating pattern {pattern.pattern_id}: {e}")
                    continue

            # Sort by performance improvement and similarity
            high_performing_patterns.sort(
                key=lambda p: (p.performance_improvement * 0.7 + p.similarity_score * 0.3),
                reverse=True
            )

            logger.debug(f"Found {len(high_performing_patterns)} similar high-performing patterns")
            return high_performing_patterns[:5]  # Return top 5

        except Exception as e:
            logger.error(f"Error finding similar high-performing patterns: {e}")
            return []

    async def _analyze_pattern_differences(self, workflow_structure: Dict[str, Any],
                                         pattern: WorkflowPattern,
                                         current_performance: Dict[str, float]) -> List[Dict[str, Any]]:
        """Analyze specific differences between workflow and pattern that could explain performance gaps."""
        try:
            differences = []

            # Compare workflow structures
            current_nodes = {node.get('id', node.get('type', 'unknown')): node
                           for node in workflow_structure.get('nodes', [])}
            pattern_nodes = {node.get('id', node.get('type', 'unknown')): node
                           for node in pattern.workflow_structure.get('nodes', [])}

            # Identify missing nodes in current workflow
            missing_nodes = set(pattern_nodes.keys()) - set(current_nodes.keys())
            for node_id in missing_nodes:
                node = pattern_nodes[node_id]
                node_type = node.get('type', 'unknown')
                differences.append({
                    'type': 'missing_node',
                    'description': f"Pattern includes {node_type} node ({node_id}) that current workflow lacks",
                    'impact': 'medium',
                    'suggestion': f"Consider adding a {node_type} node to improve workflow performance",
                    'node_details': node
                })

            # Identify extra nodes in current workflow
            extra_nodes = set(current_nodes.keys()) - set(pattern_nodes.keys())
            for node_id in extra_nodes:
                node = current_nodes[node_id]
                node_type = node.get('type', 'unknown')
                differences.append({
                    'type': 'extra_node',
                    'description': f"Current workflow has {node_type} node ({node_id}) not in pattern",
                    'impact': 'low',
                    'suggestion': f"Evaluate if {node_type} node is necessary or adds overhead",
                    'node_details': node
                })

            # Compare edge structures
            current_edges = set((edge.get('source', edge.get('from')), edge.get('target', edge.get('to')))
                              for edge in workflow_structure.get('edges', []))
            pattern_edges = set((edge.get('source', edge.get('from')), edge.get('target', edge.get('to')))
                              for edge in pattern.workflow_structure.get('edges', []))

            # Missing connections
            missing_edges = pattern_edges - current_edges
            for source, target in missing_edges:
                if source in current_nodes and target in current_nodes:
                    differences.append({
                        'type': 'missing_connection',
                        'description': f"Pattern has connection from {source} to {target} that current workflow lacks",
                        'impact': 'medium',
                        'suggestion': f"Consider adding connection from {source} to {target} for better flow",
                        'edge_details': {'source': source, 'target': target}
                    })

            # Performance metric differences
            pattern_performance = pattern.performance_metrics
            success_rate_diff = pattern_performance.get('success_rate', 0) - current_performance.get('success_rate', 0)
            execution_time_diff = current_performance.get('avg_execution_time', 0) - pattern_performance.get('avg_execution_time', 0)

            if success_rate_diff > 0.05:  # Pattern has significantly better success rate
                differences.append({
                    'type': 'performance_gap',
                    'description': f"Pattern achieves {pattern_performance.get('success_rate', 0):.1%} success rate vs current {current_performance.get('success_rate', 0):.1%}",
                    'impact': 'high',
                    'suggestion': "Analyze pattern's error handling and validation mechanisms",
                    'metric_details': {
                        'metric': 'success_rate',
                        'pattern_value': pattern_performance.get('success_rate', 0),
                        'current_value': current_performance.get('success_rate', 0),
                        'improvement': success_rate_diff
                    }
                })

            if execution_time_diff > 60:  # Pattern is significantly faster (more than 1 minute)
                differences.append({
                    'type': 'performance_gap',
                    'description': f"Pattern executes in {pattern_performance.get('avg_execution_time', 0):.0f}s vs current {current_performance.get('avg_execution_time', 0):.0f}s",
                    'impact': 'high',
                    'suggestion': "Analyze pattern's optimization techniques and parallel processing",
                    'metric_details': {
                        'metric': 'execution_time',
                        'pattern_value': pattern_performance.get('avg_execution_time', 0),
                        'current_value': current_performance.get('avg_execution_time', 0),
                        'improvement': execution_time_diff
                    }
                })

            # Configuration differences
            current_config = workflow_structure.get('configuration', {})
            pattern_config = pattern.workflow_structure.get('configuration', {})

            for key, pattern_value in pattern_config.items():
                current_value = current_config.get(key)
                if current_value != pattern_value:
                    differences.append({
                        'type': 'configuration_difference',
                        'description': f"Pattern uses {key}={pattern_value} while current workflow uses {key}={current_value}",
                        'impact': 'low',
                        'suggestion': f"Consider adjusting {key} configuration to match high-performing pattern",
                        'config_details': {
                            'key': key,
                            'pattern_value': pattern_value,
                            'current_value': current_value
                        }
                    })

            # Sort differences by impact (high, medium, low)
            impact_order = {'high': 3, 'medium': 2, 'low': 1}
            differences.sort(key=lambda d: impact_order.get(d['impact'], 0), reverse=True)

            logger.debug(f"Analyzed {len(differences)} differences between workflow and pattern {pattern.pattern_id}")
            return differences

        except Exception as e:
            logger.error(f"Error analyzing pattern differences: {e}")
            return []
