"""
Adaptive Workflow Manager for LangGraph-based Datagenius System.

This module provides enhanced workflow management with dynamic adaptation
capabilities, runtime modification, and persona-specific optimizations.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Callable, Set
from datetime import datetime, timedelta
import uuid
import copy

from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from .workflow_manager import WorkflowManager
from .marketplace_agent_factory import UserContext, MarketplaceAgentFactory
from .persona_workflow_builder import PersonaWorkflowBuilder, PersonaConfig
from ..states.unified_state import UnifiedDatageniusState, WorkflowStatus
from ..persistence.unified_checkpointer import UnifiedCheckpointer
from ..monitoring.performance_monitor import PerformanceMonitor

logger = logging.getLogger(__name__)


class WorkflowAdaptationStrategy:
    """Strategy for workflow adaptation."""
    
    def __init__(self, strategy_name: str):
        self.strategy_name = strategy_name
        self.adaptation_rules: List[Callable] = []
        self.performance_thresholds: Dict[str, float] = {}
        self.optimization_targets: List[str] = []
    
    def add_adaptation_rule(self, rule: Callable) -> None:
        """Add adaptation rule to strategy."""
        self.adaptation_rules.append(rule)
    
    def set_performance_threshold(self, metric: str, threshold: float) -> None:
        """Set performance threshold for adaptation."""
        self.performance_thresholds[metric] = threshold
    
    async def should_adapt(self, performance_metrics: Dict[str, float]) -> bool:
        """Determine if workflow should be adapted."""
        for metric, threshold in self.performance_thresholds.items():
            if metric in performance_metrics:
                if performance_metrics[metric] < threshold:
                    return True
        return False


class WorkflowOptimizer:
    """Optimizes workflows based on performance data."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.optimization_history: Dict[str, List[Dict[str, Any]]] = {}
        self.performance_baselines: Dict[str, Dict[str, float]] = {}
    
    async def optimize_workflow(
        self, 
        workflow: StateGraph, 
        performance_data: Dict[str, Any],
        optimization_targets: List[str]
    ) -> StateGraph:
        """Optimize workflow based on performance data."""
        try:
            optimized_workflow = copy.deepcopy(workflow)
            
            # Apply optimization strategies
            for target in optimization_targets:
                if target == "execution_time":
                    optimized_workflow = await self._optimize_execution_time(
                        optimized_workflow, performance_data
                    )
                elif target == "memory_usage":
                    optimized_workflow = await self._optimize_memory_usage(
                        optimized_workflow, performance_data
                    )
                elif target == "accuracy":
                    optimized_workflow = await self._optimize_accuracy(
                        optimized_workflow, performance_data
                    )
            
            return optimized_workflow
            
        except Exception as e:
            self.logger.error(f"Error optimizing workflow: {e}")
            return workflow
    
    async def _optimize_execution_time(
        self, 
        workflow: StateGraph, 
        performance_data: Dict[str, Any]
    ) -> StateGraph:
        """Optimize workflow for execution time."""
        # Implementation would analyze bottlenecks and optimize
        return workflow
    
    async def _optimize_memory_usage(
        self, 
        workflow: StateGraph, 
        performance_data: Dict[str, Any]
    ) -> StateGraph:
        """Optimize workflow for memory usage."""
        # Implementation would optimize memory consumption
        return workflow
    
    async def _optimize_accuracy(
        self, 
        workflow: StateGraph, 
        performance_data: Dict[str, Any]
    ) -> StateGraph:
        """Optimize workflow for accuracy."""
        # Implementation would enhance accuracy through better routing
        return workflow


class WorkflowVariationManager:
    """Manages A/B testing for workflow variations."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.active_experiments: Dict[str, Dict[str, Any]] = {}
        self.experiment_results: Dict[str, Dict[str, Any]] = {}
    
    async def create_experiment(
        self, 
        experiment_name: str,
        control_workflow: StateGraph,
        variation_workflows: List[StateGraph],
        traffic_split: Dict[str, float]
    ) -> str:
        """Create A/B test experiment."""
        try:
            experiment_id = str(uuid.uuid4())
            
            experiment = {
                "experiment_id": experiment_id,
                "experiment_name": experiment_name,
                "control_workflow": control_workflow,
                "variation_workflows": variation_workflows,
                "traffic_split": traffic_split,
                "start_time": datetime.now(),
                "status": "active",
                "metrics": {}
            }
            
            self.active_experiments[experiment_id] = experiment
            
            self.logger.info(f"Created workflow experiment: {experiment_name}")
            return experiment_id
            
        except Exception as e:
            self.logger.error(f"Error creating experiment: {e}")
            raise
    
    async def get_workflow_for_user(
        self, 
        experiment_id: str, 
        user_id: str
    ) -> Optional[StateGraph]:
        """Get workflow variation for user based on experiment."""
        try:
            experiment = self.active_experiments.get(experiment_id)
            if not experiment:
                return None
            
            # Simple hash-based assignment
            user_hash = hash(user_id) % 100
            cumulative_split = 0
            
            for variation, split_percentage in experiment["traffic_split"].items():
                cumulative_split += split_percentage * 100
                if user_hash < cumulative_split:
                    if variation == "control":
                        return experiment["control_workflow"]
                    else:
                        variation_index = int(variation.split("_")[-1])
                        return experiment["variation_workflows"][variation_index]
            
            # Default to control
            return experiment["control_workflow"]
            
        except Exception as e:
            self.logger.error(f"Error getting workflow for user: {e}")
            return None
    
    async def record_experiment_metric(
        self, 
        experiment_id: str, 
        variation: str,
        metric_name: str, 
        metric_value: float
    ) -> None:
        """Record experiment metric."""
        try:
            experiment = self.active_experiments.get(experiment_id)
            if experiment:
                if variation not in experiment["metrics"]:
                    experiment["metrics"][variation] = {}
                if metric_name not in experiment["metrics"][variation]:
                    experiment["metrics"][variation][metric_name] = []
                
                experiment["metrics"][variation][metric_name].append(metric_value)
            
        except Exception as e:
            self.logger.error(f"Error recording experiment metric: {e}")


class AdaptiveWorkflowManager(WorkflowManager):
    """
    Enhanced workflow manager with dynamic adaptation capabilities.
    
    Features:
    - Runtime workflow modification based on performance
    - Persona-specific workflow adaptation
    - A/B testing for workflow variations
    - Performance-driven optimization
    - Context-aware workflow selection
    """
    
    def __init__(self):
        super().__init__()
        self.persona_workflow_builder = PersonaWorkflowBuilder()
        self.marketplace_agent_factory = MarketplaceAgentFactory()
        self.workflow_optimizer = WorkflowOptimizer()
        self.variation_manager = WorkflowVariationManager()
        self.performance_monitor = PerformanceMonitor()
        
        # Adaptation strategies
        self.adaptation_strategies: Dict[str, WorkflowAdaptationStrategy] = {}
        
        # Workflow adaptation cache
        self.adapted_workflows: Dict[str, StateGraph] = {}
        self.adaptation_history: Dict[str, List[Dict[str, Any]]] = {}
        
        # Performance tracking
        self.workflow_performance: Dict[str, Dict[str, float]] = {}
        
        self._initialize_adaptation_strategies()
    
    def _initialize_adaptation_strategies(self) -> None:
        """Initialize default adaptation strategies."""
        
        # Performance-based adaptation
        performance_strategy = WorkflowAdaptationStrategy("performance_optimization")
        performance_strategy.set_performance_threshold("execution_time", 30.0)  # 30 seconds
        performance_strategy.set_performance_threshold("memory_usage", 0.8)     # 80% memory
        performance_strategy.set_performance_threshold("success_rate", 0.9)     # 90% success
        self.adaptation_strategies["performance"] = performance_strategy
        
        # User experience adaptation
        ux_strategy = WorkflowAdaptationStrategy("user_experience")
        ux_strategy.set_performance_threshold("user_satisfaction", 0.8)         # 80% satisfaction
        ux_strategy.set_performance_threshold("response_time", 5.0)             # 5 seconds
        self.adaptation_strategies["user_experience"] = ux_strategy
        
        # Business context adaptation
        business_strategy = WorkflowAdaptationStrategy("business_context")
        business_strategy.set_performance_threshold("business_relevance", 0.85) # 85% relevance
        self.adaptation_strategies["business_context"] = business_strategy
    
    async def adapt_workflow_for_persona(
        self, 
        workflow_id: str, 
        persona_id: str,
        user_context: UserContext,
        performance_data: Optional[Dict[str, float]] = None
    ) -> StateGraph:
        """Dynamically adapt workflow based on persona selection."""
        try:
            self.logger.info(f"Adapting workflow {workflow_id} for persona {persona_id}")
            
            # Check cache first
            cache_key = f"{workflow_id}_{persona_id}_{user_context.user_id}"
            if cache_key in self.adapted_workflows:
                cached_workflow = self.adapted_workflows[cache_key]
                
                # Check if adaptation is still valid
                if await self._is_adaptation_valid(cache_key, performance_data):
                    return cached_workflow
            
            # Get current workflow
            current_workflow = await self.get_workflow(workflow_id)
            if not current_workflow:
                # Create new workflow for persona
                current_workflow = await self.persona_workflow_builder.create_persona_workflow(
                    persona_id, user_context
                )
            
            # Get persona configuration
            persona_config_data = await self.persona_workflow_builder.persona_registry.get_persona_config(persona_id)
            persona_config = PersonaConfig(persona_config_data)
            
            # Create adapted workflow
            adapted_workflow = await self._create_adapted_workflow(
                current_workflow, persona_config, user_context, performance_data
            )
            
            # Cache the adapted workflow
            self.adapted_workflows[cache_key] = adapted_workflow
            
            # Record adaptation
            await self._record_adaptation(workflow_id, persona_id, user_context, performance_data)
            
            self.logger.info(f"Successfully adapted workflow for persona {persona_id}")
            return adapted_workflow
            
        except Exception as e:
            self.logger.error(f"Error adapting workflow for persona {persona_id}: {e}")
            raise
    
    async def _create_adapted_workflow(
        self, 
        base_workflow: StateGraph,
        persona_config: PersonaConfig,
        user_context: UserContext,
        performance_data: Optional[Dict[str, float]] = None
    ) -> StateGraph:
        """Create adapted workflow with persona-specific modifications."""
        try:
            # Clone base workflow
            adapted_workflow = self._clone_workflow(base_workflow)
            
            # Apply persona-specific modifications
            if persona_config.methodology_framework:
                adapted_workflow = await self._apply_methodology_framework(
                    adapted_workflow, persona_config.methodology_framework
                )
            
            # Add persona-specific tools
            for tool_config in persona_config.specialized_tools:
                adapted_workflow = await self._add_tool_node(adapted_workflow, tool_config)
            
            # Apply business profile context
            if user_context.business_profile:
                adapted_workflow = await self._inject_business_context(
                    adapted_workflow, user_context.business_profile
                )
            
            # Apply performance optimizations
            if performance_data:
                adapted_workflow = await self._apply_performance_optimizations(
                    adapted_workflow, performance_data, persona_config
                )
            
            # Apply adaptation strategies
            for strategy_name, strategy in self.adaptation_strategies.items():
                if performance_data and await strategy.should_adapt(performance_data):
                    adapted_workflow = await self._apply_adaptation_strategy(
                        adapted_workflow, strategy, performance_data
                    )
            
            return adapted_workflow
            
        except Exception as e:
            self.logger.error(f"Error creating adapted workflow: {e}")
            raise
    
    async def _apply_performance_optimizations(
        self, 
        workflow: StateGraph, 
        performance_data: Dict[str, float],
        persona_config: PersonaConfig
    ) -> StateGraph:
        """Apply performance-based optimizations."""
        
        optimization_targets = persona_config.performance_optimization.get(
            "optimization_targets", ["execution_time", "accuracy"]
        )
        
        return await self.workflow_optimizer.optimize_workflow(
            workflow, performance_data, optimization_targets
        )
    
    async def _apply_adaptation_strategy(
        self, 
        workflow: StateGraph, 
        strategy: WorkflowAdaptationStrategy,
        performance_data: Dict[str, float]
    ) -> StateGraph:
        """Apply specific adaptation strategy."""
        
        adapted_workflow = workflow
        
        for rule in strategy.adaptation_rules:
            try:
                adapted_workflow = await rule(adapted_workflow, performance_data)
            except Exception as e:
                self.logger.error(f"Error applying adaptation rule: {e}")
        
        return adapted_workflow
    
    async def create_workflow_experiment(
        self, 
        experiment_name: str,
        base_persona_id: str,
        variation_configs: List[Dict[str, Any]],
        user_context: UserContext
    ) -> str:
        """Create A/B test experiment for workflow variations."""
        try:
            # Create control workflow
            control_workflow = await self.persona_workflow_builder.create_persona_workflow(
                base_persona_id, user_context
            )
            
            # Create variation workflows
            variation_workflows = []
            for variation_config in variation_configs:
                # Modify persona config for variation
                modified_persona_id = variation_config.get("persona_id", base_persona_id)
                variation_workflow = await self.persona_workflow_builder.create_persona_workflow(
                    modified_persona_id, user_context
                )
                variation_workflows.append(variation_workflow)
            
            # Define traffic split
            num_variations = len(variation_workflows)
            traffic_split = {"control": 0.5}
            variation_split = 0.5 / num_variations if num_variations > 0 else 0
            
            for i in range(num_variations):
                traffic_split[f"variation_{i}"] = variation_split
            
            # Create experiment
            experiment_id = await self.variation_manager.create_experiment(
                experiment_name, control_workflow, variation_workflows, traffic_split
            )
            
            return experiment_id
            
        except Exception as e:
            self.logger.error(f"Error creating workflow experiment: {e}")
            raise
    
    async def get_optimized_workflow_for_user(
        self, 
        persona_id: str, 
        user_context: UserContext,
        experiment_id: Optional[str] = None
    ) -> StateGraph:
        """Get optimized workflow for user, considering experiments."""
        try:
            # Check if user is part of an experiment
            if experiment_id:
                experiment_workflow = await self.variation_manager.get_workflow_for_user(
                    experiment_id, user_context.user_id
                )
                if experiment_workflow:
                    return experiment_workflow
            
            # Get performance data for user/persona combination
            performance_data = await self._get_user_performance_data(
                user_context.user_id, persona_id
            )
            
            # Adapt workflow based on performance
            workflow_id = f"persona_{persona_id}"
            adapted_workflow = await self.adapt_workflow_for_persona(
                workflow_id, persona_id, user_context, performance_data
            )
            
            return adapted_workflow
            
        except Exception as e:
            self.logger.error(f"Error getting optimized workflow: {e}")
            # Fallback to basic persona workflow
            return await self.persona_workflow_builder.create_persona_workflow(
                persona_id, user_context
            )
    
    async def _get_user_performance_data(
        self, 
        user_id: str, 
        persona_id: str
    ) -> Dict[str, float]:
        """Get performance data for user/persona combination."""
        # Implementation would query performance monitoring system
        return await self.performance_monitor.get_user_persona_metrics(user_id, persona_id)
    
    async def _is_adaptation_valid(
        self, 
        cache_key: str, 
        current_performance: Optional[Dict[str, float]]
    ) -> bool:
        """Check if cached adaptation is still valid."""
        # Simple time-based validation for now
        # Could be enhanced with performance-based validation
        return True
    
    async def _record_adaptation(
        self, 
        workflow_id: str, 
        persona_id: str,
        user_context: UserContext, 
        performance_data: Optional[Dict[str, float]]
    ) -> None:
        """Record workflow adaptation for analysis."""
        adaptation_record = {
            "timestamp": datetime.now(),
            "workflow_id": workflow_id,
            "persona_id": persona_id,
            "user_id": user_context.user_id,
            "performance_data": performance_data,
            "adaptation_triggers": []
        }
        
        # Store adaptation history
        if workflow_id not in self.adaptation_history:
            self.adaptation_history[workflow_id] = []
        self.adaptation_history[workflow_id].append(adaptation_record)
    
    def _clone_workflow(self, workflow: StateGraph) -> StateGraph:
        """Clone workflow for modification."""
        # Implementation would properly clone the StateGraph
        return copy.deepcopy(workflow)
    
    async def _apply_methodology_framework(
        self, 
        workflow: StateGraph, 
        framework: str
    ) -> StateGraph:
        """Apply methodology framework to workflow."""
        # Implementation would modify workflow based on framework
        return workflow
    
    async def _add_tool_node(
        self, 
        workflow: StateGraph, 
        tool_config: Dict[str, Any]
    ) -> StateGraph:
        """Add tool node to workflow."""
        # Implementation would add tool execution node
        return workflow
    
    async def _inject_business_context(
        self, 
        workflow: StateGraph, 
        business_profile: Dict[str, Any]
    ) -> StateGraph:
        """Inject business context into workflow."""
        # Implementation would add business context nodes
        return workflow


# Global instance
adaptive_workflow_manager = AdaptiveWorkflowManager()
