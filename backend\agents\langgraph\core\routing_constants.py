"""
Routing Constants for LangGraph User-Centric Architecture.

This module contains all constants used in the user-centric routing system
to eliminate hardcoded strings and improve maintainability.
"""

from typing import Dict, Any
from enum import Enum


class RoutingConstants:
    """Constants for routing logic throughout the LangGraph system."""
    
    # Agent and node prefixes
    AGENT_PREFIX = "agent_"
    TOOL_PREFIX = "tool_"
    
    # Special node names
    ROUTING_FALLBACK = "routing"
    AGENT_SWITCH_NODE = "agent_switch"
    END_NODE = "END"
    START_NODE = "START"
    
    # Agent patterns for fallback logic
    CONCIERGE_AGENT_PATTERN = "concierge"
    
    # Agent selection priorities (lower number = higher priority)
    PRIORITY_SELECTED_AGENT = 1
    PRIORITY_CURRENT_AGENT = 2
    PRIORITY_CONCIERGE_FALLBACK = 3
    PRIORITY_FIRST_AVAILABLE = 4
    
    # Error messages
    ERROR_NO_AGENTS_AVAILABLE = "No agents available for routing"
    ERROR_INVALID_STATE = "Invalid state parameter"
    ERROR_AGENT_NOT_FOUND = "Specified agent not found in registry"
    
    # Logging messages
    LOG_ENTRY_POINT_DETERMINATION = "🎯 Determining entry point based on selected agent"
    LOG_TOOL_ROUTING = "🔧 Routing from tool back to selected agent"
    LOG_AGENT_ROUTING = "🎯 Routing to agent based on state analysis"
    LOG_FALLBACK_CONCIERGE = "🔧 Falling back to concierge agent"
    LOG_FALLBACK_FIRST_AVAILABLE = "🔧 Falling back to first available agent"
    LOG_EMERGENCY_FALLBACK = "🚨 Emergency fallback activated"
    
    # Success messages
    SUCCESS_SELECTED_AGENT = "✅ Using selected agent"
    SUCCESS_CURRENT_AGENT = "✅ Using current agent"
    SUCCESS_CONCIERGE_FALLBACK = "✅ Using concierge fallback"
    SUCCESS_FIRST_AVAILABLE = "✅ Using first available agent"
    
    # Warning messages
    WARNING_NO_AGENTS = "⚠️ No agents available, falling back to routing node"
    WARNING_AGENT_NOT_AVAILABLE = "⚠️ Selected agent not available, falling back"
    WARNING_INVALID_STATE = "🚨 Invalid state parameter"


class RoutingPriority(Enum):
    """Enumeration of routing priorities for consistent ordering."""
    SELECTED_AGENT = 1
    CURRENT_AGENT = 2
    COMMAND_PATTERN = 3
    CONCIERGE_FALLBACK = 4
    FIRST_AVAILABLE = 5


class RoutingContext(Enum):
    """Context types for routing decisions."""
    ENTRY_POINT = "entry_point"
    TOOL_ROUTING = "tool_routing"
    AGENT_ROUTING = "agent_routing"
    FALLBACK = "fallback"


class ValidationError(Exception):
    """Custom exception for routing validation errors."""
    pass


class AgentNotFoundError(Exception):
    """Custom exception for when specified agent is not found."""
    pass


def format_agent_node_name(agent_id: str) -> str:
    """
    Format agent ID into proper node name.
    
    Args:
        agent_id: Raw agent identifier
        
    Returns:
        Formatted node name with agent prefix
        
    Example:
        >>> format_agent_node_name("marketing")
        "agent_marketing"
    """
    if not agent_id:
        raise ValidationError("Agent ID cannot be empty")
    
    if agent_id.startswith(RoutingConstants.AGENT_PREFIX):
        return agent_id
    
    return f"{RoutingConstants.AGENT_PREFIX}{agent_id}"


def format_tool_node_name(tool_name: str) -> str:
    """
    Format tool name into proper node name.
    
    Args:
        tool_name: Raw tool name
        
    Returns:
        Formatted node name with tool prefix
        
    Example:
        >>> format_tool_node_name("data_query")
        "tool_data_query"
    """
    if not tool_name:
        raise ValidationError("Tool name cannot be empty")
    
    if tool_name.startswith(RoutingConstants.TOOL_PREFIX):
        return tool_name
    
    return f"{RoutingConstants.TOOL_PREFIX}{tool_name}"


def extract_agent_id_from_node_name(node_name: str) -> str:
    """
    Extract agent ID from formatted node name.
    
    Args:
        node_name: Formatted node name (e.g., "agent_marketing")
        
    Returns:
        Raw agent ID (e.g., "marketing")
        
    Example:
        >>> extract_agent_id_from_node_name("agent_marketing")
        "marketing"
    """
    if not node_name:
        raise ValidationError("Node name cannot be empty")
    
    if node_name.startswith(RoutingConstants.AGENT_PREFIX):
        return node_name[len(RoutingConstants.AGENT_PREFIX):]
    
    return node_name


def is_concierge_agent(agent_id: str) -> bool:
    """
    Check if agent ID represents a concierge agent.
    
    Args:
        agent_id: Agent identifier to check
        
    Returns:
        True if agent is a concierge agent
        
    Example:
        >>> is_concierge_agent("concierge-agent")
        True
        >>> is_concierge_agent("marketing-agent")
        False
    """
    if not agent_id:
        return False
    
    return RoutingConstants.CONCIERGE_AGENT_PATTERN in agent_id.lower()


def get_routing_priority_order() -> Dict[str, int]:
    """
    Get the standard routing priority order.
    
    Returns:
        Dictionary mapping priority names to numeric values
    """
    return {
        "selected_agent": RoutingConstants.PRIORITY_SELECTED_AGENT,
        "current_agent": RoutingConstants.PRIORITY_CURRENT_AGENT,
        "concierge_fallback": RoutingConstants.PRIORITY_CONCIERGE_FALLBACK,
        "first_available": RoutingConstants.PRIORITY_FIRST_AVAILABLE
    }


# Default routing configuration
DEFAULT_ROUTING_CONFIG = {
    "enable_user_centric_routing": True,
    "allow_automatic_fallback": True,
    "prefer_concierge_fallback": True,
    "enable_command_pattern": True,
    "log_routing_decisions": True,
    "validate_agent_availability": True,
    "cache_agent_lookups": True
}
