"""
Comprehensive error handling utilities for Datagenius MCP integration.

This module provides standardized error handling, logging, and user-friendly
error responses for the MCP server system.
"""

import logging
import traceback
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Union
from fastapi import HTTPException, status
from pydantic import BaseModel

logger = logging.getLogger(__name__)


class ErrorResponse(BaseModel):
    """Standardized error response model."""
    
    success: bool = False
    error_code: str
    message: str
    details: Optional[str] = None
    field_errors: Optional[Dict[str, List[str]]] = None
    timestamp: str
    request_id: Optional[str] = None
    suggestions: Optional[List[str]] = None


class MCPError(Exception):
    """Base exception for MCP-related errors."""
    
    def __init__(
        self, 
        message: str, 
        error_code: str = "MCP_ERROR",
        details: Optional[str] = None,
        field_errors: Optional[Dict[str, List[str]]] = None,
        suggestions: Optional[List[str]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details
        self.field_errors = field_errors
        self.suggestions = suggestions
        super().__init__(message)


class MCPValidationError(MCPError):
    """Exception for validation errors."""
    
    def __init__(
        self, 
        message: str, 
        field_errors: Optional[Dict[str, List[str]]] = None,
        suggestions: Optional[List[str]] = None
    ):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            field_errors=field_errors,
            suggestions=suggestions or [
                "Check the provided data format",
                "Ensure all required fields are provided",
                "Verify field values meet the requirements"
            ]
        )


class MCPSecurityError(MCPError):
    """Exception for security-related errors."""
    
    def __init__(self, message: str, details: Optional[str] = None):
        super().__init__(
            message=message,
            error_code="SECURITY_ERROR",
            details=details,
            suggestions=[
                "Review the configuration for security issues",
                "Remove potentially dangerous commands or URLs",
                "Contact support if you believe this is an error"
            ]
        )


class MCPConnectionError(MCPError):
    """Exception for connection-related errors."""
    
    def __init__(self, message: str, details: Optional[str] = None):
        super().__init__(
            message=message,
            error_code="CONNECTION_ERROR",
            details=details,
            suggestions=[
                "Verify the server URL or command is correct",
                "Check network connectivity",
                "Ensure the server is running and accessible",
                "Verify authentication credentials"
            ]
        )


class MCPConfigurationError(MCPError):
    """Exception for configuration-related errors."""
    
    def __init__(self, message: str, details: Optional[str] = None):
        super().__init__(
            message=message,
            error_code="CONFIGURATION_ERROR",
            details=details,
            suggestions=[
                "Verify the server configuration format",
                "Check that all required fields are provided",
                "Ensure variable references are correct"
            ]
        )


class ErrorHandler:
    """Centralized error handler for MCP operations."""
    
    def __init__(self):
        """Initialize the error handler."""
        self.logger = logging.getLogger(__name__)
    
    def create_error_response(
        self, 
        error: Union[Exception, MCPError], 
        request_id: Optional[str] = None
    ) -> ErrorResponse:
        """
        Create a standardized error response.
        
        Args:
            error: Exception or MCPError instance
            request_id: Optional request ID for tracking
            
        Returns:
            Standardized error response
        """
        if isinstance(error, MCPError):
            return ErrorResponse(
                error_code=error.error_code,
                message=error.message,
                details=error.details,
                field_errors=error.field_errors,
                timestamp=datetime.now(timezone.utc).isoformat(),
                request_id=request_id,
                suggestions=error.suggestions
            )
        else:
            # Handle generic exceptions
            error_code = self._get_error_code_from_exception(error)
            message = self._get_user_friendly_message(error)
            
            return ErrorResponse(
                error_code=error_code,
                message=message,
                details=str(error),
                timestamp=datetime.now(timezone.utc).isoformat(),
                request_id=request_id,
                suggestions=self._get_suggestions_for_error(error)
            )
    
    def _get_error_code_from_exception(self, error: Exception) -> str:
        """Get error code based on exception type."""
        error_type = type(error).__name__
        
        error_code_map = {
            "ValueError": "INVALID_INPUT",
            "TypeError": "TYPE_ERROR",
            "KeyError": "MISSING_FIELD",
            "ConnectionError": "CONNECTION_ERROR",
            "TimeoutError": "TIMEOUT_ERROR",
            "PermissionError": "PERMISSION_ERROR",
            "FileNotFoundError": "NOT_FOUND",
            "JSONDecodeError": "INVALID_JSON"
        }
        
        return error_code_map.get(error_type, "INTERNAL_ERROR")
    
    def _get_user_friendly_message(self, error: Exception) -> str:
        """Get user-friendly error message."""
        error_type = type(error).__name__
        
        message_map = {
            "ValueError": "Invalid input provided",
            "TypeError": "Incorrect data type provided",
            "KeyError": "Required field is missing",
            "ConnectionError": "Unable to connect to the server",
            "TimeoutError": "Operation timed out",
            "PermissionError": "Permission denied",
            "FileNotFoundError": "Resource not found",
            "JSONDecodeError": "Invalid JSON format"
        }
        
        return message_map.get(error_type, "An unexpected error occurred")
    
    def _get_suggestions_for_error(self, error: Exception) -> List[str]:
        """Get suggestions based on error type."""
        error_type = type(error).__name__
        
        suggestion_map = {
            "ValueError": [
                "Check the input format and values",
                "Ensure all required fields are provided",
                "Verify data types match requirements"
            ],
            "ConnectionError": [
                "Check network connectivity",
                "Verify server URL is correct",
                "Ensure server is running"
            ],
            "TimeoutError": [
                "Try the operation again",
                "Check server response time",
                "Consider increasing timeout values"
            ],
            "PermissionError": [
                "Verify you have the required permissions",
                "Check authentication credentials",
                "Contact administrator if needed"
            ]
        }
        
        return suggestion_map.get(error_type, [
            "Try the operation again",
            "Contact support if the issue persists"
        ])
    
    def log_error(
        self, 
        error: Exception, 
        context: Optional[Dict[str, Any]] = None,
        user_id: Optional[int] = None,
        request_id: Optional[str] = None
    ) -> None:
        """
        Log error with context information.
        
        Args:
            error: Exception to log
            context: Additional context information
            user_id: User ID if available
            request_id: Request ID for tracking
        """
        log_data = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "user_id": user_id,
            "request_id": request_id,
            "context": context or {},
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        # Add stack trace for debugging
        if self.logger.isEnabledFor(logging.DEBUG):
            log_data["stack_trace"] = traceback.format_exc()
        
        self.logger.error(f"MCP Error: {error}", extra=log_data)
    
    def handle_validation_errors(self, errors: List[str]) -> MCPValidationError:
        """Convert validation errors to MCPValidationError."""
        if not errors:
            return MCPValidationError("Validation failed")
        
        # Group errors by field if they follow "field: message" format
        field_errors = {}
        general_errors = []
        
        for error in errors:
            if ": " in error:
                field, message = error.split(": ", 1)
                if field not in field_errors:
                    field_errors[field] = []
                field_errors[field].append(message)
            else:
                general_errors.append(error)
        
        message = "Validation failed"
        if general_errors:
            message = f"Validation failed: {'; '.join(general_errors)}"
        
        return MCPValidationError(
            message=message,
            field_errors=field_errors if field_errors else None
        )
    
    def create_http_exception(
        self, 
        error: Union[Exception, MCPError],
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        request_id: Optional[str] = None
    ) -> HTTPException:
        """
        Create HTTPException from error.
        
        Args:
            error: Exception or MCPError instance
            status_code: HTTP status code
            request_id: Optional request ID
            
        Returns:
            HTTPException with standardized error response
        """
        error_response = self.create_error_response(error, request_id)
        
        # Adjust status code based on error type
        if isinstance(error, MCPValidationError):
            status_code = status.HTTP_400_BAD_REQUEST
        elif isinstance(error, MCPSecurityError):
            status_code = status.HTTP_403_FORBIDDEN
        elif isinstance(error, MCPConnectionError):
            status_code = status.HTTP_503_SERVICE_UNAVAILABLE
        elif isinstance(error, MCPConfigurationError):
            status_code = status.HTTP_400_BAD_REQUEST
        
        return HTTPException(
            status_code=status_code,
            detail=error_response.model_dump()
        )


class ValidationErrorCollector:
    """Utility for collecting and managing validation errors."""
    
    def __init__(self):
        """Initialize the error collector."""
        self.errors: List[str] = []
        self.field_errors: Dict[str, List[str]] = {}
    
    def add_error(self, message: str, field: Optional[str] = None) -> None:
        """Add an error message."""
        if field:
            if field not in self.field_errors:
                self.field_errors[field] = []
            self.field_errors[field].append(message)
        else:
            self.errors.append(message)
    
    def has_errors(self) -> bool:
        """Check if there are any errors."""
        return len(self.errors) > 0 or len(self.field_errors) > 0
    
    def get_all_errors(self) -> List[str]:
        """Get all errors as a flat list."""
        all_errors = self.errors.copy()
        
        for field, field_error_list in self.field_errors.items():
            for error in field_error_list:
                all_errors.append(f"{field}: {error}")
        
        return all_errors
    
    def to_exception(self) -> MCPValidationError:
        """Convert collected errors to MCPValidationError."""
        if not self.has_errors():
            return MCPValidationError("No validation errors")
        
        message = "Validation failed"
        if self.errors:
            message = f"Validation failed: {'; '.join(self.errors)}"
        
        return MCPValidationError(
            message=message,
            field_errors=self.field_errors if self.field_errors else None
        )


# Global error handler instance
error_handler = ErrorHandler()
