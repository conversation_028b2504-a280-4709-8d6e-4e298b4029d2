"""
Integration tests for Phase 3 AI-powered systems.

This module tests the complete integration of Phase 3 components including
the WorkflowManager integration, event handling, and end-to-end workflows.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone

from backend.agents.langgraph.core.workflow_manager import WorkflowManager
from backend.agents.langgraph.integrations.phase3_integration_service import Phase3IntegrationService
from backend.agents.langgraph.events.event_bus import event_bus
from backend.agents.langgraph.events.types import (
    WorkflowStartedEvent,
    WorkflowCompletedEvent,
    WorkflowFailedEvent
)


class TestPhase3Integration:
    """Test Phase 3 integration with WorkflowManager."""
    
    @pytest.fixture
    async def workflow_manager(self):
        """Create a WorkflowManager instance for testing."""
        manager = WorkflowManager()
        # Mock the Phase 3 service to avoid actual AI operations
        manager.phase3_service = MagicMock()
        manager.phase3_service.initialize = AsyncMock(return_value=True)
        manager.phase3_service.start_services = AsyncMock()
        manager.phase3_service.optimize_workflow = AsyncMock(return_value={
            'optimizations_applied': ['predictive_optimization'],
            'performance_prediction': {'expected_duration': 25.0}
        })
        manager.phase3_service.self_healing_system = MagicMock()
        manager.phase3_service.self_healing_system.attempt_healing = AsyncMock(return_value={
            'success': True,
            'actions_taken': ['clear_cache']
        })
        manager.phase3_enabled = True
        return manager
    
    @pytest.fixture
    def phase3_service(self):
        """Create a Phase3IntegrationService for testing."""
        service = Phase3IntegrationService()
        # Mock the AI components to avoid actual ML operations
        service.predictive_optimizer = MagicMock()
        service.self_healing_system = MagicMock()
        service.skill_matcher = MagicMock()
        service.team_formation = MagicMock()
        service.collaborative_learning = MagicMock()
        service.consensus_builder = MagicMock()
        service.collaboration_analytics = MagicMock()
        return service
    
    @pytest.mark.asyncio
    async def test_workflow_manager_phase3_initialization(self, workflow_manager):
        """Test that WorkflowManager properly initializes Phase 3 services."""
        # The initialization should have been called during setup
        assert workflow_manager.phase3_enabled is True
        assert workflow_manager.phase3_service is not None
        assert workflow_manager.workflow_monitor is not None
    
    @pytest.mark.asyncio
    async def test_workflow_creation_with_optimization(self, workflow_manager):
        """Test workflow creation includes Phase 3 optimization."""
        # Mock the necessary methods
        workflow_manager._initialize_workflow_state = MagicMock(return_value={
            'workflow_id': 'test-workflow-123',
            'user_id': 'test-user',
            'messages': []
        })
        workflow_manager._clean_state_for_serialization = MagicMock(return_value={
            'workflow_id': 'test-workflow-123',
            'user_id': 'test-user',
            'messages': []
        })
        workflow_manager._build_workflow_graph = AsyncMock(return_value=MagicMock())
        workflow_manager.checkpointer = MagicMock()
        workflow_manager.checkpointer.save_state = AsyncMock()
        
        # Create workflow
        workflow_id = await workflow_manager.create_workflow(
            user_id='test-user',
            conversation_id='test-conv',
            selected_agent='concierge-agent'
        )
        
        # Verify optimization was called
        workflow_manager.phase3_service.optimize_workflow.assert_called_once()
        assert workflow_id == 'test-workflow-123'
    
    @pytest.mark.asyncio
    async def test_workflow_execution_with_monitoring(self, workflow_manager):
        """Test workflow execution includes Phase 3 monitoring."""
        workflow_id = 'test-workflow-123'
        
        # Mock workflow cache and execution
        mock_graph = MagicMock()
        mock_graph.ainvoke = AsyncMock(return_value={
            'workflow_id': workflow_id,
            'status': 'completed',
            'messages': [],
            'execution_metrics': {'total_processing_time': 15.0}
        })
        workflow_manager.workflow_cache[workflow_id] = mock_graph
        
        # Mock checkpointer
        workflow_manager.checkpointer = MagicMock()
        workflow_manager.checkpointer.get_state = AsyncMock(return_value={
            'workflow_id': workflow_id,
            'messages': []
        })
        workflow_manager.checkpointer.save_state = AsyncMock()
        
        # Mock event bus
        with patch('backend.agents.langgraph.core.workflow_manager.event_bus') as mock_event_bus:
            mock_event_bus.emit = AsyncMock()
            
            # Execute workflow
            result = await workflow_manager.execute_workflow(workflow_id)
            
            # Verify monitoring events were emitted
            assert mock_event_bus.emit.call_count == 2  # Started and completed events
            assert result['status'] == 'completed'
    
    @pytest.mark.asyncio
    async def test_workflow_failure_with_self_healing(self, workflow_manager):
        """Test workflow failure triggers self-healing."""
        workflow_id = 'test-workflow-123'
        
        # Mock workflow cache with failing execution
        mock_graph = MagicMock()
        mock_graph.ainvoke = AsyncMock(side_effect=Exception("Test failure"))
        workflow_manager.workflow_cache[workflow_id] = mock_graph
        
        # Mock checkpointer
        workflow_manager.checkpointer = MagicMock()
        workflow_manager.checkpointer.get_state = AsyncMock(return_value={
            'workflow_id': workflow_id,
            'messages': [],
            'error_history': []
        })
        workflow_manager.checkpointer.save_state = AsyncMock()
        
        # Mock event bus
        with patch('backend.agents.langgraph.core.workflow_manager.event_bus') as mock_event_bus:
            mock_event_bus.emit = AsyncMock()
            
            # Mock successful healing that should retry
            workflow_manager.phase3_service.self_healing_system.attempt_healing.return_value = {
                'success': True,
                'actions_taken': ['clear_cache']
            }
            
            # Mock successful retry
            workflow_manager.execute_workflow = AsyncMock(return_value={
                'status': 'completed',
                'workflow_id': workflow_id
            })
            
            # This should trigger self-healing and retry
            result = await workflow_manager.execute_workflow(workflow_id)
            
            # Verify self-healing was attempted
            workflow_manager.phase3_service.self_healing_system.attempt_healing.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_phase3_service_event_handling(self, phase3_service):
        """Test Phase3IntegrationService handles events correctly."""
        # Mock the event handlers
        phase3_service._handle_workflow_started = AsyncMock()
        phase3_service._handle_workflow_completed = AsyncMock()
        phase3_service._handle_workflow_failed = AsyncMock()
        
        # Create test events
        started_event = WorkflowStartedEvent(
            workflow_id='test-123',
            workflow_type='analysis',
            user_id='user-123',
            agents_involved=['agent1', 'agent2'],
            estimated_duration=30.0
        )
        
        completed_event = WorkflowCompletedEvent(
            workflow_id='test-123',
            execution_time=25.0,
            success=True,
            results={'output': 'test result'}
        )
        
        failed_event = WorkflowFailedEvent(
            workflow_id='test-123',
            error_message='Test error',
            error_details={'type': 'timeout'},
            execution_time=35.0
        )
        
        # Simulate event handling
        await phase3_service._handle_workflow_started(started_event)
        await phase3_service._handle_workflow_completed(completed_event)
        await phase3_service._handle_workflow_failed(failed_event)
        
        # Verify handlers were called
        phase3_service._handle_workflow_started.assert_called_once()
        phase3_service._handle_workflow_completed.assert_called_once()
        phase3_service._handle_workflow_failed.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_phase3_optimization_workflow(self, phase3_service):
        """Test the complete Phase 3 optimization workflow."""
        # Mock component responses
        phase3_service.predictive_optimizer.predict_workflow_performance = AsyncMock(return_value=MagicMock(
            to_dict=MagicMock(return_value={'expected_duration': 20.0})
        ))
        phase3_service.predictive_optimizer.generate_optimization_suggestions = AsyncMock(return_value=[
            MagicMock(to_dict=MagicMock(return_value={'type': 'agent_selection', 'improvement': 15}))
        ])
        phase3_service.self_healing_system.predict_workflow_failure = AsyncMock(return_value=MagicMock(
            probability=0.3,
            to_dict=MagicMock(return_value={'probability': 0.3, 'risk_factors': []})
        ))
        phase3_service.team_formation.form_team = AsyncMock(return_value=MagicMock(
            to_dict=MagicMock(return_value={'team_id': 'team-123', 'agents': ['agent1', 'agent2']})
        ))
        
        # Test optimization
        workflow_config = {
            'workflow_id': 'test-123',
            'workflow_type': 'analysis',
            'requires_collaboration': True,
            'preferred_team_size': 2
        }
        
        result = await phase3_service.optimize_workflow('test-123', workflow_config)
        
        # Verify optimization components were called
        assert 'optimizations_applied' in result
        assert 'performance_prediction' in result
        assert result['workflow_id'] == 'test-123'
    
    @pytest.mark.asyncio
    async def test_phase3_system_status(self, phase3_service):
        """Test Phase 3 system status reporting."""
        # Mock component status
        phase3_service.predictive_optimizer.get_system_status = AsyncMock(return_value={
            'status': 'healthy',
            'models_trained': 5,
            'predictions_made': 100
        })
        phase3_service.self_healing_system.get_system_health = AsyncMock(return_value={
            'status': 'healthy',
            'healing_actions': 10,
            'success_rate': 0.9
        })
        phase3_service.collaboration_analytics.get_collaboration_insights = MagicMock(return_value={
            'active_teams': 3,
            'collaboration_efficiency': 0.85
        })
        phase3_service.collaborative_learning.get_learning_analytics = MagicMock(return_value={
            'knowledge_items': 50,
            'learning_sessions': 20
        })
        phase3_service.consensus_builder.get_decision_analytics = MagicMock(return_value={
            'decisions_made': 15,
            'consensus_rate': 0.8
        })
        
        # Get system status
        status = await phase3_service.get_system_status()
        
        # Verify status includes all components
        assert 'predictive_optimization' in status
        assert 'self_healing' in status
        assert 'collaboration' in status
        assert 'learning' in status
        assert 'consensus' in status
        assert 'integration_metrics' in status
