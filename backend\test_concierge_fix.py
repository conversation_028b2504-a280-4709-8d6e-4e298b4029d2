#!/usr/bin/env python3
"""
Test script to validate the concierge agent fixes.

This script tests the concierge agent to ensure:
1. It responds properly to user messages
2. It doesn't get stuck in infinite loops
3. The workflow terminates correctly after generating a response
4. Loop prevention mechanisms work as expected
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_concierge_agent():
    """Test the concierge agent functionality."""
    try:
        # Import required modules
        from agents.langgraph.core.workflow_manager import WorkflowManager
        from agents.langgraph.states.unified_state import create_unified_state, MessageType
        
        logger.info("🚀 Starting concierge agent test")
        
        # Create workflow manager
        workflow_manager = WorkflowManager()
        
        # Test message
        test_message = "Hello, I need help with data analysis"
        
        # Create initial state
        initial_state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation",
            workflow_type="chat_message",
            initial_message={
                "id": "test_message_1",
                "content": test_message,
                "type": MessageType.USER.value,
                "timestamp": datetime.now().isoformat()
            }
        )
        
        logger.info(f"📝 Test message: {test_message}")
        logger.info(f"🎯 Initial state created with message ID: {initial_state.get('current_message_id')}")
        
        # Execute workflow with concierge agent
        start_time = datetime.now()
        
        try:
            # Create workflow with concierge agent selected
            workflow_id = await workflow_manager.create_workflow(
                user_id="test_user",
                conversation_id="test_conversation",
                message=test_message,
                selected_agent="concierge-agent",
                workflow_type="chat_message"
            )
            
            logger.info(f"✅ Workflow created: {workflow_id}")
            
            # Execute the workflow
            result = await workflow_manager.execute_workflow(workflow_id)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"⏱️ Workflow execution completed in {execution_time:.2f} seconds")
            
            # Check results
            if result:
                logger.info("✅ Workflow execution successful")
                
                # Check for response messages
                messages = result.get("messages", [])
                agent_messages = [msg for msg in messages if msg.get("type") == MessageType.AGENT.value]
                
                if agent_messages:
                    logger.info(f"✅ Found {len(agent_messages)} agent response(s)")
                    for i, msg in enumerate(agent_messages):
                        content = msg.get("content", "")[:100]
                        logger.info(f"📨 Response {i+1}: {content}...")
                else:
                    logger.error("❌ No agent responses found")
                    return False
                
                # Check execution metrics
                execution_metrics = result.get("execution_metrics", {})
                execution_count = execution_metrics.get("agent_execution_count", 0)
                logger.info(f"📊 Agent execution count: {execution_count}")
                
                if execution_count > 10:
                    logger.warning(f"⚠️ High execution count detected: {execution_count}")
                
                # Check for loop prevention triggers
                agent_context = result.get("agent_context", {})
                concierge_context = agent_context.get("concierge-agent", {})
                rapid_execution_count = concierge_context.get("rapid_execution_count", 0)
                
                if rapid_execution_count > 0:
                    logger.warning(f"⚠️ Rapid execution detected: {rapid_execution_count} times")
                
                # Check workflow completion signals
                workflow_context = result.get("workflow_context", {})
                if workflow_context.get("concierge_completed"):
                    logger.info("✅ Concierge completion signal found")
                
                return True
            else:
                logger.error("❌ Workflow execution returned no result")
                return False
                
        except asyncio.TimeoutError:
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"❌ Workflow timed out after {execution_time:.2f} seconds")
            return False
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"❌ Workflow execution failed after {execution_time:.2f} seconds: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test setup failed: {e}")
        return False

async def test_loop_prevention():
    """Test the loop prevention mechanisms."""
    logger.info("🔄 Testing loop prevention mechanisms")
    
    try:
        from agents.langgraph.nodes.unified_persona_node import UnifiedPersonaNode
        from agents.langgraph.states.unified_state import create_unified_state, MessageType
        
        # Create a concierge node
        config = {"agent_type": "concierge", "max_agent_executions": 3}
        concierge_node = UnifiedPersonaNode("concierge-agent", config)
        
        # Create test state
        test_state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation",
            workflow_type="test",
            initial_message={
                "id": "test_message_loop",
                "content": "Test loop prevention",
                "type": MessageType.USER.value,
                "timestamp": datetime.now().isoformat()
            }
        )
        
        # Simulate multiple rapid executions
        for i in range(5):
            logger.info(f"🔄 Execution {i+1}")
            result = await concierge_node.execute(test_state)
            
            # Check if loop prevention kicked in
            messages = result.get("messages", [])
            error_messages = [msg for msg in messages if msg.get("error")]
            
            if error_messages:
                error_type = error_messages[-1].get("error_type")
                if error_type in ["max_executions_reached", "infinite_loop_detected"]:
                    logger.info(f"✅ Loop prevention triggered: {error_type}")
                    return True
            
            test_state = result
            
            # Small delay to simulate rapid execution
            await asyncio.sleep(0.01)
        
        logger.warning("⚠️ Loop prevention did not trigger as expected")
        return False
        
    except Exception as e:
        logger.error(f"❌ Loop prevention test failed: {e}")
        return False

async def main():
    """Run all tests."""
    logger.info("🧪 Starting concierge agent validation tests")
    
    # Test 1: Basic functionality
    logger.info("\n" + "="*50)
    logger.info("TEST 1: Basic Concierge Functionality")
    logger.info("="*50)
    
    test1_result = await test_concierge_agent()
    
    # Test 2: Loop prevention
    logger.info("\n" + "="*50)
    logger.info("TEST 2: Loop Prevention Mechanisms")
    logger.info("="*50)
    
    test2_result = await test_loop_prevention()
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("TEST SUMMARY")
    logger.info("="*50)
    
    logger.info(f"Basic Functionality: {'✅ PASS' if test1_result else '❌ FAIL'}")
    logger.info(f"Loop Prevention: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    overall_result = test1_result and test2_result
    logger.info(f"Overall Result: {'✅ ALL TESTS PASSED' if overall_result else '❌ SOME TESTS FAILED'}")
    
    return overall_result

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Test runner failed: {e}")
        sys.exit(1)
