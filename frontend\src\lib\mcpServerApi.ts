/**
 * Enhanced MCP Server API client for Datagenius frontend.
 * 
 * Provides functions for managing MCP servers with support for JSON configurations,
 * multiple transport types, tool discovery, and secure credential management.
 */

import { apiRequest } from './api';

// Types
export type MCPTransportType = 'http' | 'stdio' | 'sse' | 'ws';
export type MCPConfigType = 'json' | 'form' | 'auto';
export type MCPServerStatus = 'active' | 'inactive' | 'error' | 'connecting';
export type MCPInputVariableType = 'promptString' | 'promptChoice' | 'env';

export interface MCPInputDefinition {
  type: MCPInputVariableType;
  id: string;
  description: string;
  password?: boolean;
  choices?: string[];
}

export interface MCPServerDefinition {
  // HTTP/SSE/WebSocket servers
  url?: string;
  headers?: Record<string, string>;
  
  // stdio servers
  type?: string;
  command?: string;
  args?: string[];
  env?: Record<string, string>;
  env_file?: string;
}

export interface MCPJSONConfiguration {
  inputs?: MCPInputDefinition[];
  servers: Record<string, MCPServerDefinition>;
}

export interface MCPServerConfig {
  name: string;
  description?: string;
  transport_type: MCPTransportType;
  url?: string;
  headers?: Record<string, string>;
  command?: string;
  args?: string[];
  env?: Record<string, string>;
  env_file?: string;
}

export interface MCPServerCreate {
  name: string;
  description?: string;
  business_profile_id?: string;
  config_type: MCPConfigType;
  json_config?: MCPJSONConfiguration;
  server_config?: MCPServerConfig;
}

export interface MCPServerUpdate {
  name?: string;
  description?: string;
  business_profile_id?: string;
  json_config?: MCPJSONConfiguration;
  server_config?: MCPServerConfig;
}

export interface MCPTool {
  id: string;
  server_id: string;
  tool_name: string;
  tool_description?: string;
  parameters?: any;
  capabilities?: any;
  is_enabled: boolean;
  usage_count: number;
  last_used_at?: string;
  created_at: string;
}

export interface MCPResource {
  id: string;
  server_id: string;
  resource_type: string;
  resource_name: string;
  resource_description?: string;
  parameters?: any;
  created_at: string;
}

export interface MCPPrompt {
  id: string;
  server_id: string;
  prompt_name: string;
  prompt_description?: string;
  template: string;
  parameters?: any;
  created_at: string;
}

export interface MCPServer {
  id: string;
  user_id: number;
  business_profile_id?: string;
  name: string;
  description?: string;
  config_type: MCPConfigType;
  transport_type: MCPTransportType;
  configuration: any;
  status: MCPServerStatus;
  last_connected_at?: string;
  error_message?: string;
  created_at: string;
  updated_at: string;
  tools: MCPTool[];
  resources: MCPResource[];
  prompts: MCPPrompt[];
}

export interface MCPServerListResponse {
  servers: MCPServer[];
  total: number;
  page: number;
  per_page: number;
}

export interface MCPServerAction {
  action: 'start' | 'stop' | 'restart' | 'test' | 'discover';
}

export interface MCPServerActionResponse {
  success: boolean;
  message: string;
  data?: any;
}

export interface MCPDiscoveryResponse {
  tools: MCPTool[];
  resources: MCPResource[];
  prompts: MCPPrompt[];
  server_info?: any;
}

export interface MCPInputVariable {
  id: string;
  user_id: number;
  variable_id: string;
  variable_type: MCPInputVariableType;
  description?: string;
  is_password: boolean;
  created_at: string;
}

export interface MCPInputVariableCreate {
  variable_id: string;
  variable_type: MCPInputVariableType;
  description?: string;
  is_password?: boolean;
  value: string;
}

// API functions
export const mcpServerApi = {
  // Server management
  createServer: async (serverData: MCPServerCreate): Promise<MCPServer> => {
    return apiRequest('/mcp-servers', {
      method: 'POST',
      body: JSON.stringify(serverData),
    });
  },

  listServers: async (params?: {
    skip?: number;
    limit?: number;
    business_profile_id?: string;
    status?: string;
  }): Promise<MCPServerListResponse> => {
    const searchParams = new URLSearchParams();
    if (params?.skip !== undefined) searchParams.set('skip', params.skip.toString());
    if (params?.limit !== undefined) searchParams.set('limit', params.limit.toString());
    if (params?.business_profile_id) searchParams.set('business_profile_id', params.business_profile_id);
    if (params?.status) searchParams.set('status', params.status);

    const url = `/mcp-servers${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return apiRequest(url);
  },

  getServer: async (serverId: string): Promise<MCPServer> => {
    return apiRequest(`/mcp-servers/${serverId}`);
  },

  updateServer: async (serverId: string, updateData: MCPServerUpdate): Promise<MCPServer> => {
    return apiRequest(`/mcp-servers/${serverId}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  },

  deleteServer: async (serverId: string): Promise<{ message: string }> => {
    return apiRequest(`/mcp-servers/${serverId}`, {
      method: 'DELETE',
    });
  },

  // Server actions
  performAction: async (serverId: string, action: MCPServerAction): Promise<MCPServerActionResponse> => {
    return apiRequest(`/mcp-servers/${serverId}/actions`, {
      method: 'POST',
      body: JSON.stringify(action),
    });
  },

  getServerCapabilities: async (serverId: string): Promise<MCPDiscoveryResponse> => {
    return apiRequest(`/mcp-servers/${serverId}/discovery`);
  },

  getServerStatus: async (serverId: string): Promise<any> => {
    return apiRequest(`/mcp-servers/${serverId}/status`);
  },

  // Input variables
  createInputVariable: async (variableData: MCPInputVariableCreate): Promise<MCPInputVariable> => {
    return apiRequest('/mcp-servers/input-variables', {
      method: 'POST',
      body: JSON.stringify(variableData),
    });
  },

  listInputVariables: async (): Promise<MCPInputVariable[]> => {
    return apiRequest('/mcp-servers/input-variables');
  },

  // Utility functions
  testConnection: async (serverId: string): Promise<MCPServerActionResponse> => {
    return mcpServerApi.performAction(serverId, { action: 'test' });
  },

  startServer: async (serverId: string): Promise<MCPServerActionResponse> => {
    return mcpServerApi.performAction(serverId, { action: 'start' });
  },

  stopServer: async (serverId: string): Promise<MCPServerActionResponse> => {
    return mcpServerApi.performAction(serverId, { action: 'stop' });
  },

  restartServer: async (serverId: string): Promise<MCPServerActionResponse> => {
    return mcpServerApi.performAction(serverId, { action: 'restart' });
  },

  discoverCapabilities: async (serverId: string): Promise<MCPServerActionResponse> => {
    return mcpServerApi.performAction(serverId, { action: 'discover' });
  },
};

// Configuration templates for popular MCP servers
export const mcpServerTemplates: Record<string, MCPJSONConfiguration> = {
  github: {
    servers: {
      github: {
        url: "https://api.githubcopilot.com/mcp/"
      }
    }
  },
  
  filesystem: {
    inputs: [
      {
        type: "promptString",
        id: "workspace-path",
        description: "Workspace path to access",
        password: false
      }
    ],
    servers: {
      filesystem: {
        type: "stdio",
        command: "npx",
        args: ["-y", "@modelcontextprotocol/server-filesystem", "${input:workspace-path}"]
      }
    }
  },
  
  database: {
    inputs: [
      {
        type: "promptString",
        id: "db-connection",
        description: "Database connection string",
        password: true
      }
    ],
    servers: {
      database: {
        type: "stdio",
        command: "python",
        args: ["mcp_database_server.py"],
        env: {
          "DATABASE_URL": "${input:db-connection}"
        }
      }
    }
  },
  
  web_search: {
    inputs: [
      {
        type: "promptString",
        id: "search-api-key",
        description: "Search API Key",
        password: true
      }
    ],
    servers: {
      search: {
        type: "stdio",
        command: "npx",
        args: ["-y", "@modelcontextprotocol/server-brave-search"],
        env: {
          "BRAVE_API_KEY": "${input:search-api-key}"
        }
      }
    }
  }
};

// Validation functions
export const validateMCPConfiguration = (config: MCPJSONConfiguration): string[] => {
  const errors: string[] = [];
  
  if (!config.servers || Object.keys(config.servers).length === 0) {
    errors.push("At least one server must be configured");
  }
  
  for (const [serverName, serverConfig] of Object.entries(config.servers)) {
    if (!serverName.trim()) {
      errors.push("Server names cannot be empty");
    }
    
    // Validate based on server type
    if (serverConfig.type === "stdio") {
      if (!serverConfig.command) {
        errors.push(`Server '${serverName}': command is required for stdio servers`);
      }
    } else {
      if (!serverConfig.url) {
        errors.push(`Server '${serverName}': url is required for HTTP/SSE/WebSocket servers`);
      }
    }
  }
  
  // Validate input variable references
  if (config.inputs) {
    const inputIds = new Set(config.inputs.map(input => input.id));
    const configStr = JSON.stringify(config.servers);
    const variableRefs = configStr.match(/\$\{input:([^}]+)\}/g) || [];
    
    for (const ref of variableRefs) {
      const inputId = ref.match(/\$\{input:([^}]+)\}/)?.[1];
      if (inputId && !inputIds.has(inputId)) {
        errors.push(`Reference to undefined input variable: ${inputId}`);
      }
    }
  }
  
  return errors;
};
