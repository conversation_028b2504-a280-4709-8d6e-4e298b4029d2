"""
Migration Execution Manager for Extensible Persona System.

This module provides the main execution manager for migrating from legacy
agent implementations to the new extensible, configuration-driven system.
"""

import logging
import asyncio
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum

from .legacy_agent_adapter import legacy_agent_adapter
from ..config.dynamic_config_loader import dynamic_config_loader
from ..nodes.unified_persona_node import create_unified_persona_node_from_config
from ..integrations.system_integration_manager import system_integration_manager

logger = logging.getLogger(__name__)


class MigrationPhase(Enum):
    """Migration phase enumeration."""
    FOUNDATION_SETUP = "foundation_setup"
    PARALLEL_DEPLOYMENT = "parallel_deployment"
    CONFIGURATION_MIGRATION = "configuration_migration"
    GRADUAL_TRAFFIC_MIGRATION = "gradual_traffic_migration"
    LEGACY_DEPRECATION = "legacy_deprecation"
    COMPLETED = "completed"


class MigrationMode(Enum):
    """Migration mode enumeration."""
    LEGACY = "legacy"
    HYBRID = "hybrid"
    UNIFIED = "unified"


@dataclass
class MigrationStatus:
    """Migration status tracking."""
    current_phase: MigrationPhase
    mode: MigrationMode
    traffic_percentage: int
    start_time: datetime
    last_update: datetime
    errors: List[str]
    warnings: List[str]
    metrics: Dict[str, Any]


class MigrationExecutionManager:
    """
    Main manager for executing the extensible persona system migration.
    
    Provides orchestration, monitoring, and control for the migration process.
    """
    
    def __init__(self):
        """Initialize the migration execution manager."""
        self.logger = logging.getLogger(__name__)
        
        # Migration status
        self.status = MigrationStatus(
            current_phase=MigrationPhase.FOUNDATION_SETUP,
            mode=MigrationMode.LEGACY,
            traffic_percentage=0,
            start_time=datetime.now(),
            last_update=datetime.now(),
            errors=[],
            warnings=[],
            metrics={}
        )
        
        # Configuration
        self.migration_config: Dict[str, Any] = {}
        
        # Legacy agents to migrate
        self.legacy_agents = [
            "composable-analysis-ai",
            "composable-marketing-ai",
            "concierge-agent",
            "classification-agent"
        ]
        
        # Migration metrics
        self.metrics = {
            "requests_processed": {"legacy": 0, "unified": 0},
            "errors_encountered": {"legacy": 0, "unified": 0},
            "response_times": {"legacy": [], "unified": []},
            "configurations_migrated": 0,
            "tests_passed": 0,
            "rollbacks_performed": 0
        }
        
        self.logger.info("MigrationExecutionManager initialized")
    
    async def initialize_migration(self, config: Optional[Dict[str, Any]] = None) -> bool:
        """
        Initialize the migration process.
        
        Args:
            config: Optional migration configuration
            
        Returns:
            True if initialization successful
        """
        try:
            # Load migration configuration
            self.migration_config = config or await self._load_migration_config()
            
            # Validate prerequisites
            validation_result = await self._validate_prerequisites()
            if not validation_result.success:
                self.status.errors.extend(validation_result.errors)
                return False
            
            # Initialize components
            await self._initialize_components()
            
            self.logger.info("Migration initialization completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error initializing migration: {e}")
            self.status.errors.append(f"Initialization error: {str(e)}")
            return False
    
    async def execute_phase(self, phase: MigrationPhase) -> bool:
        """
        Execute a specific migration phase.
        
        Args:
            phase: Migration phase to execute
            
        Returns:
            True if phase completed successfully
        """
        try:
            self.logger.info(f"Starting migration phase: {phase.value}")
            self.status.current_phase = phase
            self.status.last_update = datetime.now()
            
            if phase == MigrationPhase.FOUNDATION_SETUP:
                success = await self._execute_foundation_setup()
            elif phase == MigrationPhase.PARALLEL_DEPLOYMENT:
                success = await self._execute_parallel_deployment()
            elif phase == MigrationPhase.CONFIGURATION_MIGRATION:
                success = await self._execute_configuration_migration()
            elif phase == MigrationPhase.GRADUAL_TRAFFIC_MIGRATION:
                success = await self._execute_gradual_traffic_migration()
            elif phase == MigrationPhase.LEGACY_DEPRECATION:
                success = await self._execute_legacy_deprecation()
            else:
                self.logger.error(f"Unknown migration phase: {phase}")
                return False
            
            if success:
                self.logger.info(f"Migration phase completed successfully: {phase.value}")
            else:
                self.logger.error(f"Migration phase failed: {phase.value}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error executing migration phase {phase.value}: {e}")
            self.status.errors.append(f"Phase {phase.value} error: {str(e)}")
            return False
    
    async def _execute_foundation_setup(self) -> bool:
        """Execute foundation setup phase."""
        try:
            # Deploy extensible components
            self.logger.info("Deploying extensible persona system components")
            
            # Verify extensible strategy system
            from ..strategies.extensible_strategy_system import extensible_strategy_registry
            strategies = extensible_strategy_registry.list_strategies()
            self.logger.info(f"Available strategies: {strategies}")
            
            # Verify dynamic configuration loader
            configs = await dynamic_config_loader.list_all_configs()
            self.logger.info(f"Available configurations: {configs}")
            
            # Verify system integration manager
            integration_status = await system_integration_manager.get_integration_status()
            self.logger.info(f"Integration status: {integration_status}")
            
            # Create initial configuration templates
            await self._create_configuration_templates()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error in foundation setup: {e}")
            return False
    
    async def _execute_parallel_deployment(self) -> bool:
        """Execute parallel deployment phase."""
        try:
            # Enable hybrid mode
            self.status.mode = MigrationMode.HYBRID
            self.status.traffic_percentage = 10  # Start with 10%
            
            # Initialize legacy adapter
            migration_status = legacy_agent_adapter.get_migration_status()
            self.logger.info(f"Legacy adapter status: {migration_status}")
            
            # Test unified system with sample requests
            test_results = await self._test_unified_system()
            
            if test_results.success:
                self.logger.info("Parallel deployment successful")
                return True
            else:
                self.logger.error(f"Parallel deployment failed: {test_results.errors}")
                return False
            
        except Exception as e:
            self.logger.error(f"Error in parallel deployment: {e}")
            return False
    
    async def _execute_configuration_migration(self) -> bool:
        """Execute configuration migration phase."""
        try:
            migrated_count = 0
            
            for agent_id in self.legacy_agents:
                try:
                    # Check if configuration already exists
                    existing_config = await dynamic_config_loader.load_config(agent_id)
                    
                    if existing_config:
                        self.logger.info(f"Configuration already exists for {agent_id}")
                        migrated_count += 1
                        continue
                    
                    # Extract configuration from legacy agent
                    legacy_config = await self._extract_legacy_configuration(agent_id)
                    
                    if legacy_config:
                        # Save to configuration system
                        success = await dynamic_config_loader.save_config(agent_id, legacy_config)
                        
                        if success:
                            migrated_count += 1
                            self.logger.info(f"Migrated configuration for {agent_id}")
                        else:
                            self.logger.error(f"Failed to save configuration for {agent_id}")
                    else:
                        self.logger.error(f"Failed to extract configuration for {agent_id}")
                        
                except Exception as e:
                    self.logger.error(f"Error migrating configuration for {agent_id}: {e}")
            
            self.metrics["configurations_migrated"] = migrated_count
            
            # Validate migrated configurations
            validation_success = await self._validate_migrated_configurations()
            
            return migrated_count == len(self.legacy_agents) and validation_success
            
        except Exception as e:
            self.logger.error(f"Error in configuration migration: {e}")
            return False
    
    async def _execute_gradual_traffic_migration(self) -> bool:
        """Execute gradual traffic migration phase."""
        try:
            # Progressive rollout schedule
            rollout_schedule = [25, 50, 75, 100]
            
            for percentage in rollout_schedule:
                self.logger.info(f"Increasing traffic to {percentage}%")
                self.status.traffic_percentage = percentage
                
                # Monitor system health for 5 minutes
                health_check_passed = await self._monitor_system_health(duration_minutes=5)
                
                if not health_check_passed:
                    self.logger.error(f"Health check failed at {percentage}% traffic")
                    await self._rollback_traffic()
                    return False
                
                # Wait before next increase
                await asyncio.sleep(300)  # 5 minutes
            
            self.status.mode = MigrationMode.UNIFIED
            self.logger.info("Traffic migration completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error in gradual traffic migration: {e}")
            return False
    
    async def _execute_legacy_deprecation(self) -> bool:
        """Execute legacy system deprecation phase."""
        try:
            # Verify complete migration
            verification_result = await self._verify_migration_completion()
            
            if not verification_result.success:
                self.logger.error(f"Migration verification failed: {verification_result.errors}")
                return False
            
            # Archive legacy code (simulation)
            self.logger.info("Archiving legacy agent implementations")
            
            # Update status
            self.status.current_phase = MigrationPhase.COMPLETED
            
            self.logger.info("Legacy deprecation completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error in legacy deprecation: {e}")
            return False
    
    async def _create_configuration_templates(self) -> None:
        """Create initial configuration templates."""
        # Templates are already created as files
        # This method would create them programmatically if needed
        self.logger.info("Configuration templates verified")
    
    async def _test_unified_system(self) -> "TestResult":
        """Test the unified system functionality."""
        try:
            test_results = []
            
            for agent_id in self.legacy_agents:
                # Create unified persona node
                persona_node = await create_unified_persona_node_from_config(agent_id)
                
                if persona_node:
                    test_results.append(True)
                    self.logger.info(f"Unified system test passed for {agent_id}")
                else:
                    test_results.append(False)
                    self.logger.error(f"Unified system test failed for {agent_id}")
            
            success = all(test_results)
            errors = [] if success else ["Some unified system tests failed"]
            
            return TestResult(success=success, errors=errors)
            
        except Exception as e:
            return TestResult(success=False, errors=[str(e)])
    
    async def _extract_legacy_configuration(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Extract configuration from legacy agent implementation."""
        # This would extract actual configuration from legacy code
        # For now, return a basic configuration
        
        agent_type_mapping = {
            "composable-analysis-ai": "analysis",
            "composable-marketing-ai": "marketing",
            "concierge-agent": "concierge",
            "classification-agent": "classification"
        }
        
        agent_type = agent_type_mapping.get(agent_id, "default")
        
        return {
            "persona_id": agent_id,
            "agent_type": agent_type,
            "name": agent_id.replace("-", " ").title(),
            "description": f"Migrated {agent_type} agent",
            "capabilities": [],
            "supported_intents": [],
            "tools": [],
            "processing_rules": {},
            "prompt_templates": {},
            "migrated_from_legacy": True,
            "migration_timestamp": datetime.now().isoformat()
        }
    
    async def _validate_migrated_configurations(self) -> bool:
        """Validate migrated configurations."""
        try:
            validation_results = []
            
            for agent_id in self.legacy_agents:
                config = await dynamic_config_loader.load_config(agent_id)
                
                if config:
                    # Basic validation
                    required_fields = ["persona_id", "agent_type", "name"]
                    has_required_fields = all(field in config for field in required_fields)
                    validation_results.append(has_required_fields)
                else:
                    validation_results.append(False)
            
            return all(validation_results)
            
        except Exception as e:
            self.logger.error(f"Error validating configurations: {e}")
            return False
    
    async def _monitor_system_health(self, duration_minutes: int = 5) -> bool:
        """Monitor system health for specified duration."""
        try:
            end_time = datetime.now() + timedelta(minutes=duration_minutes)
            
            while datetime.now() < end_time:
                # Check integration status
                integration_status = await system_integration_manager.get_integration_status()
                
                # Check for any critical issues
                if not all(status.get("status") == "connected" for status in integration_status.values()):
                    return False
                
                await asyncio.sleep(30)  # Check every 30 seconds
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error monitoring system health: {e}")
            return False
    
    async def _rollback_traffic(self) -> None:
        """Rollback traffic to previous percentage."""
        self.status.traffic_percentage = max(0, self.status.traffic_percentage - 25)
        self.metrics["rollbacks_performed"] += 1
        self.logger.warning(f"Traffic rolled back to {self.status.traffic_percentage}%")
    
    async def _verify_migration_completion(self) -> "TestResult":
        """Verify that migration is complete and successful."""
        try:
            # Check that all configurations exist
            configs_exist = []
            for agent_id in self.legacy_agents:
                config = await dynamic_config_loader.load_config(agent_id)
                configs_exist.append(config is not None)
            
            # Check that unified system is functional
            unified_functional = await self._test_unified_system()
            
            success = all(configs_exist) and unified_functional.success
            errors = [] if success else ["Migration verification failed"]
            
            return TestResult(success=success, errors=errors)
            
        except Exception as e:
            return TestResult(success=False, errors=[str(e)])
    
    async def _load_migration_config(self) -> Dict[str, Any]:
        """Load migration configuration."""
        config = await dynamic_config_loader.load_config("migration_config")
        
        if config:
            return config
        
        # Default configuration
        return {
            "enable_monitoring": True,
            "enable_rollback": True,
            "health_check_interval": 60,
            "error_threshold": 0.05,
            "performance_threshold": 1.2
        }
    
    async def _validate_prerequisites(self) -> "TestResult":
        """Validate migration prerequisites."""
        try:
            errors = []
            
            # Check that extensible system is available
            try:
                from ..strategies.extensible_strategy_system import extensible_strategy_registry
            except ImportError:
                errors.append("Extensible strategy system not available")
            
            # Check that configuration loader is available
            try:
                await dynamic_config_loader.list_all_configs()
            except Exception:
                errors.append("Dynamic configuration loader not functional")
            
            # Check that integration manager is available
            try:
                await system_integration_manager.get_integration_status()
            except Exception:
                errors.append("System integration manager not functional")
            
            return TestResult(success=len(errors) == 0, errors=errors)
            
        except Exception as e:
            return TestResult(success=False, errors=[str(e)])
    
    async def _initialize_components(self) -> None:
        """Initialize migration components."""
        # Components are already initialized through imports
        self.logger.info("Migration components initialized")
    
    def get_migration_status(self) -> Dict[str, Any]:
        """Get current migration status."""
        return {
            "status": asdict(self.status),
            "metrics": self.metrics,
            "legacy_agents": self.legacy_agents,
            "configuration": self.migration_config
        }


@dataclass
class TestResult:
    """Test result data class."""
    success: bool
    errors: List[str]


# Global migration manager instance
migration_execution_manager = MigrationExecutionManager()
