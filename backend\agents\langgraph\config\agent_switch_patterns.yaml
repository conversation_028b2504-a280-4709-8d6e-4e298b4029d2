# Agent Switch Patterns Configuration
# This file defines the natural language patterns used to detect explicit agent switch requests

patterns:
  # Basic switch patterns
  - "switch to (\\w+)"
  - "talk to (\\w+)"
  - "connect me with (\\w+)"
  - "I want to speak with (\\w+)"
  - "can I talk to (\\w+)"
  - "transfer me to (\\w+)"
  - "route me to (\\w+)"
  - "send me to (\\w+)"
  - "go to (\\w+)"
  - "take me to (\\w+)"
  
  # More natural variations
  - "let me speak with (\\w+)"
  - "put me through to (\\w+)"
  - "I need to talk to (\\w+)"
  - "can you connect me to (\\w+)"
  - "redirect me to (\\w+)"
  - "forward me to (\\w+)"
  - "hand me over to (\\w+)"
  - "pass me to (\\w+)"
  
  # Polite variations
  - "could I speak with (\\w+)"
  - "may I talk to (\\w+)"
  - "would you connect me to (\\w+)"
  - "please transfer me to (\\w+)"
  - "kindly route me to (\\w+)"

# Agent aliases for better matching
aliases:
  concierge:
    - "help"
    - "support"
    - "assistant"
    - "helper"
    - "guide"
  
  analysis:
    - "analyze"
    - "data"
    - "analytics"
    - "analyst"
    - "research"
  
  marketing:
    - "market"
    - "promotion"
    - "campaign"
    - "advertise"
    - "brand"
  
  sales:
    - "sell"
    - "revenue"
    - "deals"
    - "prospects"
    - "leads"

# Rate limiting configuration
rate_limiting:
  max_switches_per_minute: 10
  rate_limit_window_seconds: 60
  cleanup_interval_seconds: 300  # Clean up old rate limit data every 5 minutes

# Security settings
security:
  max_message_length: 1000
  allowed_agent_id_pattern: "^[a-zA-Z0-9_-]+$"
  max_agent_id_length: 50
  enable_audit_logging: true

# Performance settings
performance:
  enable_agent_lookup_cache: true
  cache_refresh_interval_seconds: 3600  # Refresh cache every hour
  max_concurrent_switches: 5
