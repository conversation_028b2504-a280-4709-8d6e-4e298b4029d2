#!/usr/bin/env python3
"""
Validation script for user-centric LangGraph routing architecture changes.

This script validates the implementation of the user-centric routing changes
documented in workflow.md without requiring pytest.
"""

import sys
import os
import logging
from typing import Dict, Any

# Add the parent directory to the path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from core.workflow_manager import WorkflowManager
from states.unified_state import (
    UnifiedDatageniusState, 
    create_unified_state,
    get_routing_target,
    ConversationMode
)
from nodes.base_agent_node import BaseAgentNode

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


class MockAgentNode(BaseAgentNode):
    """Mock agent node for testing."""
    
    def __init__(self, agent_id: str):
        super().__init__(agent_id, "test_agent")
        self.execution_calls = []
    
    async def _process_message(self, state: UnifiedDatageniusState):
        """Mock message processing."""
        self.execution_calls.append(state.get("current_message", {}))
        return state


def test_entry_point_logic():
    """Test the entry point logic changes."""
    logger.info("🧪 Testing entry point logic...")
    
    # Create workflow manager with mock agents
    manager = WorkflowManager()
    manager.agent_nodes = {
        "concierge": MockAgentNode("concierge"),
        "marketing": MockAgentNode("marketing"),
        "analysis": MockAgentNode("analysis")
    }
    
    # Test 1: Entry point with selected agent
    state = create_unified_state(
        user_id="test_user",
        conversation_id="test_conversation",
        selected_agent="marketing"
    )
    
    entry_point = manager._get_entry_point(state)
    assert entry_point == "agent_marketing", f"Expected agent_marketing, got {entry_point}"
    logger.info("✅ Entry point correctly uses selected agent")
    
    # Test 2: Entry point fallback to concierge
    state_no_selection = create_unified_state(
        user_id="test_user",
        conversation_id="test_conversation"
    )
    
    entry_point = manager._get_entry_point(state_no_selection)
    assert entry_point == "agent_concierge", f"Expected agent_concierge, got {entry_point}"
    logger.info("✅ Entry point correctly falls back to concierge")
    
    return True


def test_tool_routing_logic():
    """Test the tool routing logic changes."""
    logger.info("🧪 Testing tool routing logic...")
    
    # Create workflow manager with mock agents
    manager = WorkflowManager()
    manager.agent_nodes = {
        "concierge": MockAgentNode("concierge"),
        "marketing": MockAgentNode("marketing")
    }
    
    # Test 1: Tool routes back to selected agent
    state = create_unified_state(
        user_id="test_user",
        conversation_id="test_conversation",
        selected_agent="marketing"
    )
    
    route_target = manager._route_from_tool(state)
    assert route_target == "agent_marketing", f"Expected agent_marketing, got {route_target}"
    logger.info("✅ Tool routing correctly returns to selected agent")
    
    # Test 2: Tool routing fallback
    state_no_selection = create_unified_state(
        user_id="test_user",
        conversation_id="test_conversation"
    )
    
    route_target = manager._route_from_tool(state_no_selection)
    assert route_target == "agent_concierge", f"Expected agent_concierge, got {route_target}"
    logger.info("✅ Tool routing correctly falls back to concierge")
    
    return True


def test_simplified_routing_logic():
    """Test the simplified routing logic changes."""
    logger.info("🧪 Testing simplified routing logic...")
    
    # Test 1: Prioritizes selected agent
    state = create_unified_state(
        user_id="test_user",
        conversation_id="test_conversation",
        selected_agent="marketing"
    )
    
    target = get_routing_target(state)
    assert target == "marketing", f"Expected marketing, got {target}"
    logger.info("✅ Routing logic correctly prioritizes selected agent")
    
    # Test 2: No fallback to automatic analysis
    state_no_selection = create_unified_state(
        user_id="test_user",
        conversation_id="test_conversation"
    )
    
    # Add routing analysis that should be ignored
    state_no_selection["routing_analysis"] = {
        "target_agent": "analysis",
        "confidence": 0.9
    }
    
    target = get_routing_target(state_no_selection)
    assert target is None, f"Expected None, got {target}"
    logger.info("✅ Routing logic correctly ignores automatic analysis")
    
    # Test 3: Command pattern still works
    state_command = create_unified_state(
        user_id="test_user",
        conversation_id="test_conversation"
    )
    
    # Mock agent command
    class MockCommand:
        def __init__(self, goto):
            self.goto = goto
    
    state_command["agent_command"] = MockCommand("analysis")
    
    target = get_routing_target(state_command)
    assert target == "analysis", f"Expected analysis, got {target}"
    logger.info("✅ Command pattern routing works correctly")
    
    # Test 4: User selection overrides command
    state_override = create_unified_state(
        user_id="test_user",
        conversation_id="test_conversation",
        selected_agent="marketing"
    )
    
    state_override["agent_command"] = MockCommand("analysis")
    
    target = get_routing_target(state_override)
    assert target == "marketing", f"Expected marketing, got {target}"
    logger.info("✅ User selection correctly overrides command routing")
    
    return True


def test_conversation_continuity():
    """Test conversation continuity with selected agent."""
    logger.info("🧪 Testing conversation continuity...")
    
    # Create workflow manager
    manager = WorkflowManager()
    manager.agent_nodes = {
        "concierge": MockAgentNode("concierge"),
        "marketing": MockAgentNode("marketing")
    }
    
    # Test workflow starts at selected agent and maintains continuity
    state = create_unified_state(
        user_id="test_user",
        conversation_id="test_conversation",
        selected_agent="marketing"
    )
    
    # Entry point should be marketing
    entry_point = manager._get_entry_point(state)
    assert entry_point == "agent_marketing"
    
    # Tool should route back to marketing
    tool_route = manager._route_from_tool(state)
    assert tool_route == "agent_marketing"
    
    # Routing target should be marketing
    routing_target = get_routing_target(state)
    assert routing_target == "marketing"
    
    logger.info("✅ Conversation continuity maintained throughout workflow")
    
    return True


def main():
    """Run all validation tests."""
    logger.info("🚀 Starting user-centric routing validation...")
    
    tests = [
        test_entry_point_logic,
        test_tool_routing_logic,
        test_simplified_routing_logic,
        test_conversation_continuity
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
                logger.error(f"❌ Test failed: {test.__name__}")
        except Exception as e:
            failed += 1
            logger.error(f"❌ Test error in {test.__name__}: {e}")
    
    logger.info(f"📊 Validation Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        logger.info("🎉 All user-centric routing changes validated successfully!")
        return True
    else:
        logger.error("💥 Some validation tests failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
