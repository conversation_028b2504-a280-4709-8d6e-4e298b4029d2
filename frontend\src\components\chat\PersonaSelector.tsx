import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Loader2, User, Search, X, ShoppingCart } from "lucide-react";
import { personaApi, Persona } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";

interface PersonaSelectorProps {
  currentPersona?: Persona | null;
  onSelect: (personaId: string) => void;
  availablePersonas?: Persona[];
  isLoading?: boolean;
  onGoToMarketplace?: () => void;
}

export const PersonaSelector = ({
  onSelect,
  currentPersona,
  availablePersonas = [],
  isLoading = false,
  onGoToMarketplace
}: PersonaSelectorProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredPersonas, setFilteredPersonas] = useState<Persona[]>([]);
  const { toast } = useToast();
  const navigate = useNavigate();

  // Filter personas based on search query
  useEffect(() => {
    if (!availablePersonas || availablePersonas.length === 0) {
      setFilteredPersonas([]);
      return;
    }
    
    if (!searchQuery.trim()) {
      setFilteredPersonas(availablePersonas);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = availablePersonas.filter(persona =>
      persona.name.toLowerCase().includes(query) ||
      persona.description.toLowerCase().includes(query) ||
      persona.industry.toLowerCase().includes(query) ||
      persona.skills.some(skill => skill.toLowerCase().includes(query))
    );
    setFilteredPersonas(filtered);
  }, [searchQuery, availablePersonas]);

  // Handle selecting a persona
  const handleSelectPersona = (persona: Persona) => {
    // Always allow selection of the concierge persona (both IDs)
    if (persona.id === 'concierge' || persona.id === 'concierge-agent') {
      onSelect(persona.id);
      return;
    }

    // For other personas, check availability
    if (!persona.isAvailable) {
      // Check if it's not purchased
      if (persona.isPurchased === false) {
        toast({
          title: "Not Purchased",
          description: "You need to purchase this AI persona from the marketplace.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Not Available",
          description: "This AI persona is not currently available. Check your API keys.",
          variant: "destructive",
        });
      }
      return;
    }
    onSelect(persona.id);
  };

  // Clear search query
  const clearSearch = () => {
    setSearchQuery("");
  };

  return (
    <div className="space-y-4">
      {/* Search */}
      <div className="relative">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
        <Input
          type="text"
          placeholder="Search personas..."
          className="pl-9 pr-9"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
        {searchQuery && (
          <button
            onClick={clearSearch}
            className="absolute right-2.5 top-2.5 text-gray-500 hover:text-gray-700"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>

      {/* Personas list */}
      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      ) : filteredPersonas.length === 0 ? (
        <Card className="p-6 text-center">
          <div className="flex flex-col items-center gap-3">
            <ShoppingCart className="h-12 w-12 text-gray-300" />
            <h3 className="text-lg font-medium text-gray-700">
              {searchQuery ? "No personas found" : "No purchased personas"}
            </h3>
            <p className="text-gray-500 max-w-xs">
              {searchQuery
                ? "Try adjusting your search"
                : "You need to purchase AI personas from the marketplace to use them in chat."}
            </p>
            {searchQuery ? (
              <Button
                size="sm"
                variant="outline"
                onClick={clearSearch}
              >
                Clear search
              </Button>
            ) : (
              <Button
                size="sm"
                variant="default"
                onClick={() => onGoToMarketplace ? onGoToMarketplace() : navigate("/ai-marketplace")}
                className="mt-2"
              >
                Browse Marketplace
              </Button>
            )}
          </div>
        </Card>
      ) : (
        <div className="space-y-3 max-h-[60vh] overflow-y-auto pr-1">
          {filteredPersonas.map((persona) => (
            <Card
              key={persona.id}
              className={`p-4 cursor-pointer hover:bg-gray-50 transition-all duration-200 hover:shadow-md group ${
                currentPersona?.id === persona.id ? "ring-2 ring-brand-500 bg-brand-50" : ""
              } ${!persona.isAvailable && persona.id !== 'concierge' && persona.id !== 'concierge-agent' ? "opacity-60" : ""}`}
              onClick={() => handleSelectPersona(persona)}
            >
              <div className="flex items-start gap-3">
                <div className="p-2 bg-gray-100 rounded-md group-hover:bg-white">
                  <div className="h-10 w-10 rounded-full overflow-hidden bg-muted flex items-center justify-center">
                  <img
                    src={persona.avatarUrl || persona.imageUrl}
                    alt={persona.name}
                    className="h-full w-full object-cover"
                  />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-gray-900 truncate">{persona.name}</h3>
                    {/* Always show concierge as available */}
                    {(persona.id === 'concierge' || persona.id === 'concierge-agent') && (
                      <span className="text-xs text-green-500 font-medium">Always Available</span>
                    )}
                    {/* For other personas, show availability status */}
                    {persona.id !== 'concierge' && persona.id !== 'concierge-agent' && !persona.isAvailable && (
                      <span className="text-xs text-red-500 font-medium">
                        {persona.isPurchased === false ? "Not Purchased" : "Not Available"}
                      </span>
                    )}
                    {persona.id !== 'concierge' && persona.id !== 'concierge-agent' && persona.isPurchased && (
                      <span className="text-xs text-green-500 font-medium">Purchased</span>
                    )}
                  </div>
                  <p className="text-sm text-gray-500 truncate">{persona.industry}</p>
                  {persona.description && (
                    <p className="text-sm text-gray-500 line-clamp-2 mt-1">{persona.description}</p>
                  )}
                  <div className="flex flex-wrap gap-1 mt-2">
                    {persona.skills.slice(0, 3).map((skill, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
                      >
                        {skill}
                      </span>
                    ))}
                    {persona.skills.length > 3 && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                        +{persona.skills.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};
