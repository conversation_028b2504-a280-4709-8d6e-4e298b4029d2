/**
 * MCP Server Manager Component
 * 
 * Comprehensive management interface for MCP servers including
 * creation, configuration, monitoring, and control.
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Snackbar,
  Tooltip,
  LinearProgress,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Add as AddIcon,
  PlayArrow as StartIcon,
  Stop as StopIcon,
  Refresh as RestartIcon,
  Settings as SettingsIcon,
  Delete as DeleteIcon,
  MoreVert as MoreIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon
} from '@mui/icons-material';

import { MCPServerConfigurationModal } from './MCPServerConfigurationModal';
import { MCPServerDetailsModal } from './MCPServerDetailsModal';
import { useMCPServers } from '../../hooks/useMCPServers';
import { MCPServer, MCPServerAction } from '../../types/mcp';

interface MCPServerManagerProps {
  businessProfileId?: string;
  onServerChange?: (servers: MCPServer[]) => void;
}

export const MCPServerManager: React.FC<MCPServerManagerProps> = ({
  businessProfileId,
  onServerChange
}) => {
  const {
    servers,
    loading,
    error,
    createServer,
    updateServer,
    deleteServer,
    performAction,
    refreshServers
  } = useMCPServers(businessProfileId);

  const [configModalOpen, setConfigModalOpen] = useState(false);
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);
  const [selectedServer, setSelectedServer] = useState<MCPServer | null>(null);
  const [actionMenuAnchor, setActionMenuAnchor] = useState<null | HTMLElement>(null);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'info'
  });

  // Notify parent of server changes
  useEffect(() => {
    if (onServerChange) {
      onServerChange(servers);
    }
  }, [servers, onServerChange]);

  const handleCreateServer = useCallback(async (serverData: any) => {
    try {
      await createServer(serverData);
      setConfigModalOpen(false);
      setSnackbar({
        open: true,
        message: 'MCP server created successfully',
        severity: 'success'
      });
    } catch (error) {
      setSnackbar({
        open: true,
        message: `Failed to create server: ${error.message}`,
        severity: 'error'
      });
    }
  }, [createServer]);

  const handleServerAction = useCallback(async (
    server: MCPServer, 
    action: MCPServerAction
  ) => {
    try {
      const result = await performAction(server.id, action);
      
      if (result.success) {
        setSnackbar({
          open: true,
          message: result.message,
          severity: 'success'
        });
      } else {
        setSnackbar({
          open: true,
          message: result.message,
          severity: 'error'
        });
      }
    } catch (error) {
      setSnackbar({
        open: true,
        message: `Action failed: ${error.message}`,
        severity: 'error'
      });
    }
    
    setActionMenuAnchor(null);
  }, [performAction]);

  const handleDeleteServer = useCallback(async (server: MCPServer) => {
    if (window.confirm(`Are you sure you want to delete "${server.name}"?`)) {
      try {
        await deleteServer(server.id);
        setSnackbar({
          open: true,
          message: 'Server deleted successfully',
          severity: 'success'
        });
      } catch (error) {
        setSnackbar({
          open: true,
          message: `Failed to delete server: ${error.message}`,
          severity: 'error'
        });
      }
    }
    setActionMenuAnchor(null);
  }, [deleteServer]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'error':
        return 'error';
      case 'connecting':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <SuccessIcon fontSize="small" />;
      case 'error':
        return <ErrorIcon fontSize="small" />;
      case 'connecting':
        return <WarningIcon fontSize="small" />;
      default:
        return <InfoIcon fontSize="small" />;
    }
  };

  const renderServerCard = (server: MCPServer) => (
    <Card key={server.id} sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1 }}>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
          <Typography variant="h6" component="h3" noWrap>
            {server.name}
          </Typography>
          <Box display="flex" alignItems="center" gap={1}>
            <Chip
              icon={getStatusIcon(server.status)}
              label={server.status}
              color={getStatusColor(server.status) as any}
              size="small"
            />
            <IconButton
              size="small"
              onClick={(e) => {
                setSelectedServer(server);
                setActionMenuAnchor(e.currentTarget);
              }}
            >
              <MoreIcon />
            </IconButton>
          </Box>
        </Box>

        <Typography variant="body2" color="text.secondary" paragraph>
          {server.description || 'No description provided'}
        </Typography>

        <Box display="flex" gap={1} flexWrap="wrap" mb={2}>
          <Chip label={server.config_type} size="small" variant="outlined" />
          <Chip label={server.transport_type} size="small" variant="outlined" />
          {server.tool_count > 0 && (
            <Chip label={`${server.tool_count} tools`} size="small" color="primary" />
          )}
        </Box>

        {server.last_connected_at && (
          <Typography variant="caption" color="text.secondary">
            Last connected: {new Date(server.last_connected_at).toLocaleString()}
          </Typography>
        )}
      </CardContent>

      <Box p={2} pt={0}>
        <Box display="flex" gap={1}>
          <Tooltip title="Start Server">
            <IconButton
              size="small"
              color="success"
              disabled={server.status === 'active' || server.status === 'connecting'}
              onClick={() => handleServerAction(server, 'start')}
            >
              <StartIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Stop Server">
            <IconButton
              size="small"
              color="error"
              disabled={server.status === 'inactive'}
              onClick={() => handleServerAction(server, 'stop')}
            >
              <StopIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Restart Server">
            <IconButton
              size="small"
              color="warning"
              onClick={() => handleServerAction(server, 'restart')}
            >
              <RestartIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="View Details">
            <IconButton
              size="small"
              onClick={() => {
                setSelectedServer(server);
                setDetailsModalOpen(true);
              }}
            >
              <SettingsIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
    </Card>
  );

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          MCP Servers
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setConfigModalOpen(true)}
          disabled={loading}
        >
          Add Server
        </Button>
      </Box>

      {/* Loading indicator */}
      {loading && <LinearProgress sx={{ mb: 2 }} />}

      {/* Error display */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Server grid */}
      <Grid container spacing={3}>
        {servers.map(renderServerCard)}
      </Grid>

      {/* Empty state */}
      {!loading && servers.length === 0 && (
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          py={8}
        >
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No MCP servers configured
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Add your first MCP server to start integrating external tools and services.
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setConfigModalOpen(true)}
          >
            Add Your First Server
          </Button>
        </Box>
      )}

      {/* Configuration Modal */}
      <MCPServerConfigurationModal
        open={configModalOpen}
        onClose={() => setConfigModalOpen(false)}
        onSubmit={handleCreateServer}
        businessProfileId={businessProfileId}
      />

      {/* Details Modal */}
      {selectedServer && (
        <MCPServerDetailsModal
          open={detailsModalOpen}
          onClose={() => {
            setDetailsModalOpen(false);
            setSelectedServer(null);
          }}
          server={selectedServer}
          onUpdate={updateServer}
          onAction={handleServerAction}
        />
      )}

      {/* Action Menu */}
      <Menu
        anchorEl={actionMenuAnchor}
        open={Boolean(actionMenuAnchor)}
        onClose={() => setActionMenuAnchor(null)}
      >
        <MenuItem onClick={() => handleServerAction(selectedServer!, 'test')}>
          <ListItemIcon>
            <InfoIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Test Connection</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={() => handleServerAction(selectedServer!, 'discover')}>
          <ListItemIcon>
            <RefreshIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Discover Capabilities</ListItemText>
        </MenuItem>
        
        <MenuItem
          onClick={() => {
            setDetailsModalOpen(true);
            setActionMenuAnchor(null);
          }}
        >
          <ListItemIcon>
            <SettingsIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Configure</ListItemText>
        </MenuItem>
        
        <MenuItem
          onClick={() => handleDeleteServer(selectedServer!)}
          sx={{ color: 'error.main' }}
        >
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Delete</ListItemText>
        </MenuItem>
      </Menu>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};
