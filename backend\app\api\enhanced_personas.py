"""
Enhanced Personas API endpoints for the enhanced marketplace architecture.

This module provides API endpoints for enhanced personas functionality including
marketplace personas retrieval, workflow creation, and adaptive workflow management.
"""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from ..database import get_db
from ..services.enhanced_marketplace_service import (
    persona_config_service,
    agent_plugin_service,
    workflow_adaptation_service
)

# Import LangGraph components
try:
    from agents.langgraph.core.marketplace_agent_factory import (
        MarketplaceAgentFactory, 
        UserContext,
        AgentDefinition
    )
    from agents.langgraph.core.persona_workflow_builder import PersonaWorkflowBuilder
    from agents.langgraph.core.adaptive_workflow_manager import AdaptiveWorkflowManager
    LANGGRAPH_AVAILABLE = True
except ImportError:
    LANGGRAPH_AVAILABLE = False

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/enhanced-personas", tags=["Enhanced Personas"])

# Initialize LangGraph components
marketplace_agent_factory = MarketplaceAgentFactory() if LANGGRAPH_AVAILABLE else None
persona_workflow_builder = PersonaWorkflowBuilder() if LANGGRAPH_AVAILABLE else None
adaptive_workflow_manager = AdaptiveWorkflowManager() if LANGGRAPH_AVAILABLE else None


@router.get("/marketplace/{user_id}")
async def get_user_marketplace_personas(
    user_id: str,
    db: Session = Depends(get_db)
):
    """Get user's purchased personas from marketplace."""
    
    if not LANGGRAPH_AVAILABLE:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Enhanced marketplace functionality not available"
        )
    
    try:
        logger.info(f"Getting marketplace personas for user {user_id}")
        
        # Create user context
        user_context = UserContext(user_id=user_id)
        
        # Discover marketplace agents
        agent_definitions = await marketplace_agent_factory.discover_marketplace_agents(user_id)
        
        # Convert to response format
        personas = []
        for agent in agent_definitions:
            persona_data = {
                "agent_id": agent.agent_id,
                "name": agent.name,
                "description": agent.description,
                "industry": agent.industry,
                "capabilities": agent.capabilities,
                "is_purchased": agent.is_purchased,
                "is_custom": agent.is_custom,
                "plugin_id": agent.plugin_id,
                "marketplace_listing_id": agent.marketplace_listing_id
            }
            
            # Add configuration if available
            if agent.configuration:
                persona_data["configuration"] = agent.configuration
            
            personas.append(persona_data)
        
        return {
            "user_id": user_id,
            "purchased_personas": len([p for p in personas if p["is_purchased"]]),
            "total_personas": len(personas),
            "personas": personas
        }
        
    except Exception as e:
        logger.error(f"Error getting marketplace personas: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve marketplace personas: {str(e)}"
        )


@router.post("/workflows/create")
async def create_persona_workflow(
    persona_id: str,
    user_id: str,
    business_profile: Optional[Dict[str, Any]] = None,
    db: Session = Depends(get_db)
):
    """Create optimized workflow for persona and user context."""
    
    if not LANGGRAPH_AVAILABLE:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Enhanced marketplace functionality not available"
        )
    
    try:
        logger.info(f"Creating persona workflow for {persona_id} and user {user_id}")
        
        # Create user context
        user_context = UserContext(
            user_id=user_id,
            business_profile=business_profile
        )
        
        # Create persona workflow
        workflow = await persona_workflow_builder.create_persona_workflow(
            persona_id, user_context, business_profile
        )
        
        # Store workflow configuration in database
        workflow_config = {
            "persona_id": persona_id,
            "user_id": user_id,
            "business_profile": business_profile,
            "workflow_nodes": len(workflow.nodes) if hasattr(workflow, 'nodes') else 0,
            "created_at": "now"
        }
        
        # Create persona configuration if it doesn't exist
        existing_config = persona_config_service.get_persona_configuration(db, persona_id)
        if not existing_config:
            persona_config_service.create_persona_configuration(
                db=db,
                persona_id=persona_id,
                configuration=workflow_config,
                industry_specialization=business_profile.get("industry") if business_profile else None
            )
        
        return {
            "workflow_id": f"persona_{persona_id}_{user_id}",
            "persona_id": persona_id,
            "user_id": user_id,
            "status": "created",
            "nodes": len(workflow.nodes) if hasattr(workflow, 'nodes') else 0,
            "business_profile_applied": business_profile is not None
        }
        
    except Exception as e:
        logger.error(f"Error creating persona workflow: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create persona workflow: {str(e)}"
        )


@router.get("/configurations/{persona_id}")
async def get_persona_configuration(
    persona_id: str,
    db: Session = Depends(get_db)
):
    """Get persona configuration."""
    
    try:
        config = persona_config_service.get_persona_configuration(db, persona_id)
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Persona configuration not found for {persona_id}"
            )
        
        return {
            "persona_id": config.persona_id,
            "configuration": config.configuration,
            "industry_specialization": config.industry_specialization,
            "methodology_framework": config.methodology_framework,
            "enable_cross_agent_intelligence": config.enable_cross_agent_intelligence,
            "specialized_tools": config.specialized_tools,
            "compliance_requirements": config.compliance_requirements,
            "workflow_patterns": config.workflow_patterns,
            "performance_optimization": config.performance_optimization,
            "created_at": config.created_at.isoformat(),
            "updated_at": config.updated_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting persona configuration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve persona configuration: {str(e)}"
        )


@router.put("/configurations/{persona_id}")
async def update_persona_configuration(
    persona_id: str,
    updates: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """Update persona configuration."""
    
    try:
        config = persona_config_service.update_persona_configuration(
            db, persona_id, updates
        )
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Persona configuration not found for {persona_id}"
            )
        
        return {
            "persona_id": config.persona_id,
            "status": "updated",
            "updated_at": config.updated_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating persona configuration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update persona configuration: {str(e)}"
        )


@router.get("/plugins")
async def get_available_plugins(
    status_filter: Optional[str] = None,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Get available agent plugins."""
    
    try:
        if status_filter:
            plugins = agent_plugin_service.get_plugins_by_status(db, status_filter, limit)
        else:
            # Get all plugins (would need a service method for this)
            plugins = agent_plugin_service.get_plugins_by_status(db, "approved", limit)
        
        return {
            "plugins": [
                {
                    "plugin_id": plugin.plugin_id,
                    "name": plugin.name,
                    "version": plugin.version,
                    "author": plugin.author,
                    "description": plugin.description,
                    "status": plugin.status,
                    "supported_industries": plugin.supported_industries,
                    "required_tools": plugin.required_tools,
                    "compliance_certifications": plugin.compliance_certifications,
                    "created_at": plugin.created_at.isoformat()
                }
                for plugin in plugins
            ],
            "total": len(plugins)
        }
        
    except Exception as e:
        logger.error(f"Error getting available plugins: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve plugins: {str(e)}"
        )


@router.get("/adaptations/{user_id}")
async def get_user_workflow_adaptations(
    user_id: int,
    persona_id: Optional[str] = None,
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """Get workflow adaptations for a user."""
    
    try:
        adaptations = workflow_adaptation_service.get_user_adaptations(
            db, user_id, persona_id, limit
        )
        
        return {
            "user_id": user_id,
            "adaptations": [
                {
                    "adaptation_id": adaptation.adaptation_id,
                    "workflow_id": adaptation.workflow_id,
                    "persona_id": adaptation.persona_id,
                    "adaptation_type": adaptation.adaptation_type,
                    "adaptation_triggers": adaptation.adaptation_triggers,
                    "performance_data": adaptation.performance_data,
                    "success_metrics": adaptation.success_metrics,
                    "created_at": adaptation.created_at.isoformat()
                }
                for adaptation in adaptations
            ],
            "total": len(adaptations)
        }
        
    except Exception as e:
        logger.error(f"Error getting workflow adaptations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve workflow adaptations: {str(e)}"
        )
