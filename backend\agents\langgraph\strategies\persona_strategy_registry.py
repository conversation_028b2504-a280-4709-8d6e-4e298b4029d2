"""
Persona Strategy Registry for LangGraph-based Datagenius System.

This module provides a registry system for managing persona strategies,
enabling dynamic strategy loading and configuration-driven behavior.

DEPRECATED: This registry is deprecated in favor of the extensible_strategy_system.
All personas now use ConfigurablePersonaStrategy with YAML configuration.
"""

import logging
import importlib
from typing import Dict, Any, List, Optional, Type, Union
from pathlib import Path
import yaml

# Import only the extensible strategy system - no hardcoded strategies
from .extensible_strategy_system import ConfigurablePersonaStrategy, ExtensiblePersonaStrategy

logger = logging.getLogger(__name__)


class PersonaStrategyRegistry:
    """
    DEPRECATED: Registry for managing persona strategies.

    This registry is deprecated in favor of the extensible_strategy_system.
    All personas now use ConfigurablePersonaStrategy with YAML configuration.

    This class is maintained for backward compatibility but should not be used
    for new implementations.
    """

    def __init__(self):
        """Initialize the persona strategy registry."""
        self.logger = logging.getLogger(__name__)

        # All strategies now use ConfigurablePersonaStrategy
        self.builtin_strategies: Dict[str, Type[ExtensiblePersonaStrategy]] = {
            "analysis": ConfigurablePersonaStrategy,
            "marketing": ConfigurablePersonaStrategy,
            "concierge": ConfigurablePersonaStrategy,
            "classification": ConfigurablePersonaStrategy,
            "default": ConfigurablePersonaStrategy
        }

        # Custom strategy mappings (deprecated - all use ConfigurablePersonaStrategy)
        self.custom_strategies: Dict[str, Type[ExtensiblePersonaStrategy]] = {}

        # Strategy configurations
        self.strategy_configs: Dict[str, Dict[str, Any]] = {}

        # Strategy instances cache
        self.strategy_cache: Dict[str, ExtensiblePersonaStrategy] = {}
        
        # Load strategy configurations
        self._load_strategy_configurations()
        
        self.logger.info("PersonaStrategyRegistry initialized")
    
    def _load_strategy_configurations(self) -> None:
        """Load strategy configurations from YAML files."""
        try:
            config_dir = Path(__file__).parent.parent / "config" / "strategies"
            if config_dir.exists():
                for config_file in config_dir.glob("*.yaml"):
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config = yaml.safe_load(f)
                        
                        strategy_id = config_file.stem
                        self.strategy_configs[strategy_id] = config
                        
                        self.logger.info(f"Loaded strategy config: {strategy_id}")
                        
                    except Exception as e:
                        self.logger.error(f"Error loading strategy config {config_file}: {e}")
            else:
                self.logger.info("No strategy configuration directory found, using defaults")
                
        except Exception as e:
            self.logger.error(f"Error loading strategy configurations: {e}")
    
    def register_strategy(
        self,
        strategy_id: str,
        strategy_class: Type[ExtensiblePersonaStrategy],
        config: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        DEPRECATED: Register a custom persona strategy.

        All strategies now use ConfigurablePersonaStrategy with YAML configuration.

        Args:
            strategy_id: Unique identifier for the strategy
            strategy_class: Strategy class to register (should be ConfigurablePersonaStrategy)
            config: Optional configuration for the strategy
        """
        self.custom_strategies[strategy_id] = strategy_class
        
        if config:
            self.strategy_configs[strategy_id] = config
        
        # Clear cache for this strategy
        if strategy_id in self.strategy_cache:
            del self.strategy_cache[strategy_id]
        
        self.logger.info(f"Registered custom strategy: {strategy_id}")
    
    def get_strategy(
        self,
        strategy_id: str,
        config: Optional[Dict[str, Any]] = None
    ) -> ExtensiblePersonaStrategy:
        """
        DEPRECATED: Get a persona strategy instance.

        All strategies now use ConfigurablePersonaStrategy with YAML configuration.

        Args:
            strategy_id: Strategy identifier
            config: Optional configuration override

        Returns:
            ExtensiblePersonaStrategy instance (always ConfigurablePersonaStrategy)
        """
        # Check cache first
        cache_key = f"{strategy_id}_{hash(str(config)) if config else 'default'}"
        if cache_key in self.strategy_cache:
            return self.strategy_cache[cache_key]
        
        # Get strategy class
        strategy_class = self._get_strategy_class(strategy_id)
        
        # Prepare configuration
        strategy_config = config or self.strategy_configs.get(strategy_id, {})
        
        # Create strategy instance
        strategy_instance = strategy_class(strategy_config)
        
        # Cache the instance
        self.strategy_cache[cache_key] = strategy_instance
        
        return strategy_instance
    
    def _get_strategy_class(self, strategy_id: str) -> Type[ExtensiblePersonaStrategy]:
        """
        DEPRECATED: Get strategy class by ID.

        All strategies now return ConfigurablePersonaStrategy.

        Args:
            strategy_id: Strategy identifier

        Returns:
            ExtensiblePersonaStrategy class (always ConfigurablePersonaStrategy)
        """
        # Check custom strategies first
        if strategy_id in self.custom_strategies:
            return self.custom_strategies[strategy_id]
        
        # Check built-in strategies
        if strategy_id in self.builtin_strategies:
            return self.builtin_strategies[strategy_id]
        
        # Try to load dynamically
        try:
            strategy_class = self._load_strategy_dynamically(strategy_id)
            if strategy_class:
                self.custom_strategies[strategy_id] = strategy_class
                return strategy_class
        except Exception as e:
            self.logger.error(f"Error loading strategy {strategy_id} dynamically: {e}")
        
        # Fallback to default strategy
        self.logger.warning(f"Strategy {strategy_id} not found, using default")
        return self.builtin_strategies["default"]
    
    def _load_strategy_dynamically(self, strategy_id: str) -> Optional[Type[ExtensiblePersonaStrategy]]:
        """
        DEPRECATED: Attempt to load strategy dynamically.

        This method is deprecated as all strategies now use ConfigurablePersonaStrategy.

        Args:
            strategy_id: Strategy identifier

        Returns:
            ExtensiblePersonaStrategy class if found, None otherwise
        """
        # Try common module patterns
        module_patterns = [
            f"agents.langgraph.strategies.{strategy_id}_strategy",
            f"agents.strategies.{strategy_id}_strategy",
            f"strategies.{strategy_id}_strategy"
        ]
        
        for module_pattern in module_patterns:
            try:
                module = importlib.import_module(module_pattern)
                
                # Look for strategy class (deprecated - return ConfigurablePersonaStrategy)
                class_name = f"{strategy_id.title()}PersonaStrategy"
                if hasattr(module, class_name):
                    strategy_class = getattr(module, class_name)
                    if issubclass(strategy_class, ExtensiblePersonaStrategy):
                        # Return ConfigurablePersonaStrategy instead of hardcoded class
                        return ConfigurablePersonaStrategy
                        
            except ImportError:
                continue
        
        return None
    
    def list_strategies(self) -> List[str]:
        """
        List all available strategy IDs.
        
        Returns:
            List of strategy identifiers
        """
        all_strategies = set()
        all_strategies.update(self.builtin_strategies.keys())
        all_strategies.update(self.custom_strategies.keys())
        all_strategies.update(self.strategy_configs.keys())
        
        return sorted(list(all_strategies))
    
    def get_strategy_info(self, strategy_id: str) -> Dict[str, Any]:
        """
        Get information about a strategy.
        
        Args:
            strategy_id: Strategy identifier
            
        Returns:
            Strategy information dictionary
        """
        strategy_class = self._get_strategy_class(strategy_id)
        config = self.strategy_configs.get(strategy_id, {})
        
        return {
            "strategy_id": strategy_id,
            "class_name": strategy_class.__name__,
            "module": strategy_class.__module__,
            "is_builtin": strategy_id in self.builtin_strategies,
            "is_custom": strategy_id in self.custom_strategies,
            "has_config": strategy_id in self.strategy_configs,
            "config": config,
            "description": strategy_class.__doc__ or "No description available"
        }
    
    def validate_strategy(self, strategy_id: str) -> Dict[str, Any]:
        """
        Validate a strategy configuration.
        
        Args:
            strategy_id: Strategy identifier
            
        Returns:
            Validation results
        """
        try:
            strategy_class = self._get_strategy_class(strategy_id)
            config = self.strategy_configs.get(strategy_id, {})
            
            # Try to create an instance
            strategy_instance = strategy_class(config)
            
            # Basic validation checks
            validation_results = {
                "valid": True,
                "strategy_id": strategy_id,
                "class_found": True,
                "instance_created": True,
                "has_required_methods": True,
                "errors": []
            }
            
            # Check required methods
            required_methods = ["process_message", "get_system_prompt", "get_specialized_tools"]
            for method_name in required_methods:
                if not hasattr(strategy_instance, method_name):
                    validation_results["has_required_methods"] = False
                    validation_results["errors"].append(f"Missing required method: {method_name}")
            
            if validation_results["errors"]:
                validation_results["valid"] = False
            
            return validation_results
            
        except Exception as e:
            return {
                "valid": False,
                "strategy_id": strategy_id,
                "class_found": False,
                "instance_created": False,
                "has_required_methods": False,
                "errors": [str(e)]
            }
    
    def clear_cache(self) -> None:
        """Clear the strategy instance cache."""
        self.strategy_cache.clear()
        self.logger.info("Cleared strategy cache")
    
    def reload_configurations(self) -> None:
        """Reload strategy configurations from files."""
        self.strategy_configs.clear()
        self.clear_cache()
        self._load_strategy_configurations()
        self.logger.info("Reloaded strategy configurations")


# Global registry instance
persona_strategy_registry = PersonaStrategyRegistry()
