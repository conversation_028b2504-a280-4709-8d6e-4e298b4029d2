"""
System Integration Manager for Complete Legacy Migration

This module manages the complete integration of migrated legacy agents
with all existing systems including FastAPI, PostgreSQL, MCP tools,
and business profile integration.
"""

import logging
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime

from .unified_migration_adapter import get_unified_migration_adapter
from ..config.dynamic_config_loader import get_dynamic_config_loader
from ..integrations.system_integration_manager import SystemIntegrationManager
from ..tools.mcp.unified_tool_manager import get_unified_tool_manager
from ..intelligence.cross_agent_intelligence_manager import get_cross_agent_intelligence_manager

logger = logging.getLogger(__name__)


class CompleteMigrationIntegrationManager:
    """
    Manages complete integration of migrated legacy agents with all system components.
    
    This manager ensures that migrated personas work seamlessly with:
    - FastAPI endpoints and routing
    - PostgreSQL database operations
    - MCP tool integration
    - Business profile context injection
    - Cross-agent intelligence capabilities
    - Performance monitoring and metrics
    """
    
    def __init__(self):
        """Initialize the complete migration integration manager."""
        self.logger = logging.getLogger(__name__)
        
        # Component managers
        self.migration_adapter = None
        self.config_loader = None
        self.system_integration = None
        self.tool_manager = None
        self.intelligence_manager = None
        
        # Integration status tracking
        self.integration_status = {
            "migration_adapter": False,
            "config_loader": False,
            "system_integration": False,
            "tool_manager": False,
            "intelligence_manager": False,
            "database_integration": False,
            "fastapi_integration": False,
            "mcp_tools_integration": False,
            "business_profile_integration": False,
            "cross_agent_intelligence": False
        }
        
        # Migration tracking
        self.migration_progress = {
            "total_agents": 4,
            "migrated_agents": 0,
            "integration_complete": False,
            "start_time": None,
            "completion_time": None
        }
        
        self.logger.info("Initialized CompleteMigrationIntegrationManager")
    
    async def initialize_all_components(self) -> bool:
        """
        Initialize all required components for complete migration integration.
        
        Returns:
            True if all components initialized successfully, False otherwise
        """
        try:
            self.logger.info("Initializing all migration integration components...")
            
            # Initialize migration adapter
            self.migration_adapter = await get_unified_migration_adapter()
            self.integration_status["migration_adapter"] = True
            self.logger.info("✅ Migration adapter initialized")
            
            # Initialize config loader
            self.config_loader = await get_dynamic_config_loader()
            self.integration_status["config_loader"] = True
            self.logger.info("✅ Config loader initialized")
            
            # Initialize system integration manager
            self.system_integration = SystemIntegrationManager()
            await self.system_integration.initialize()
            self.integration_status["system_integration"] = True
            self.logger.info("✅ System integration manager initialized")
            
            # Initialize tool manager
            self.tool_manager = await get_unified_tool_manager()
            self.integration_status["tool_manager"] = True
            self.logger.info("✅ Tool manager initialized")
            
            # Initialize cross-agent intelligence
            self.intelligence_manager = await get_cross_agent_intelligence_manager()
            self.integration_status["intelligence_manager"] = True
            self.logger.info("✅ Cross-agent intelligence manager initialized")
            
            # Verify all components are ready
            all_initialized = all(self.integration_status.values())
            
            if all_initialized:
                self.logger.info("🎉 All migration integration components initialized successfully")
            else:
                failed_components = [k for k, v in self.integration_status.items() if not v]
                self.logger.error(f"❌ Failed to initialize components: {failed_components}")
            
            return all_initialized
            
        except Exception as e:
            self.logger.error(f"Error initializing migration integration components: {e}")
            return False
    
    async def execute_complete_migration(self) -> Dict[str, Any]:
        """
        Execute complete migration of all legacy agents with full system integration.
        
        Returns:
            Migration execution results
        """
        try:
            self.migration_progress["start_time"] = datetime.now()
            self.logger.info("🚀 Starting complete legacy agent migration...")
            
            # Step 1: Initialize all components
            if not await self.initialize_all_components():
                return {
                    "success": False,
                    "error": "Failed to initialize required components",
                    "integration_status": self.integration_status
                }
            
            # Step 2: Validate all legacy agent configurations
            validation_results = await self._validate_all_configurations()
            if not validation_results["success"]:
                return {
                    "success": False,
                    "error": "Configuration validation failed",
                    "validation_results": validation_results
                }
            
            # Step 3: Migrate each legacy agent
            migration_results = await self._migrate_all_agents()
            if not migration_results["success"]:
                return {
                    "success": False,
                    "error": "Agent migration failed",
                    "migration_results": migration_results
                }
            
            # Step 4: Integrate with database systems
            database_integration = await self._integrate_with_database()
            self.integration_status["database_integration"] = database_integration["success"]
            
            # Step 5: Integrate with FastAPI endpoints
            fastapi_integration = await self._integrate_with_fastapi()
            self.integration_status["fastapi_integration"] = fastapi_integration["success"]
            
            # Step 6: Integrate MCP tools
            mcp_integration = await self._integrate_mcp_tools()
            self.integration_status["mcp_tools_integration"] = mcp_integration["success"]
            
            # Step 7: Integrate business profile context
            business_profile_integration = await self._integrate_business_profiles()
            self.integration_status["business_profile_integration"] = business_profile_integration["success"]
            
            # Step 8: Enable cross-agent intelligence
            cross_agent_integration = await self._enable_cross_agent_intelligence()
            self.integration_status["cross_agent_intelligence"] = cross_agent_integration["success"]
            
            # Step 9: Validate complete integration
            final_validation = await self._validate_complete_integration()
            
            # Update completion status
            self.migration_progress["completion_time"] = datetime.now()
            self.migration_progress["integration_complete"] = final_validation["success"]
            
            # Prepare final results
            results = {
                "success": final_validation["success"],
                "migration_progress": self.migration_progress,
                "integration_status": self.integration_status,
                "validation_results": validation_results,
                "migration_results": migration_results,
                "database_integration": database_integration,
                "fastapi_integration": fastapi_integration,
                "mcp_integration": mcp_integration,
                "business_profile_integration": business_profile_integration,
                "cross_agent_integration": cross_agent_integration,
                "final_validation": final_validation
            }
            
            if results["success"]:
                self.logger.info("🎉 Complete legacy agent migration executed successfully!")
            else:
                self.logger.error("❌ Complete migration execution failed")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error executing complete migration: {e}")
            return {
                "success": False,
                "error": str(e),
                "migration_progress": self.migration_progress,
                "integration_status": self.integration_status
            }
    
    async def _validate_all_configurations(self) -> Dict[str, Any]:
        """Validate all legacy agent configurations."""
        try:
            self.logger.info("Validating all legacy agent configurations...")
            
            legacy_agents = [
                "composable-analysis-ai",
                "composable-marketing-ai", 
                "concierge-agent",
                "classification-agent"
            ]
            
            validation_results = {}
            all_valid = True
            
            for agent_id in legacy_agents:
                is_valid = await self.migration_adapter.validate_migration_compatibility(agent_id)
                validation_results[agent_id] = is_valid
                if not is_valid:
                    all_valid = False
                    self.logger.error(f"❌ Configuration validation failed for {agent_id}")
                else:
                    self.logger.info(f"✅ Configuration validated for {agent_id}")
            
            return {
                "success": all_valid,
                "agent_validations": validation_results,
                "total_agents": len(legacy_agents),
                "valid_agents": sum(validation_results.values())
            }
            
        except Exception as e:
            self.logger.error(f"Error validating configurations: {e}")
            return {"success": False, "error": str(e)}
    
    async def _migrate_all_agents(self) -> Dict[str, Any]:
        """Migrate all legacy agents to unified persona system."""
        try:
            self.logger.info("Migrating all legacy agents...")
            
            migration_status = await self.migration_adapter.get_migration_status()
            
            self.migration_progress["migrated_agents"] = migration_status.get("migrated_agents", 0)
            
            return {
                "success": migration_status.get("migration_complete", False),
                "migration_status": migration_status
            }
            
        except Exception as e:
            self.logger.error(f"Error migrating agents: {e}")
            return {"success": False, "error": str(e)}
    
    async def _integrate_with_database(self) -> Dict[str, Any]:
        """Integrate migrated personas with PostgreSQL database."""
        try:
            self.logger.info("Integrating with PostgreSQL database...")
            
            # Test database connectivity and persona storage
            database_ready = await self.system_integration.verify_database_connectivity()
            
            if database_ready:
                self.logger.info("✅ Database integration successful")
            else:
                self.logger.error("❌ Database integration failed")
            
            return {"success": database_ready}
            
        except Exception as e:
            self.logger.error(f"Error integrating with database: {e}")
            return {"success": False, "error": str(e)}
    
    async def _integrate_with_fastapi(self) -> Dict[str, Any]:
        """Integrate migrated personas with FastAPI endpoints."""
        try:
            self.logger.info("Integrating with FastAPI endpoints...")
            
            # Verify API endpoint compatibility
            api_ready = await self.system_integration.verify_api_compatibility()
            
            if api_ready:
                self.logger.info("✅ FastAPI integration successful")
            else:
                self.logger.error("❌ FastAPI integration failed")
            
            return {"success": api_ready}
            
        except Exception as e:
            self.logger.error(f"Error integrating with FastAPI: {e}")
            return {"success": False, "error": str(e)}
    
    async def _integrate_mcp_tools(self) -> Dict[str, Any]:
        """Integrate MCP tools with migrated personas."""
        try:
            self.logger.info("Integrating MCP tools...")
            
            # Verify tool availability for each persona
            tools_ready = await self.tool_manager.verify_all_tools_available()
            
            if tools_ready:
                self.logger.info("✅ MCP tools integration successful")
            else:
                self.logger.error("❌ MCP tools integration failed")
            
            return {"success": tools_ready}
            
        except Exception as e:
            self.logger.error(f"Error integrating MCP tools: {e}")
            return {"success": False, "error": str(e)}
    
    async def _integrate_business_profiles(self) -> Dict[str, Any]:
        """Integrate business profile context with migrated personas."""
        try:
            self.logger.info("Integrating business profile context...")
            
            # Verify business profile integration
            profiles_ready = await self.system_integration.verify_business_profile_integration()
            
            if profiles_ready:
                self.logger.info("✅ Business profile integration successful")
            else:
                self.logger.error("❌ Business profile integration failed")
            
            return {"success": profiles_ready}
            
        except Exception as e:
            self.logger.error(f"Error integrating business profiles: {e}")
            return {"success": False, "error": str(e)}
    
    async def _enable_cross_agent_intelligence(self) -> Dict[str, Any]:
        """Enable cross-agent intelligence for migrated personas."""
        try:
            self.logger.info("Enabling cross-agent intelligence...")
            
            # Verify cross-agent intelligence capabilities
            intelligence_ready = await self.intelligence_manager.verify_intelligence_capabilities()
            
            if intelligence_ready:
                self.logger.info("✅ Cross-agent intelligence enabled successfully")
            else:
                self.logger.error("❌ Cross-agent intelligence enablement failed")
            
            return {"success": intelligence_ready}
            
        except Exception as e:
            self.logger.error(f"Error enabling cross-agent intelligence: {e}")
            return {"success": False, "error": str(e)}
    
    async def _validate_complete_integration(self) -> Dict[str, Any]:
        """Validate that complete integration is working properly."""
        try:
            self.logger.info("Validating complete integration...")
            
            # Test each migrated persona with a sample message
            test_results = {}
            
            legacy_agents = [
                "composable-analysis-ai",
                "composable-marketing-ai",
                "concierge-agent", 
                "classification-agent"
            ]
            
            all_tests_passed = True
            
            for agent_id in legacy_agents:
                try:
                    # Test message processing
                    test_response = await self.migration_adapter.process_legacy_message(
                        agent_id=agent_id,
                        user_id=1,
                        message="Hello, this is a test message",
                        conversation_id="test_conversation",
                        context={"test": True}
                    )
                    
                    test_passed = test_response.get("success", False)
                    test_results[agent_id] = test_passed
                    
                    if test_passed:
                        self.logger.info(f"✅ Integration test passed for {agent_id}")
                    else:
                        self.logger.error(f"❌ Integration test failed for {agent_id}")
                        all_tests_passed = False
                        
                except Exception as e:
                    self.logger.error(f"❌ Integration test error for {agent_id}: {e}")
                    test_results[agent_id] = False
                    all_tests_passed = False
            
            return {
                "success": all_tests_passed,
                "test_results": test_results,
                "total_tests": len(legacy_agents),
                "passed_tests": sum(test_results.values())
            }
            
        except Exception as e:
            self.logger.error(f"Error validating complete integration: {e}")
            return {"success": False, "error": str(e)}


# Global instance
_complete_migration_manager: Optional[CompleteMigrationIntegrationManager] = None


async def get_complete_migration_manager() -> CompleteMigrationIntegrationManager:
    """Get the global complete migration integration manager instance."""
    global _complete_migration_manager
    
    if _complete_migration_manager is None:
        _complete_migration_manager = CompleteMigrationIntegrationManager()
    
    return _complete_migration_manager


async def execute_complete_legacy_migration() -> Dict[str, Any]:
    """
    Convenience function to execute complete legacy agent migration.
    
    Returns:
        Migration execution results
    """
    try:
        manager = await get_complete_migration_manager()
        return await manager.execute_complete_migration()
    except Exception as e:
        logger.error(f"Error executing complete legacy migration: {e}")
        return {"success": False, "error": str(e)}
