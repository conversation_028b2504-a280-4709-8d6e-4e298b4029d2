"""
MCP Tool Provider Component for Datagenius Agents.

This component automatically provides MCP server tools to agents,
integrating them seamlessly into the agent workflow system.
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from ..base_component import BaseComponent, AgentContext
from ...app.services.agent_mcp_integration import agent_mcp_integration_service

logger = logging.getLogger(__name__)


class MCPToolProvider(BaseComponent):
    """
    Component that provides MCP server tools to agents.
    
    This component automatically discovers and provides MCP tools
    based on the user's business profile and active MCP servers.
    """
    
    def __init__(self, name: str = "mcp_tool_provider"):
        """
        Initialize the MCP tool provider component.
        
        Args:
            name: Component name
        """
        super().__init__(name)
        self.auto_initialize = True
        self.cache_tools = True
        self.tool_categories = ["all"]  # Which categories to include
        self.max_tools = 50  # Maximum number of tools to provide
        
    async def _initialize_component(self) -> None:
        """Initialize the MCP tool provider component."""
        # Get configuration
        config = getattr(self, 'config', {})
        
        self.auto_initialize = config.get("auto_initialize", True)
        self.cache_tools = config.get("cache_tools", True)
        self.tool_categories = config.get("tool_categories", ["all"])
        self.max_tools = config.get("max_tools", 50)
        
        self.logger.info(f"MCP Tool Provider initialized with categories: {self.tool_categories}")
    
    async def process(self, context: AgentContext) -> AgentContext:
        """
        Process the agent context and provide MCP tools.
        
        Args:
            context: Agent processing context
            
        Returns:
            Updated context with MCP tools available
        """
        try:
            # Check if MCP tools are already provided
            if context.get_field("mcp_tools_provided"):
                return context
            
            # Get user and business profile information
            user_id = context.user_id
            business_profile_id = context.get_field("business_profile_id")
            agent_id = context.get_field("agent_id", "unknown")
            
            self.logger.debug(f"Providing MCP tools for agent {agent_id}, user {user_id}")
            
            # Initialize MCP tools for the agent if auto-initialize is enabled
            if self.auto_initialize:
                await agent_mcp_integration_service.initialize_mcp_tools_for_agent(
                    agent_id=agent_id,
                    user_id=int(user_id),
                    business_profile_id=business_profile_id
                )
            
            # Get available MCP tools
            available_tools = await agent_mcp_integration_service.get_mcp_tools_for_context(context)
            
            # Filter tools by categories if specified
            if "all" not in self.tool_categories:
                filtered_tools = []
                for tool in available_tools:
                    tool_category = self._get_tool_category(tool)
                    if tool_category in self.tool_categories:
                        filtered_tools.append(tool)
                available_tools = filtered_tools
            
            # Limit number of tools
            if len(available_tools) > self.max_tools:
                available_tools = available_tools[:self.max_tools]
                self.logger.warning(f"Limited MCP tools to {self.max_tools} for performance")
            
            # Get MCP resources
            available_resources = await agent_mcp_integration_service.get_mcp_resources_for_context(context)
            
            # Get MCP prompts
            available_prompts = await agent_mcp_integration_service.get_mcp_prompts_for_context(context)
            
            # Add MCP capabilities to context
            context.set_field("mcp_tools", available_tools)
            context.set_field("mcp_resources", available_resources)
            context.set_field("mcp_prompts", available_prompts)
            context.set_field("mcp_tools_provided", True)
            context.set_field("mcp_tool_count", len(available_tools))
            context.set_field("mcp_resource_count", len(available_resources))
            context.set_field("mcp_prompt_count", len(available_prompts))
            
            # Add MCP tool execution helper
            context.set_field("execute_mcp_tool", self._create_tool_executor(context))
            
            self.logger.info(
                f"Provided {len(available_tools)} MCP tools, "
                f"{len(available_resources)} resources, "
                f"{len(available_prompts)} prompts to agent {agent_id}"
            )
            
            return context
            
        except Exception as e:
            self.logger.error(f"Error providing MCP tools: {e}")
            context.add_error("mcp_tool_provider", str(e))
            return context
    
    def _get_tool_category(self, tool: Dict[str, Any]) -> str:
        """
        Get the category of an MCP tool.
        
        Args:
            tool: Tool information dictionary
            
        Returns:
            Tool category string
        """
        # This could be enhanced to use more sophisticated categorization
        name = tool.get("name", "").lower()
        description = tool.get("description", "").lower()
        
        if any(keyword in name or keyword in description for keyword in 
               ["data", "query", "analysis", "chart", "visualization"]):
            return "data_analysis"
        elif any(keyword in name or keyword in description for keyword in 
                 ["file", "filesystem", "read", "write"]):
            return "file_management"
        elif any(keyword in name or keyword in description for keyword in 
                 ["web", "http", "api", "search"]):
            return "web_integration"
        elif any(keyword in name or keyword in description for keyword in 
                 ["git", "github", "code", "repository"]):
            return "development"
        else:
            return "general"
    
    def _create_tool_executor(self, context: AgentContext):
        """
        Create a tool executor function for the agent context.
        
        Args:
            context: Agent processing context
            
        Returns:
            Tool executor function
        """
        async def execute_tool(tool_name: str, arguments: Dict[str, Any] = None) -> Dict[str, Any]:
            """
            Execute an MCP tool with the given arguments.
            
            Args:
                tool_name: Name of the tool to execute
                arguments: Tool arguments
                
            Returns:
                Tool execution result
            """
            if arguments is None:
                arguments = {}
            
            try:
                result = await agent_mcp_integration_service.execute_mcp_tool_from_context(
                    context=context,
                    tool_name=tool_name,
                    arguments=arguments
                )
                
                # Log tool usage
                self.logger.info(f"Executed MCP tool {tool_name} with result: {result.get('success', False)}")
                
                return result
                
            except Exception as e:
                self.logger.error(f"Error executing MCP tool {tool_name}: {e}")
                return {
                    "success": False,
                    "error": str(e),
                    "tool_name": tool_name
                }
        
        return execute_tool


class MCPToolExecutor(BaseComponent):
    """
    Component specifically for executing MCP tools within agent workflows.
    
    This component can be used in agent workflows to execute specific
    MCP tools based on agent decisions or user requests.
    """
    
    def __init__(self, name: str = "mcp_tool_executor"):
        """
        Initialize the MCP tool executor component.
        
        Args:
            name: Component name
        """
        super().__init__(name)
        self.default_timeout = 30  # seconds
        self.retry_attempts = 2
        
    async def _initialize_component(self) -> None:
        """Initialize the MCP tool executor component."""
        config = getattr(self, 'config', {})
        
        self.default_timeout = config.get("default_timeout", 30)
        self.retry_attempts = config.get("retry_attempts", 2)
        
        self.logger.info(f"MCP Tool Executor initialized with timeout: {self.default_timeout}s")
    
    async def process(self, context: AgentContext) -> AgentContext:
        """
        Process the agent context and execute requested MCP tools.
        
        Args:
            context: Agent processing context
            
        Returns:
            Updated context with tool execution results
        """
        try:
            # Get tool execution request from context
            tool_request = context.get_field("mcp_tool_request")
            if not tool_request:
                return context
            
            tool_name = tool_request.get("tool_name")
            tool_arguments = tool_request.get("arguments", {})
            
            if not tool_name:
                context.add_error("mcp_tool_executor", "No tool name specified in request")
                return context
            
            self.logger.info(f"Executing MCP tool: {tool_name}")
            
            # Execute the tool with retry logic
            result = None
            last_error = None
            
            for attempt in range(self.retry_attempts + 1):
                try:
                    result = await asyncio.wait_for(
                        agent_mcp_integration_service.execute_mcp_tool_from_context(
                            context=context,
                            tool_name=tool_name,
                            arguments=tool_arguments
                        ),
                        timeout=self.default_timeout
                    )
                    
                    if result.get("success"):
                        break
                    else:
                        last_error = result.get("error", "Unknown error")
                        if attempt < self.retry_attempts:
                            self.logger.warning(f"Tool execution failed, retrying (attempt {attempt + 1})")
                            await asyncio.sleep(1)  # Brief delay before retry
                        
                except asyncio.TimeoutError:
                    last_error = f"Tool execution timed out after {self.default_timeout} seconds"
                    if attempt < self.retry_attempts:
                        self.logger.warning(f"Tool execution timed out, retrying (attempt {attempt + 1})")
                        await asyncio.sleep(1)
                except Exception as e:
                    last_error = str(e)
                    if attempt < self.retry_attempts:
                        self.logger.warning(f"Tool execution error, retrying (attempt {attempt + 1}): {e}")
                        await asyncio.sleep(1)
            
            # Store result in context
            if result and result.get("success"):
                context.set_field("mcp_tool_result", result)
                context.set_field("mcp_tool_execution_status", "success")
                self.logger.info(f"Successfully executed MCP tool: {tool_name}")
            else:
                context.set_field("mcp_tool_result", {"success": False, "error": last_error})
                context.set_field("mcp_tool_execution_status", "failed")
                context.add_error("mcp_tool_executor", f"Tool execution failed: {last_error}")
                self.logger.error(f"Failed to execute MCP tool {tool_name}: {last_error}")
            
            # Clear the request
            context.set_field("mcp_tool_request", None)
            
            return context
            
        except Exception as e:
            self.logger.error(f"Error in MCP tool executor: {e}")
            context.add_error("mcp_tool_executor", str(e))
            context.set_field("mcp_tool_execution_status", "error")
            return context


class MCPResourceProvider(BaseComponent):
    """
    Component that provides access to MCP server resources.
    
    This component makes MCP resources available to agents for
    context enhancement and data access.
    """
    
    def __init__(self, name: str = "mcp_resource_provider"):
        """
        Initialize the MCP resource provider component.
        
        Args:
            name: Component name
        """
        super().__init__(name)
        self.auto_load_resources = False
        self.resource_types = ["all"]
        
    async def _initialize_component(self) -> None:
        """Initialize the MCP resource provider component."""
        config = getattr(self, 'config', {})
        
        self.auto_load_resources = config.get("auto_load_resources", False)
        self.resource_types = config.get("resource_types", ["all"])
        
        self.logger.info(f"MCP Resource Provider initialized")
    
    async def process(self, context: AgentContext) -> AgentContext:
        """
        Process the agent context and provide MCP resources.
        
        Args:
            context: Agent processing context
            
        Returns:
            Updated context with MCP resources available
        """
        try:
            # Get available resources
            available_resources = await agent_mcp_integration_service.get_mcp_resources_for_context(context)
            
            # Filter by resource types if specified
            if "all" not in self.resource_types:
                filtered_resources = []
                for resource in available_resources:
                    if resource.get("type") in self.resource_types:
                        filtered_resources.append(resource)
                available_resources = filtered_resources
            
            # Add resources to context
            context.set_field("mcp_resources", available_resources)
            context.set_field("mcp_resource_count", len(available_resources))
            
            # Add resource reader helper
            context.set_field("read_mcp_resource", self._create_resource_reader(context))
            
            self.logger.info(f"Provided {len(available_resources)} MCP resources")
            
            return context
            
        except Exception as e:
            self.logger.error(f"Error providing MCP resources: {e}")
            context.add_error("mcp_resource_provider", str(e))
            return context
    
    def _create_resource_reader(self, context: AgentContext):
        """
        Create a resource reader function for the agent context.
        
        Args:
            context: Agent processing context
            
        Returns:
            Resource reader function
        """
        async def read_resource(resource_name: str) -> Dict[str, Any]:
            """
            Read content from an MCP resource.
            
            Args:
                resource_name: Name of the resource to read
                
            Returns:
                Resource content or error
            """
            try:
                # Find the resource ID
                resources = context.get_field("mcp_resources", [])
                resource_id = None
                
                for resource in resources:
                    if resource["name"] == resource_name:
                        resource_id = resource["id"]
                        break
                
                if not resource_id:
                    return {
                        "success": False,
                        "error": f"Resource '{resource_name}' not found"
                    }
                
                # Read the resource
                user_id = int(context.user_id)
                db = context.get_field("db_session")
                
                result = await agent_mcp_integration_service.mcp_agent_integration.read_mcp_resource(
                    db=db,
                    resource_id=resource_id,
                    user_id=user_id
                )
                
                return result
                
            except Exception as e:
                self.logger.error(f"Error reading MCP resource {resource_name}: {e}")
                return {
                    "success": False,
                    "error": str(e)
                }
        
        return read_resource
