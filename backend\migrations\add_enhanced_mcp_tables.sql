-- Enhanced MCP Server Integration Migration
-- This migration adds tables for enhanced MCP server support with JSON configurations,
-- multiple transport types, tool discovery, and secure credential management.

-- Create enhanced MCP server configurations table
CREATE TABLE IF NOT EXISTS mcp_servers (
    id UUID PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    business_profile_id UUID REFERENCES business_profiles(id) ON DELETE SET NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    config_type VARCHAR(20) DEFAULT 'json' CHECK (config_type IN ('json', 'form', 'auto')),
    transport_type VARCHAR(20) DEFAULT 'http' CHECK (transport_type IN ('http', 'stdio', 'sse', 'ws')),
    configuration JSONB NOT NULL,
    status VARCHAR(20) DEFAULT 'inactive' CHECK (status IN ('active', 'inactive', 'error', 'connecting')),
    last_connected_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for mcp_servers
CREATE INDEX IF NOT EXISTS idx_mcp_servers_user_id ON mcp_servers(user_id);
CREATE INDEX IF NOT EXISTS idx_mcp_servers_business_profile_id ON mcp_servers(business_profile_id);
CREATE INDEX IF NOT EXISTS idx_mcp_servers_status ON mcp_servers(status);
CREATE INDEX IF NOT EXISTS idx_mcp_servers_created_at ON mcp_servers(created_at);

-- Create MCP server tools registry table
CREATE TABLE IF NOT EXISTS mcp_tools (
    id UUID PRIMARY KEY,
    server_id UUID NOT NULL REFERENCES mcp_servers(id) ON DELETE CASCADE,
    tool_name VARCHAR(100) NOT NULL,
    tool_description TEXT,
    parameters JSONB,
    capabilities JSONB,
    is_enabled BOOLEAN DEFAULT true,
    usage_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(server_id, tool_name)
);

-- Create indexes for mcp_tools
CREATE INDEX IF NOT EXISTS idx_mcp_tools_server_id ON mcp_tools(server_id);
CREATE INDEX IF NOT EXISTS idx_mcp_tools_is_enabled ON mcp_tools(is_enabled);
CREATE INDEX IF NOT EXISTS idx_mcp_tools_usage_count ON mcp_tools(usage_count DESC);

-- Create MCP server resources table
CREATE TABLE IF NOT EXISTS mcp_resources (
    id UUID PRIMARY KEY,
    server_id UUID NOT NULL REFERENCES mcp_servers(id) ON DELETE CASCADE,
    resource_type VARCHAR(100) NOT NULL,
    resource_name VARCHAR(100) NOT NULL,
    resource_description TEXT,
    parameters JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(server_id, resource_name)
);

-- Create indexes for mcp_resources
CREATE INDEX IF NOT EXISTS idx_mcp_resources_server_id ON mcp_resources(server_id);
CREATE INDEX IF NOT EXISTS idx_mcp_resources_type ON mcp_resources(resource_type);

-- Create MCP server prompts table
CREATE TABLE IF NOT EXISTS mcp_prompts (
    id UUID PRIMARY KEY,
    server_id UUID NOT NULL REFERENCES mcp_servers(id) ON DELETE CASCADE,
    prompt_name VARCHAR(100) NOT NULL,
    prompt_description TEXT,
    template TEXT NOT NULL,
    parameters JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(server_id, prompt_name)
);

-- Create indexes for mcp_prompts
CREATE INDEX IF NOT EXISTS idx_mcp_prompts_server_id ON mcp_prompts(server_id);

-- Create MCP input variables table for secure credential management
CREATE TABLE IF NOT EXISTS mcp_input_variables (
    id UUID PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    variable_id VARCHAR(100) NOT NULL,
    variable_type VARCHAR(20) NOT NULL CHECK (variable_type IN ('promptString', 'promptChoice', 'env')),
    description TEXT,
    is_password BOOLEAN DEFAULT false,
    encrypted_value TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, variable_id)
);

-- Create indexes for mcp_input_variables
CREATE INDEX IF NOT EXISTS idx_mcp_input_variables_user_id ON mcp_input_variables(user_id);
CREATE INDEX IF NOT EXISTS idx_mcp_input_variables_type ON mcp_input_variables(variable_type);

-- Create trigger function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_mcp_servers_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for mcp_servers updated_at
DROP TRIGGER IF EXISTS trigger_update_mcp_servers_updated_at ON mcp_servers;
CREATE TRIGGER trigger_update_mcp_servers_updated_at
    BEFORE UPDATE ON mcp_servers
    FOR EACH ROW
    EXECUTE FUNCTION update_mcp_servers_updated_at();

-- Insert sample MCP server configurations for demonstration
INSERT INTO mcp_servers (id, user_id, name, description, config_type, transport_type, configuration, status)
SELECT 
    gen_random_uuid(),
    u.id,
    'GitHub MCP Server',
    'Access GitHub repositories and issues through MCP',
    'json',
    'http',
    '{"url": "https://api.githubcopilot.com/mcp/", "headers": {}}'::jsonb,
    'inactive'
FROM users u
WHERE u.email = '<EMAIL>'
ON CONFLICT DO NOTHING;

INSERT INTO mcp_servers (id, user_id, name, description, config_type, transport_type, configuration, status)
SELECT 
    gen_random_uuid(),
    u.id,
    'Filesystem MCP Server',
    'Local filesystem access through MCP stdio',
    'json',
    'stdio',
    '{"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/workspace"], "env": {}}'::jsonb,
    'inactive'
FROM users u
WHERE u.email = '<EMAIL>'
ON CONFLICT DO NOTHING;

-- Add sample tools for the GitHub MCP server
INSERT INTO mcp_tools (id, server_id, tool_name, tool_description, parameters, capabilities, is_enabled)
SELECT 
    gen_random_uuid(),
    s.id,
    'list_repositories',
    'List GitHub repositories for the authenticated user',
    '{"type": "object", "properties": {"org": {"type": "string", "description": "Organization name (optional)"}}}'::jsonb,
    '{"read": true, "write": false}'::jsonb,
    true
FROM mcp_servers s
WHERE s.name = 'GitHub MCP Server'
ON CONFLICT DO NOTHING;

INSERT INTO mcp_tools (id, server_id, tool_name, tool_description, parameters, capabilities, is_enabled)
SELECT 
    gen_random_uuid(),
    s.id,
    'create_issue',
    'Create a new GitHub issue',
    '{"type": "object", "properties": {"repo": {"type": "string"}, "title": {"type": "string"}, "body": {"type": "string"}}, "required": ["repo", "title"]}'::jsonb,
    '{"read": false, "write": true}'::jsonb,
    true
FROM mcp_servers s
WHERE s.name = 'GitHub MCP Server'
ON CONFLICT DO NOTHING;

-- Add sample tools for the Filesystem MCP server
INSERT INTO mcp_tools (id, server_id, tool_name, tool_description, parameters, capabilities, is_enabled)
SELECT 
    gen_random_uuid(),
    s.id,
    'read_file',
    'Read contents of a file',
    '{"type": "object", "properties": {"path": {"type": "string", "description": "File path to read"}}, "required": ["path"]}'::jsonb,
    '{"read": true, "write": false}'::jsonb,
    true
FROM mcp_servers s
WHERE s.name = 'Filesystem MCP Server'
ON CONFLICT DO NOTHING;

INSERT INTO mcp_tools (id, server_id, tool_name, tool_description, parameters, capabilities, is_enabled)
SELECT 
    gen_random_uuid(),
    s.id,
    'write_file',
    'Write content to a file',
    '{"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"]}'::jsonb,
    '{"read": false, "write": true}'::jsonb,
    true
FROM mcp_servers s
WHERE s.name = 'Filesystem MCP Server'
ON CONFLICT DO NOTHING;

-- Add sample resources
INSERT INTO mcp_resources (id, server_id, resource_type, resource_name, resource_description, parameters)
SELECT 
    gen_random_uuid(),
    s.id,
    'file',
    'workspace_files',
    'Access to workspace files and directories',
    '{"base_path": "/workspace", "allowed_extensions": [".txt", ".md", ".json", ".py"]}'::jsonb
FROM mcp_servers s
WHERE s.name = 'Filesystem MCP Server'
ON CONFLICT DO NOTHING;

-- Add sample prompts
INSERT INTO mcp_prompts (id, server_id, prompt_name, prompt_description, template, parameters)
SELECT 
    gen_random_uuid(),
    s.id,
    'code_review',
    'Generate a code review for a GitHub pull request',
    'Please review the following pull request:\n\nRepository: {{repo}}\nPR Number: {{pr_number}}\nTitle: {{title}}\n\nProvide feedback on code quality, potential issues, and suggestions for improvement.',
    '{"repo": {"type": "string", "description": "Repository name"}, "pr_number": {"type": "number", "description": "Pull request number"}, "title": {"type": "string", "description": "PR title"}}'::jsonb
FROM mcp_servers s
WHERE s.name = 'GitHub MCP Server'
ON CONFLICT DO NOTHING;

-- Create view for MCP server summary
CREATE OR REPLACE VIEW mcp_server_summary AS
SELECT 
    s.id,
    s.user_id,
    s.business_profile_id,
    s.name,
    s.description,
    s.config_type,
    s.transport_type,
    s.status,
    s.last_connected_at,
    s.created_at,
    s.updated_at,
    COUNT(DISTINCT t.id) as tool_count,
    COUNT(DISTINCT r.id) as resource_count,
    COUNT(DISTINCT p.id) as prompt_count,
    COUNT(DISTINCT CASE WHEN t.is_enabled = true THEN t.id END) as enabled_tool_count
FROM mcp_servers s
LEFT JOIN mcp_tools t ON s.id = t.server_id
LEFT JOIN mcp_resources r ON s.id = r.server_id
LEFT JOIN mcp_prompts p ON s.id = p.server_id
GROUP BY s.id, s.user_id, s.business_profile_id, s.name, s.description, 
         s.config_type, s.transport_type, s.status, s.last_connected_at, 
         s.created_at, s.updated_at;

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON mcp_servers TO datagenius_app;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON mcp_tools TO datagenius_app;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON mcp_resources TO datagenius_app;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON mcp_prompts TO datagenius_app;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON mcp_input_variables TO datagenius_app;
-- GRANT SELECT ON mcp_server_summary TO datagenius_app;

-- Add comments for documentation
COMMENT ON TABLE mcp_servers IS 'Enhanced MCP server configurations supporting multiple transport types and JSON configurations';
COMMENT ON TABLE mcp_tools IS 'Registry of tools discovered from MCP servers';
COMMENT ON TABLE mcp_resources IS 'Registry of resources available from MCP servers';
COMMENT ON TABLE mcp_prompts IS 'Registry of prompts available from MCP servers';
COMMENT ON TABLE mcp_input_variables IS 'Secure storage for MCP server input variables and credentials';

COMMENT ON COLUMN mcp_servers.config_type IS 'Configuration method: json (VS Code compatible), form (simple form), auto (auto-detected)';
COMMENT ON COLUMN mcp_servers.transport_type IS 'Transport protocol: http, stdio, sse (server-sent events), ws (websocket)';
COMMENT ON COLUMN mcp_servers.configuration IS 'Server configuration in JSON format, transport-specific';
COMMENT ON COLUMN mcp_servers.status IS 'Current server status: active, inactive, error, connecting';

COMMENT ON COLUMN mcp_input_variables.encrypted_value IS 'Encrypted credential value using Fernet encryption';
COMMENT ON COLUMN mcp_input_variables.variable_type IS 'Type of input: promptString, promptChoice, env';

-- Migration complete
SELECT 'Enhanced MCP server tables created successfully' as result;
