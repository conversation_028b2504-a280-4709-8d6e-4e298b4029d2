"""
Enhanced MCP server models for the Datagenius API.

This module provides Pydantic models for MCP server configuration,
management, and integration with the enhanced MCP system.
"""

from datetime import datetime
from typing import Dict, Any, List, Optional, Union, Literal
from pydantic import BaseModel, Field, model_validator
from enum import Enum


class MCPTransportType(str, Enum):
    """Enum for MCP transport types."""
    HTTP = "http"
    STDIO = "stdio"
    SSE = "sse"
    WEBSOCKET = "ws"


class MCPConfigType(str, Enum):
    """Enum for MCP configuration types."""
    JSON = "json"
    FORM = "form"
    AUTO = "auto"


class MCPServerStatus(str, Enum):
    """Enum for MCP server status."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    CONNECTING = "connecting"


class MCPInputVariableType(str, Enum):
    """Enum for MCP input variable types."""
    PROMPT_STRING = "promptString"
    PROMPT_CHOICE = "promptChoice"
    ENV = "env"


# Input Variable Models
class MCPInputVariableBase(BaseModel):
    """Base model for MCP input variables."""
    variable_id: str = Field(..., description="Unique identifier for the variable")
    variable_type: MCPInputVariableType = Field(..., description="Type of input variable")
    description: Optional[str] = Field(None, description="Description of the variable")
    is_password: bool = Field(False, description="Whether this is a password field")


class MCPInputVariableCreate(MCPInputVariableBase):
    """Model for creating MCP input variables."""
    value: str = Field(..., description="Value of the variable (will be encrypted)")


class MCPInputVariableResponse(MCPInputVariableBase):
    """Model for MCP input variable responses."""
    id: str
    user_id: int
    created_at: datetime

    class Config:
        from_attributes = True


# Server Configuration Models
class MCPServerConfigBase(BaseModel):
    """Base configuration for MCP servers."""
    name: str = Field(..., description="Name of the MCP server")
    description: Optional[str] = Field(None, description="Description of the server")
    transport_type: MCPTransportType = Field(MCPTransportType.HTTP, description="Transport type")


class MCPServerHTTPConfig(MCPServerConfigBase):
    """Configuration for HTTP-based MCP servers."""
    transport_type: Literal[MCPTransportType.HTTP] = MCPTransportType.HTTP
    url: str = Field(..., description="Server URL")
    headers: Optional[Dict[str, str]] = Field(None, description="HTTP headers")


class MCPServerStdioConfig(MCPServerConfigBase):
    """Configuration for stdio-based MCP servers."""
    transport_type: Literal[MCPTransportType.STDIO] = MCPTransportType.STDIO
    command: str = Field(..., description="Command to execute")
    args: List[str] = Field(default_factory=list, description="Command arguments")
    env: Optional[Dict[str, str]] = Field(None, description="Environment variables")
    env_file: Optional[str] = Field(None, description="Path to environment file")


class MCPServerSSEConfig(MCPServerConfigBase):
    """Configuration for SSE-based MCP servers."""
    transport_type: Literal[MCPTransportType.SSE] = MCPTransportType.SSE
    url: str = Field(..., description="Server URL")
    headers: Optional[Dict[str, str]] = Field(None, description="HTTP headers")


class MCPServerWebSocketConfig(MCPServerConfigBase):
    """Configuration for WebSocket-based MCP servers."""
    transport_type: Literal[MCPTransportType.WEBSOCKET] = MCPTransportType.WEBSOCKET
    url: str = Field(..., description="WebSocket URL")
    headers: Optional[Dict[str, str]] = Field(None, description="Connection headers")


# Union type for all server configurations
MCPServerConfig = Union[
    MCPServerHTTPConfig,
    MCPServerStdioConfig,
    MCPServerSSEConfig,
    MCPServerWebSocketConfig
]


# JSON Configuration Models
class MCPInputDefinition(BaseModel):
    """Definition for input variables in JSON configuration."""
    type: MCPInputVariableType
    id: str
    description: str
    password: bool = False
    choices: Optional[List[str]] = None  # For promptChoice type


class MCPServerDefinition(BaseModel):
    """Definition for a server in JSON configuration."""
    # HTTP/SSE/WebSocket servers
    url: Optional[str] = None
    headers: Optional[Dict[str, str]] = None
    
    # stdio servers
    type: Optional[str] = None  # "stdio"
    command: Optional[str] = None
    args: Optional[List[str]] = None
    env: Optional[Dict[str, str]] = None
    env_file: Optional[str] = None


class MCPJSONConfiguration(BaseModel):
    """Complete JSON configuration format (VS Code compatible)."""
    inputs: Optional[List[MCPInputDefinition]] = Field(default_factory=list)
    servers: Dict[str, MCPServerDefinition] = Field(..., description="Server definitions")


# Server Management Models
class MCPServerCreate(BaseModel):
    """Model for creating MCP servers."""
    name: str = Field(..., description="Server name")
    description: Optional[str] = Field(None, description="Server description")
    business_profile_id: Optional[str] = Field(None, description="Associated business profile")
    config_type: MCPConfigType = Field(MCPConfigType.JSON, description="Configuration type")
    
    # For JSON configuration
    json_config: Optional[MCPJSONConfiguration] = Field(None, description="JSON configuration")
    
    # For form-based configuration
    server_config: Optional[MCPServerConfig] = Field(None, description="Server configuration")

    @model_validator(mode='before')
    @classmethod
    def validate_config(cls, values):
        """Ensure at least one configuration method is provided."""
        if isinstance(values, dict):
            config_type = values.get('config_type')
            if config_type == MCPConfigType.JSON and not values.get('json_config'):
                raise ValueError("json_config is required when config_type is 'json'")
            if config_type == MCPConfigType.FORM and not values.get('server_config'):
                raise ValueError("server_config is required when config_type is 'form'")
        return values


class MCPServerUpdate(BaseModel):
    """Model for updating MCP servers."""
    name: Optional[str] = None
    description: Optional[str] = None
    business_profile_id: Optional[str] = None
    json_config: Optional[MCPJSONConfiguration] = None
    server_config: Optional[MCPServerConfig] = None


# Tool Models
class MCPToolResponse(BaseModel):
    """Model for MCP tool responses."""
    id: str
    server_id: str
    tool_name: str
    tool_description: Optional[str]
    parameters: Optional[Dict[str, Any]]
    capabilities: Optional[Dict[str, Any]]
    is_enabled: bool
    usage_count: int
    last_used_at: Optional[datetime]
    created_at: datetime

    class Config:
        from_attributes = True


# Resource Models
class MCPResourceResponse(BaseModel):
    """Model for MCP resource responses."""
    id: str
    server_id: str
    resource_type: str
    resource_name: str
    resource_description: Optional[str]
    parameters: Optional[Dict[str, Any]]
    created_at: datetime

    class Config:
        from_attributes = True


# Prompt Models
class MCPPromptResponse(BaseModel):
    """Model for MCP prompt responses."""
    id: str
    server_id: str
    prompt_name: str
    prompt_description: Optional[str]
    template: str
    parameters: Optional[Dict[str, Any]]
    created_at: datetime

    class Config:
        from_attributes = True


# Server Response Models
class MCPServerResponse(BaseModel):
    """Model for MCP server responses."""
    id: str
    user_id: int
    business_profile_id: Optional[str]
    name: str
    description: Optional[str]
    config_type: MCPConfigType
    transport_type: MCPTransportType
    configuration: Dict[str, Any]
    status: MCPServerStatus
    last_connected_at: Optional[datetime]
    error_message: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    # Related data
    tools: List[MCPToolResponse] = Field(default_factory=list)
    resources: List[MCPResourceResponse] = Field(default_factory=list)
    prompts: List[MCPPromptResponse] = Field(default_factory=list)

    class Config:
        from_attributes = True


class MCPServerListResponse(BaseModel):
    """Model for MCP server list responses."""
    servers: List[MCPServerResponse]
    total: int
    page: int
    per_page: int


# Server Management Actions
class MCPServerAction(BaseModel):
    """Model for server management actions."""
    action: Literal["start", "stop", "restart", "test", "discover"] = Field(..., description="Action to perform")


class MCPServerActionResponse(BaseModel):
    """Model for server action responses."""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None


# Discovery Models
class MCPDiscoveryResponse(BaseModel):
    """Model for MCP server discovery responses."""
    tools: List[MCPToolResponse] = Field(default_factory=list)
    resources: List[MCPResourceResponse] = Field(default_factory=list)
    prompts: List[MCPPromptResponse] = Field(default_factory=list)
    server_info: Optional[Dict[str, Any]] = None
