"""
Comprehensive integration tests for the Enhanced MCP Server Integration system.

This test suite validates the complete MCP integration workflow including
API endpoints, validation, error handling, and agent integration.
"""

import asyncio
import json
import pytest
import uuid
from unittest.mock import Mock, AsyncMock, patch
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.models.mcp_server import (
    MCPServerCreate, MCPJSONConfiguration, MCPServerDefinition,
    MCPInputDefinition, MCPServerConfig, MCPTransportType
)
from app.database import MCPServer, MCPTool, MCPResource, MCPPrompt, MCPInputVariable
from app.services.mcp_server_manager import MCPServerManager
from app.utils.validation import mcp_validator
from app.utils.error_handlers import error_handler, MCPValidationError


class TestMCPAPIIntegration:
    """Integration tests for MCP API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Test client for API requests."""
        return TestClient(app)
    
    @pytest.fixture
    def mock_user(self):
        """Mock authenticated user."""
        return Mock(id=1, email="<EMAIL>")
    
    @pytest.fixture
    def sample_json_config(self):
        """Sample JSON configuration for testing."""
        return {
            "inputs": [
                {
                    "type": "promptString",
                    "id": "api-key",
                    "description": "API Key for the service",
                    "password": True
                }
            ],
            "servers": {
                "github": {
                    "url": "https://api.github.com/mcp/",
                    "headers": {
                        "Authorization": "Bearer ${input:api-key}"
                    }
                },
                "filesystem": {
                    "type": "stdio",
                    "command": "npx",
                    "args": ["-y", "@modelcontextprotocol/server-filesystem", "/workspace"]
                }
            }
        }
    
    @pytest.fixture
    def sample_server_create(self, sample_json_config):
        """Sample server creation request."""
        return {
            "name": "Test MCP Server",
            "description": "Test server for integration testing",
            "config_type": "json",
            "json_config": sample_json_config
        }
    
    def test_create_server_success(self, client, sample_server_create, mock_user):
        """Test successful server creation."""
        with patch('app.auth.get_current_active_user', return_value=mock_user):
            with patch('app.services.mcp_server_manager.MCPServerManager.create_server_from_json') as mock_create:
                mock_server = Mock()
                mock_server.id = "test-server-id"
                mock_server.name = "Test MCP Server"
                mock_server.status = "inactive"
                mock_create.return_value = mock_server
                
                response = client.post("/mcp-servers", json=sample_server_create)
                
                assert response.status_code == 200
                data = response.json()
                assert data["name"] == "Test MCP Server"
                assert data["status"] == "inactive"
    
    def test_create_server_validation_error(self, client, mock_user):
        """Test server creation with validation errors."""
        invalid_data = {
            "name": "",  # Empty name should fail validation
            "config_type": "json",
            "json_config": {}
        }
        
        with patch('app.auth.get_current_active_user', return_value=mock_user):
            response = client.post("/mcp-servers", json=invalid_data)
            
            assert response.status_code == 400
            data = response.json()
            assert data["success"] is False
            assert data["error_code"] == "VALIDATION_ERROR"
    
    def test_list_servers_with_filters(self, client, mock_user):
        """Test listing servers with filters."""
        with patch('app.auth.get_current_active_user', return_value=mock_user):
            with patch('app.database.get_db') as mock_db:
                mock_session = Mock()
                mock_db.return_value = mock_session
                
                # Mock query chain
                mock_query = Mock()
                mock_session.query.return_value = mock_query
                mock_query.filter.return_value = mock_query
                mock_query.count.return_value = 2
                mock_query.offset.return_value = mock_query
                mock_query.limit.return_value = mock_query
                mock_query.all.return_value = []
                
                response = client.get("/mcp-servers?status=active&limit=10")
                
                assert response.status_code == 200
                data = response.json()
                assert "servers" in data
                assert "total" in data
    
    def test_server_actions(self, client, mock_user):
        """Test server action endpoints."""
        server_id = "test-server-id"
        
        with patch('app.auth.get_current_active_user', return_value=mock_user):
            with patch('app.database.get_db') as mock_db:
                mock_session = Mock()
                mock_db.return_value = mock_session
                
                # Mock server lookup
                mock_server = Mock()
                mock_server.id = server_id
                mock_server.status = "inactive"
                mock_session.query.return_value.filter.return_value.first.return_value = mock_server
                
                with patch('app.services.mcp_server_manager.MCPServerManager.start_server', return_value=True):
                    response = client.post(f"/mcp-servers/{server_id}/actions", json={"action": "start"})
                    
                    assert response.status_code == 200
                    data = response.json()
                    assert data["success"] is True
                    assert "started successfully" in data["message"]
    
    def test_input_variables_crud(self, client, mock_user):
        """Test input variables CRUD operations."""
        variable_data = {
            "variable_id": "test-var",
            "variable_type": "promptString",
            "description": "Test variable",
            "is_password": True,
            "value": "secret-value"
        }
        
        with patch('app.auth.get_current_active_user', return_value=mock_user):
            with patch('app.database.get_db') as mock_db:
                mock_session = Mock()
                mock_db.return_value = mock_session
                
                # Mock variable creation
                mock_session.query.return_value.filter.return_value.first.return_value = None
                mock_session.add = Mock()
                mock_session.commit = Mock()
                mock_session.refresh = Mock()
                
                response = client.post("/mcp-servers/input-variables", json=variable_data)
                
                assert response.status_code == 200
                data = response.json()
                assert data["variable_id"] == "test-var"


class TestMCPValidationIntegration:
    """Integration tests for MCP validation system."""
    
    @pytest.mark.asyncio
    async def test_comprehensive_server_validation(self):
        """Test comprehensive server validation."""
        # Valid configuration
        valid_config = MCPJSONConfiguration(
            servers={
                "test": MCPServerDefinition(
                    url="https://api.example.com/mcp"
                )
            }
        )
        
        server_data = MCPServerCreate(
            name="Valid Server",
            description="A valid test server",
            config_type="json",
            json_config=valid_config
        )
        
        is_valid, errors = await mcp_validator.validate_server_creation(server_data, 1)
        assert is_valid is True
        assert len(errors) == 0
    
    @pytest.mark.asyncio
    async def test_security_validation(self):
        """Test security validation for dangerous configurations."""
        # Dangerous configuration
        dangerous_config = MCPJSONConfiguration(
            servers={
                "dangerous": MCPServerDefinition(
                    type="stdio",
                    command="rm",
                    args=["-rf", "/"]
                )
            }
        )
        
        server_data = MCPServerCreate(
            name="Dangerous Server",
            config_type="json",
            json_config=dangerous_config
        )
        
        is_valid, errors = await mcp_validator.validate_server_creation(server_data, 1)
        assert is_valid is False
        assert len(errors) > 0
        assert any("not allowed" in error.lower() for error in errors)
    
    def test_input_sanitization(self):
        """Test input sanitization."""
        dangerous_input = "<script>alert('xss')</script>; DROP TABLE users;"
        sanitized = mcp_validator.sanitize_input(dangerous_input)
        
        assert "<script>" not in sanitized
        assert "DROP TABLE" not in sanitized
        assert len(sanitized) <= 1000
    
    def test_json_validation(self):
        """Test JSON structure validation."""
        # Valid JSON
        valid_json = '{"servers": {"test": {"url": "https://example.com"}}}'
        is_valid, parsed, error = mcp_validator.validate_json_structure(valid_json)
        assert is_valid is True
        assert parsed is not None
        assert error is None
        
        # Invalid JSON
        invalid_json = '{"servers": {"test": {'  # Malformed JSON
        is_valid, parsed, error = mcp_validator.validate_json_structure(invalid_json)
        assert is_valid is False
        assert parsed is None
        assert error is not None


class TestMCPErrorHandling:
    """Integration tests for MCP error handling system."""
    
    def test_error_response_creation(self):
        """Test standardized error response creation."""
        error = MCPValidationError(
            "Test validation error",
            field_errors={"name": ["Name is required"]},
            suggestions=["Check the name field"]
        )
        
        response = error_handler.create_error_response(error, "test-request-id")
        
        assert response.success is False
        assert response.error_code == "VALIDATION_ERROR"
        assert response.message == "Test validation error"
        assert response.field_errors == {"name": ["Name is required"]}
        assert response.request_id == "test-request-id"
        assert "Check the name field" in response.suggestions
    
    def test_http_exception_creation(self):
        """Test HTTP exception creation from errors."""
        error = MCPValidationError("Validation failed")
        
        http_exception = error_handler.create_http_exception(error, 400, "test-request")
        
        assert http_exception.status_code == 400
        assert isinstance(http_exception.detail, dict)
        assert http_exception.detail["success"] is False
        assert http_exception.detail["error_code"] == "VALIDATION_ERROR"
    
    def test_error_logging(self):
        """Test error logging with context."""
        error = ValueError("Test error")
        context = {"operation": "test", "data": "sample"}
        
        with patch('app.utils.error_handlers.logger') as mock_logger:
            error_handler.log_error(error, context, 1, "test-request")
            
            mock_logger.error.assert_called_once()
            call_args = mock_logger.error.call_args
            assert "Test error" in str(call_args[0][0])
            assert call_args[1]["extra"]["user_id"] == 1
            assert call_args[1]["extra"]["request_id"] == "test-request"


class TestMCPAgentIntegration:
    """Integration tests for MCP agent integration."""
    
    @pytest.mark.asyncio
    async def test_tool_discovery_and_registration(self):
        """Test MCP tool discovery and registration with agents."""
        from app.services.mcp_agent_integration import mcp_agent_integration
        
        # Mock database and server
        mock_db = Mock()
        mock_server = Mock()
        mock_server.id = "test-server"
        mock_server.name = "Test Server"
        
        mock_db.query.return_value.filter.return_value.all.return_value = [mock_server]
        
        # Mock tool discovery
        with patch.object(mcp_agent_integration.mcp_manager, 'start_server', return_value=True):
            with patch.object(mcp_agent_integration, '_get_server_capabilities') as mock_capabilities:
                mock_capabilities.return_value = {
                    "tools": [
                        {
                            "id": "test-tool",
                            "name": "test_tool",
                            "description": "Test tool",
                            "parameters": {"type": "object"},
                            "is_enabled": True
                        }
                    ],
                    "resources": [],
                    "prompts": []
                }
                
                capabilities = await mcp_agent_integration.initialize_for_business_profile(
                    mock_db, "test-profile"
                )
                
                assert "tools" in capabilities
                assert len(capabilities["tools"]) == 1
                assert capabilities["tools"][0]["name"] == "test_tool"
    
    @pytest.mark.asyncio
    async def test_tool_execution_workflow(self):
        """Test complete tool execution workflow."""
        from app.services.mcp_agent_integration import mcp_agent_integration
        
        mock_db = Mock()
        mock_server = Mock()
        mock_server.id = "test-server"
        mock_server.name = "Test Server"
        
        mock_tool = Mock()
        mock_tool.usage_count = 0
        
        mock_db.query.return_value.filter.return_value.first.return_value = mock_server
        mock_db.query.return_value.filter.return_value.filter.return_value.first.return_value = mock_tool
        mock_db.commit = Mock()
        
        # Mock MCP client
        mock_client = AsyncMock()
        mock_client.call_tool.return_value = {"result": "Tool executed successfully"}
        
        mcp_agent_integration.mcp_manager.active_clients["test-server"] = mock_client
        
        result = await mcp_agent_integration.execute_mcp_tool(
            db=mock_db,
            tool_id="mcp_test-server_test_tool",
            arguments={"param": "value"},
            user_id=1
        )
        
        assert result["success"] is True
        assert "result" in result
        assert mock_tool.usage_count == 1


class TestMCPPerformanceIntegration:
    """Integration tests for MCP performance and scalability."""
    
    @pytest.mark.asyncio
    async def test_concurrent_server_operations(self):
        """Test concurrent server operations."""
        manager = MCPServerManager()
        
        # Mock multiple concurrent operations
        async def mock_operation(server_id):
            await asyncio.sleep(0.1)  # Simulate work
            return f"completed-{server_id}"
        
        # Test concurrent execution
        tasks = [mock_operation(f"server-{i}") for i in range(10)]
        results = await asyncio.gather(*tasks)
        
        assert len(results) == 10
        assert all("completed-" in result for result in results)
    
    def test_large_configuration_handling(self):
        """Test handling of large configurations."""
        # Create a large configuration
        large_config = {
            "servers": {
                f"server-{i}": {
                    "url": f"https://api{i}.example.com/mcp",
                    "headers": {"Authorization": f"Bearer token-{i}"}
                }
                for i in range(100)
            }
        }
        
        # Test JSON validation with large data
        json_str = json.dumps(large_config)
        is_valid, parsed, error = mcp_validator.validate_json_structure(json_str)
        
        # Should handle large configurations
        assert is_valid is True
        assert parsed is not None
        assert len(parsed["servers"]) == 100
    
    @pytest.mark.asyncio
    async def test_error_recovery(self):
        """Test error recovery mechanisms."""
        from app.services.mcp_server_manager import MCPServerManager
        
        manager = MCPServerManager()
        
        # Test recovery from connection failures
        with patch('app.utils.mcp_client.test_mcp_connection', side_effect=ConnectionError("Connection failed")):
            success, error = await manager.test_server_connection(Mock(), "test-server")
            
            assert success is False
            assert "Connection failed" in error


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])
