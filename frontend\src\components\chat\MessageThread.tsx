import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { chatApi, type Message, type MessageThread as MessageThreadType } from '@/lib/api';
import { EnhancedMessage } from './EnhancedMessage';
import { 
  ChevronDown,
  ChevronRight,
  MessageSquare,
  Clock,
  User,
  Bot,
  GitBranch,
  Loader2,
  AlertCircle
} from 'lucide-react';

interface MessageThreadProps {
  rootMessageId: string;
  conversationId: string;
  onMessageEdited?: (editResponse: any) => void;
  onMessageResubmit?: (messageId: string) => void;
  className?: string;
}

interface ThreadNode {
  message: Message;
  children: ThreadNode[];
  depth: number;
}

export const MessageThread: React.FC<MessageThreadProps> = ({
  rootMessageId,
  conversationId,
  onMessageEdited,
  onMessageResubmit,
  className = ''
}) => {
  const [threadData, setThreadData] = useState<MessageThreadType | null>(null);
  const [threadTree, setThreadTree] = useState<ThreadNode[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const { toast } = useToast();

  useEffect(() => {
    if (rootMessageId) {
      fetchMessageThread();
    }
  }, [rootMessageId]);

  const fetchMessageThread = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const thread = await chatApi.getMessageThread(rootMessageId);
      setThreadData(thread);
      buildThreadTree(thread.messages);
    } catch (error) {
      console.error('Error fetching message thread:', error);
      setError(error instanceof Error ? error.message : 'Failed to load message thread');
    } finally {
      setIsLoading(false);
    }
  };

  const buildThreadTree = (messages: Message[]) => {
    // Create a map of message ID to message for quick lookup
    const messageMap = new Map<string, Message>();
    messages.forEach(msg => messageMap.set(msg.id, msg));

    // Build the tree structure
    const rootNodes: ThreadNode[] = [];
    const nodeMap = new Map<string, ThreadNode>();

    // First pass: create all nodes
    messages.forEach(message => {
      const node: ThreadNode = {
        message,
        children: [],
        depth: 0
      };
      nodeMap.set(message.id, node);
    });

    // Second pass: build parent-child relationships
    messages.forEach(message => {
      const node = nodeMap.get(message.id)!;
      
      if (message.metadata?.parent_message_id) {
        const parentNode = nodeMap.get(message.metadata.parent_message_id);
        if (parentNode) {
          parentNode.children.push(node);
          node.depth = parentNode.depth + 1;
        } else {
          rootNodes.push(node);
        }
      } else {
        rootNodes.push(node);
      }
    });

    // Sort by creation time
    const sortNodes = (nodes: ThreadNode[]) => {
      nodes.sort((a, b) => new Date(a.message.created_at).getTime() - new Date(b.message.created_at).getTime());
      nodes.forEach(node => sortNodes(node.children));
    };

    sortNodes(rootNodes);
    setThreadTree(rootNodes);

    // Auto-expand root nodes
    const autoExpanded = new Set<string>();
    rootNodes.forEach(node => autoExpanded.add(node.message.id));
    setExpandedNodes(autoExpanded);
  };

  const toggleNodeExpansion = (messageId: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(messageId)) {
      newExpanded.delete(messageId);
    } else {
      newExpanded.add(messageId);
    }
    setExpandedNodes(newExpanded);
  };

  const renderThreadNode = (node: ThreadNode, index: number) => {
    const isExpanded = expandedNodes.has(node.message.id);
    const hasChildren = node.children.length > 0;
    const indentLevel = Math.min(node.depth, 5); // Limit indentation depth

    return (
      <motion.div
        key={node.message.id}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.05 }}
        className={`${indentLevel > 0 ? 'ml-4' : ''}`}
        style={{ marginLeft: indentLevel > 1 ? `${(indentLevel - 1) * 16}px` : undefined }}
      >
        <div className="flex items-start gap-2 mb-2">
          {/* Thread connector line */}
          {indentLevel > 0 && (
            <div className="flex items-center">
              <div className="w-4 h-4 border-l-2 border-b-2 border-gray-300 rounded-bl-md" />
              <GitBranch className="h-3 w-3 text-gray-400 ml-1" />
            </div>
          )}

          {/* Expand/collapse button */}
          {hasChildren && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => toggleNodeExpansion(node.message.id)}
              className="h-6 w-6 p-0 rounded-full"
            >
              {isExpanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronRight className="h-3 w-3" />
              )}
            </Button>
          )}

          {/* Message content */}
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              {node.message.sender === 'user' ? (
                <User className="h-4 w-4 text-blue-500" />
              ) : (
                <Bot className="h-4 w-4 text-green-500" />
              )}
              <Badge variant="outline" className="text-xs">
                <Clock className="h-3 w-3 mr-1" />
                {new Date(node.message.created_at).toLocaleString()}
              </Badge>
              {hasChildren && (
                <Badge variant="secondary" className="text-xs">
                  {node.children.length} {node.children.length === 1 ? 'reply' : 'replies'}
                </Badge>
              )}
            </div>

            <EnhancedMessage
              id={node.message.id}
              conversationId={conversationId}
              content={node.message.content}
              sender={node.message.sender}
              timestamp={node.message.created_at}
              metadata={node.message.metadata}
              onMessageEdited={onMessageEdited}
              onMessageResubmit={onMessageResubmit}
              className="border-l-2 border-gray-200 pl-3"
            />
          </div>
        </div>

        {/* Render children */}
        <AnimatePresence>
          {isExpanded && hasChildren && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <div className="space-y-2">
                {node.children.map((childNode, childIndex) => 
                  renderThreadNode(childNode, childIndex)
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex items-center gap-2 text-gray-500">
          <Loader2 className="h-5 w-5 animate-spin" />
          <span>Loading message thread...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex items-center gap-2 text-red-500">
          <AlertCircle className="h-5 w-5" />
          <span>{error}</span>
        </div>
      </div>
    );
  }

  if (!threadData || threadTree.length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex items-center gap-2 text-gray-500">
          <MessageSquare className="h-5 w-5" />
          <span>No thread data available</span>
        </div>
      </div>
    );
  }

  return (
    <Card className={`${className}`}>
      <CardContent className="p-4">
        <div className="flex items-center gap-2 mb-4">
          <MessageSquare className="h-5 w-5 text-blue-500" />
          <h3 className="font-medium">Message Thread</h3>
          <Badge variant="outline" className="text-xs">
            {threadData.messages.length} messages
          </Badge>
        </div>

        <ScrollArea className="max-h-96">
          <div className="space-y-4">
            {threadTree.map((node, index) => renderThreadNode(node, index))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};
