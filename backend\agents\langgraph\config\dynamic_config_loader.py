"""
Dynamic Configuration Loader for Extensible Persona System.

This module provides dynamic configuration loading from multiple sources
including files, databases, APIs, and environment variables, ensuring
complete extensibility without hardcoded values.
"""

import logging
import os
import json
import yaml
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from abc import ABC, abstractmethod
import asyncio
import aiohttp
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class ConfigurationSource(ABC):
    """Abstract base class for configuration sources."""
    
    @abstractmethod
    async def load_config(self, config_id: str) -> Optional[Dict[str, Any]]:
        """Load configuration by ID."""
        pass
    
    @abstractmethod
    async def list_configs(self) -> List[str]:
        """List available configuration IDs."""
        pass
    
    @abstractmethod
    async def save_config(self, config_id: str, config: Dict[str, Any]) -> bool:
        """Save configuration."""
        pass


class FileConfigurationSource(ConfigurationSource):
    """Configuration source that loads from files."""
    
    def __init__(self, config_dir: Path, file_extensions: List[str] = None):
        """Initialize file configuration source."""
        self.config_dir = Path(config_dir)
        self.file_extensions = file_extensions or [".yaml", ".yml", ".json"]
        self.logger = logging.getLogger(f"{__name__}.FileConfigurationSource")
        
        # Ensure directory exists
        self.config_dir.mkdir(parents=True, exist_ok=True)
    
    async def load_config(self, config_id: str) -> Optional[Dict[str, Any]]:
        """Load configuration from file."""
        for ext in self.file_extensions:
            config_file = self.config_dir / f"{config_id}{ext}"
            if config_file.exists():
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        if ext in [".yaml", ".yml"]:
                            return yaml.safe_load(f)
                        elif ext == ".json":
                            return json.load(f)
                except Exception as e:
                    self.logger.error(f"Error loading config {config_id} from {config_file}: {e}")
        
        return None
    
    async def list_configs(self) -> List[str]:
        """List available configuration files."""
        configs = set()
        for ext in self.file_extensions:
            for config_file in self.config_dir.glob(f"*{ext}"):
                configs.add(config_file.stem)
        return sorted(list(configs))
    
    async def save_config(self, config_id: str, config: Dict[str, Any]) -> bool:
        """Save configuration to file."""
        try:
            config_file = self.config_dir / f"{config_id}.yaml"
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, sort_keys=False)
            return True
        except Exception as e:
            self.logger.error(f"Error saving config {config_id}: {e}")
            return False


class DatabaseConfigurationSource(ConfigurationSource):
    """Configuration source that loads from database."""
    
    def __init__(self, db_connection_string: str, table_name: str = "persona_configurations"):
        """Initialize database configuration source."""
        self.db_connection_string = db_connection_string
        self.table_name = table_name
        self.logger = logging.getLogger(f"{__name__}.DatabaseConfigurationSource")
    
    async def load_config(self, config_id: str) -> Optional[Dict[str, Any]]:
        """Load configuration from database."""
        try:
            # This would use actual database connection
            # For now, return None to indicate not implemented
            self.logger.warning("Database configuration source not yet implemented")
            return None
        except Exception as e:
            self.logger.error(f"Error loading config {config_id} from database: {e}")
            return None
    
    async def list_configs(self) -> List[str]:
        """List available configurations in database."""
        try:
            # This would query the database
            self.logger.warning("Database configuration source not yet implemented")
            return []
        except Exception as e:
            self.logger.error(f"Error listing configs from database: {e}")
            return []
    
    async def save_config(self, config_id: str, config: Dict[str, Any]) -> bool:
        """Save configuration to database."""
        try:
            # This would save to database
            self.logger.warning("Database configuration source not yet implemented")
            return False
        except Exception as e:
            self.logger.error(f"Error saving config {config_id} to database: {e}")
            return False


class APIConfigurationSource(ConfigurationSource):
    """Configuration source that loads from external API."""
    
    def __init__(self, api_base_url: str, api_key: Optional[str] = None):
        """Initialize API configuration source."""
        self.api_base_url = api_base_url.rstrip('/')
        self.api_key = api_key
        self.logger = logging.getLogger(f"{__name__}.APIConfigurationSource")
    
    async def load_config(self, config_id: str) -> Optional[Dict[str, Any]]:
        """Load configuration from API."""
        try:
            headers = {}
            if self.api_key:
                headers["Authorization"] = f"Bearer {self.api_key}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.api_base_url}/configs/{config_id}",
                    headers=headers
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    elif response.status == 404:
                        return None
                    else:
                        self.logger.error(f"API error loading config {config_id}: {response.status}")
                        return None
        except Exception as e:
            self.logger.error(f"Error loading config {config_id} from API: {e}")
            return None
    
    async def list_configs(self) -> List[str]:
        """List available configurations from API."""
        try:
            headers = {}
            if self.api_key:
                headers["Authorization"] = f"Bearer {self.api_key}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.api_base_url}/configs",
                    headers=headers
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get("configs", [])
                    else:
                        self.logger.error(f"API error listing configs: {response.status}")
                        return []
        except Exception as e:
            self.logger.error(f"Error listing configs from API: {e}")
            return []
    
    async def save_config(self, config_id: str, config: Dict[str, Any]) -> bool:
        """Save configuration to API."""
        try:
            headers = {"Content-Type": "application/json"}
            if self.api_key:
                headers["Authorization"] = f"Bearer {self.api_key}"
            
            async with aiohttp.ClientSession() as session:
                async with session.put(
                    f"{self.api_base_url}/configs/{config_id}",
                    headers=headers,
                    json=config
                ) as response:
                    return response.status in [200, 201]
        except Exception as e:
            self.logger.error(f"Error saving config {config_id} to API: {e}")
            return False


class EnvironmentConfigurationSource(ConfigurationSource):
    """Configuration source that loads from environment variables."""
    
    def __init__(self, prefix: str = "DATAGENIUS_PERSONA_"):
        """Initialize environment configuration source."""
        self.prefix = prefix
        self.logger = logging.getLogger(f"{__name__}.EnvironmentConfigurationSource")
    
    async def load_config(self, config_id: str) -> Optional[Dict[str, Any]]:
        """Load configuration from environment variables."""
        try:
            env_var = f"{self.prefix}{config_id.upper()}"
            config_json = os.getenv(env_var)
            
            if config_json:
                return json.loads(config_json)
            
            return None
        except Exception as e:
            self.logger.error(f"Error loading config {config_id} from environment: {e}")
            return None
    
    async def list_configs(self) -> List[str]:
        """List available configurations in environment."""
        configs = []
        for key in os.environ:
            if key.startswith(self.prefix):
                config_id = key[len(self.prefix):].lower()
                configs.append(config_id)
        return sorted(configs)
    
    async def save_config(self, config_id: str, config: Dict[str, Any]) -> bool:
        """Save configuration to environment (not persistent)."""
        try:
            env_var = f"{self.prefix}{config_id.upper()}"
            os.environ[env_var] = json.dumps(config)
            return True
        except Exception as e:
            self.logger.error(f"Error saving config {config_id} to environment: {e}")
            return False


class DynamicConfigurationLoader:
    """
    Dynamic configuration loader that supports multiple sources.
    
    Loads configurations from files, databases, APIs, and environment variables
    with caching and fallback mechanisms.
    """
    
    def __init__(self):
        """Initialize the dynamic configuration loader."""
        self.logger = logging.getLogger(__name__)
        
        # Configuration sources (ordered by priority)
        self.sources: List[ConfigurationSource] = []
        
        # Configuration cache
        self.config_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_timestamps: Dict[str, datetime] = {}
        self.cache_ttl = timedelta(minutes=30)  # 30 minute cache TTL
        
        # Initialize default sources
        self._initialize_default_sources()
        
        self.logger.info("DynamicConfigurationLoader initialized")
    
    def _initialize_default_sources(self) -> None:
        """Initialize default configuration sources."""
        # File source (highest priority)
        config_dir = Path(__file__).parent / "personas"
        self.add_source(FileConfigurationSource(config_dir))
        
        # Environment source
        self.add_source(EnvironmentConfigurationSource())
        
        # Database source (if configured)
        db_url = os.getenv("DATAGENIUS_CONFIG_DB_URL")
        if db_url:
            self.add_source(DatabaseConfigurationSource(db_url))
        
        # API source (if configured)
        api_url = os.getenv("DATAGENIUS_CONFIG_API_URL")
        api_key = os.getenv("DATAGENIUS_CONFIG_API_KEY")
        if api_url:
            self.add_source(APIConfigurationSource(api_url, api_key))
    
    def add_source(self, source: ConfigurationSource) -> None:
        """Add a configuration source."""
        self.sources.append(source)
        self.logger.info(f"Added configuration source: {source.__class__.__name__}")
    
    async def load_config(
        self,
        config_id: str,
        use_cache: bool = True,
        merge_sources: bool = False
    ) -> Optional[Dict[str, Any]]:
        """
        Load configuration by ID from available sources.
        
        Args:
            config_id: Configuration identifier
            use_cache: Whether to use cached configuration
            merge_sources: Whether to merge configurations from multiple sources
            
        Returns:
            Configuration dictionary or None if not found
        """
        # Check cache first
        if use_cache and self._is_cached_valid(config_id):
            return self.config_cache[config_id]
        
        if merge_sources:
            # Merge configurations from all sources
            merged_config = {}
            for source in reversed(self.sources):  # Reverse for priority order
                try:
                    config = await source.load_config(config_id)
                    if config:
                        merged_config.update(config)
                except Exception as e:
                    self.logger.error(f"Error loading from source {source.__class__.__name__}: {e}")
            
            if merged_config:
                self._cache_config(config_id, merged_config)
                return merged_config
        else:
            # Load from first available source
            for source in self.sources:
                try:
                    config = await source.load_config(config_id)
                    if config:
                        self._cache_config(config_id, config)
                        return config
                except Exception as e:
                    self.logger.error(f"Error loading from source {source.__class__.__name__}: {e}")
        
        return None
    
    async def list_all_configs(self) -> List[str]:
        """List all available configuration IDs from all sources."""
        all_configs = set()
        
        for source in self.sources:
            try:
                configs = await source.list_configs()
                all_configs.update(configs)
            except Exception as e:
                self.logger.error(f"Error listing from source {source.__class__.__name__}: {e}")
        
        return sorted(list(all_configs))
    
    async def save_config(
        self,
        config_id: str,
        config: Dict[str, Any],
        source_index: int = 0
    ) -> bool:
        """
        Save configuration to specified source.
        
        Args:
            config_id: Configuration identifier
            config: Configuration data
            source_index: Index of source to save to (default: first source)
            
        Returns:
            True if saved successfully
        """
        if source_index >= len(self.sources):
            self.logger.error(f"Invalid source index: {source_index}")
            return False
        
        try:
            source = self.sources[source_index]
            success = await source.save_config(config_id, config)
            
            if success:
                # Update cache
                self._cache_config(config_id, config)
            
            return success
        except Exception as e:
            self.logger.error(f"Error saving config {config_id}: {e}")
            return False
    
    def _is_cached_valid(self, config_id: str) -> bool:
        """Check if cached configuration is still valid."""
        if config_id not in self.config_cache:
            return False
        
        if config_id not in self.cache_timestamps:
            return False
        
        return datetime.now() - self.cache_timestamps[config_id] < self.cache_ttl
    
    def _cache_config(self, config_id: str, config: Dict[str, Any]) -> None:
        """Cache configuration with timestamp."""
        self.config_cache[config_id] = config
        self.cache_timestamps[config_id] = datetime.now()
    
    def clear_cache(self, config_id: Optional[str] = None) -> None:
        """Clear configuration cache."""
        if config_id:
            self.config_cache.pop(config_id, None)
            self.cache_timestamps.pop(config_id, None)
        else:
            self.config_cache.clear()
            self.cache_timestamps.clear()
        
        self.logger.info(f"Cleared cache for {config_id or 'all configurations'}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "cached_configs": len(self.config_cache),
            "cache_ttl_minutes": self.cache_ttl.total_seconds() / 60,
            "sources_count": len(self.sources),
            "source_types": [source.__class__.__name__ for source in self.sources]
        }


# Global configuration loader instance
dynamic_config_loader = DynamicConfigurationLoader()
