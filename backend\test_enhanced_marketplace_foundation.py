"""
Test script for Enhanced Marketplace Foundation (Phase 1).

This script tests the basic functionality of the enhanced marketplace
infrastructure to ensure it's operational before proceeding to Phase 2.
"""

import asyncio
import logging
import sys
import os
from typing import Dict, Any

# Add backend to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.database import get_db, engine
from app.services.enhanced_marketplace_service import (
    persona_config_service,
    agent_plugin_service,
    hierarchical_message_service,
    workflow_adaptation_service
)

# Test LangGraph components
try:
    from agents.langgraph.core.marketplace_agent_factory import MarketplaceAgentFactory
    from agents.langgraph.core.persona_workflow_builder import PersonaWorkflowBuilder
    from agents.langgraph.industry.specialization_manager import IndustrySpecializationManager
    from agents.langgraph.plugins.plugin_manager import AgentPluginManager
    from agents.langgraph.messaging.hierarchical_message_manager import HierarchicalMessageManager
    LANGGRAPH_AVAILABLE = True
except ImportError as e:
    print(f"LangGraph components not available: {e}")
    LANGGRAPH_AVAILABLE = False

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_database_tables():
    """Test that all enhanced marketplace tables exist."""
    logger.info("Testing database tables...")
    
    try:
        from sqlalchemy import inspect
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        enhanced_tables = [
            'persona_configurations', 'agent_plugins', 'message_threads',
            'message_edit_history', 'industry_templates', 'workflow_adaptations',
            'conversation_resubmissions', 'workflow_experiments', 
            'experiment_participations', 'workflow_performance_metrics'
        ]
        
        missing_tables = []
        for table in enhanced_tables:
            if table not in tables:
                missing_tables.append(table)
        
        if missing_tables:
            logger.error(f"Missing tables: {missing_tables}")
            return False
        
        logger.info("✓ All enhanced marketplace tables exist")
        return True
        
    except Exception as e:
        logger.error(f"Database table test failed: {e}")
        return False


async def test_database_services():
    """Test database services functionality."""
    logger.info("Testing database services...")
    
    try:
        db = next(get_db())
        
        # Test persona configuration service
        test_config = {
            "test": True,
            "methodology_framework": "UNDERSTAND_ASSESS_EXECUTE_DELIVER"
        }
        
        config = persona_config_service.create_persona_configuration(
            db=db,
            persona_id="test-persona",
            configuration=test_config,
            industry_specialization="technology"
        )
        
        if not config:
            logger.error("Failed to create persona configuration")
            return False
        
        # Test retrieval
        retrieved_config = persona_config_service.get_persona_configuration(db, "test-persona")
        if not retrieved_config:
            logger.error("Failed to retrieve persona configuration")
            return False
        
        logger.info("✓ Database services working correctly")
        
        # Cleanup
        db.delete(config)
        db.commit()
        
        return True
        
    except Exception as e:
        logger.error(f"Database services test failed: {e}")
        return False


async def test_langgraph_components():
    """Test LangGraph components initialization."""
    logger.info("Testing LangGraph components...")
    
    if not LANGGRAPH_AVAILABLE:
        logger.warning("LangGraph components not available, skipping test")
        return True
    
    try:
        # Test MarketplaceAgentFactory
        marketplace_factory = MarketplaceAgentFactory()
        await marketplace_factory.initialize()
        logger.info("✓ MarketplaceAgentFactory initialized")
        
        # Test IndustrySpecializationManager
        industry_manager = IndustrySpecializationManager()
        await industry_manager.initialize()
        logger.info("✓ IndustrySpecializationManager initialized")
        
        # Test AgentPluginManager
        plugin_manager = AgentPluginManager()
        await plugin_manager.initialize()
        logger.info("✓ AgentPluginManager initialized")
        
        # Test HierarchicalMessageManager
        message_manager = HierarchicalMessageManager()
        await message_manager.initialize()
        logger.info("✓ HierarchicalMessageManager initialized")
        
        logger.info("✓ All LangGraph components initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"LangGraph components test failed: {e}")
        return False


async def test_api_endpoints():
    """Test API endpoints availability."""
    logger.info("Testing API endpoints...")
    
    try:
        # Import the FastAPI app
        from app.main import app
        
        # Check if enhanced personas router is included
        routes = [route.path for route in app.routes]
        enhanced_routes = [route for route in routes if route.startswith("/enhanced-personas")]
        
        if not enhanced_routes:
            logger.error("Enhanced personas API endpoints not found")
            return False
        
        expected_routes = [
            "/enhanced-personas/marketplace/{user_id}",
            "/enhanced-personas/workflows/create",
            "/enhanced-personas/configurations/{persona_id}",
            "/enhanced-personas/plugins",
            "/enhanced-personas/adaptations/{user_id}"
        ]
        
        logger.info(f"Found enhanced routes: {enhanced_routes}")
        logger.info("✓ Enhanced personas API endpoints available")
        return True
        
    except Exception as e:
        logger.error(f"API endpoints test failed: {e}")
        return False


async def test_integration_workflow():
    """Test a complete integration workflow."""
    logger.info("Testing integration workflow...")
    
    if not LANGGRAPH_AVAILABLE:
        logger.warning("LangGraph not available, skipping integration workflow test")
        return True
    
    try:
        db = next(get_db())
        
        # 1. Create a persona configuration
        test_config = {
            "workflow_type": "integration_test",
            "capabilities": ["analysis", "reporting"]
        }
        
        config = persona_config_service.create_persona_configuration(
            db=db,
            persona_id="integration-test-persona",
            configuration=test_config,
            industry_specialization="technology"
        )
        
        # 2. Test marketplace agent discovery (mock)
        marketplace_factory = MarketplaceAgentFactory()
        await marketplace_factory.initialize()
        
        # 3. Create a workflow adaptation
        adaptation = workflow_adaptation_service.create_adaptation(
            db=db,
            workflow_id="test-workflow",
            persona_id="integration-test-persona",
            user_id=1,  # Assuming user ID 1 exists
            adaptation_type="performance_optimization",
            adaptation_triggers=["low_performance"],
            performance_data={"execution_time": 1.5}
        )
        
        logger.info("✓ Integration workflow completed successfully")
        
        # Cleanup
        db.delete(config)
        db.delete(adaptation)
        db.commit()
        
        return True
        
    except Exception as e:
        logger.error(f"Integration workflow test failed: {e}")
        return False


async def run_foundation_tests():
    """Run all foundation tests."""
    logger.info("Starting Enhanced Marketplace Foundation Tests...")
    
    tests = [
        ("Database Tables", test_database_tables),
        ("Database Services", test_database_services),
        ("LangGraph Components", test_langgraph_components),
        ("API Endpoints", test_api_endpoints),
        ("Integration Workflow", test_integration_workflow)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        try:
            result = await test_func()
            results[test_name] = result
            status = "✓ PASSED" if result else "✗ FAILED"
            logger.info(f"{test_name}: {status}")
        except Exception as e:
            logger.error(f"{test_name}: ✗ FAILED - {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("ENHANCED MARKETPLACE FOUNDATION TEST SUMMARY")
    logger.info("="*50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASSED" if result else "✗ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All foundation tests passed! Ready for Phase 2.")
        return True
    else:
        logger.error("❌ Some foundation tests failed. Please fix issues before proceeding.")
        return False


if __name__ == "__main__":
    asyncio.run(run_foundation_tests())
