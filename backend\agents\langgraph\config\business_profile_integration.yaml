# Business Profile Integration Configuration
# This configuration defines how business profiles integrate with the extensible persona system

# Global integration settings
global_settings:
  enable_auto_injection: true
  enable_industry_context: true
  enable_company_context: true
  enable_dynamic_customization: true
  cache_profile_data: true
  cache_duration_minutes: 60

# Context fields to inject from business profiles
context_fields:
  # Basic company information
  basic_info:
    - "company_name"
    - "industry"
    - "company_size"
    - "location"
    - "founded_year"
  
  # Business characteristics
  business_characteristics:
    - "business_model"
    - "revenue_model"
    - "target_market"
    - "competitive_position"
    - "growth_stage"
  
  # Marketing and brand
  marketing_brand:
    - "brand_voice"
    - "target_audience"
    - "value_proposition"
    - "marketing_channels"
    - "brand_guidelines"
  
  # Goals and metrics
  goals_metrics:
    - "business_goals"
    - "key_metrics"
    - "success_criteria"
    - "performance_targets"
    - "growth_objectives"
  
  # Operational context
  operational:
    - "team_size"
    - "budget_range"
    - "technology_stack"
    - "operational_constraints"
    - "resource_availability"

# Industry-specific context enrichment
industry_contexts:
  # Technology industry
  technology:
    common_metrics:
      - "user_acquisition_cost"
      - "monthly_recurring_revenue"
      - "churn_rate"
      - "product_market_fit"
    
    typical_challenges:
      - "scaling_infrastructure"
      - "talent_acquisition"
      - "market_competition"
      - "technology_adoption"
    
    recommended_tools:
      - "technical_analysis_tools"
      - "user_behavior_analytics"
      - "product_metrics_tracker"
    
    specialized_prompts:
      analysis: "Focus on technical metrics, user behavior, and product performance"
      marketing: "Emphasize technical benefits, developer experience, and innovation"
  
  # Healthcare industry
  healthcare:
    common_metrics:
      - "patient_satisfaction"
      - "treatment_outcomes"
      - "operational_efficiency"
      - "compliance_scores"
    
    typical_challenges:
      - "regulatory_compliance"
      - "patient_privacy"
      - "cost_management"
      - "quality_assurance"
    
    recommended_tools:
      - "compliance_checker"
      - "patient_data_analyzer"
      - "outcome_tracker"
    
    specialized_prompts:
      analysis: "Ensure HIPAA compliance and focus on patient outcomes"
      marketing: "Emphasize safety, efficacy, and regulatory approval"
  
  # Financial services
  financial:
    common_metrics:
      - "assets_under_management"
      - "client_retention_rate"
      - "risk_adjusted_returns"
      - "regulatory_capital_ratio"
    
    typical_challenges:
      - "regulatory_compliance"
      - "risk_management"
      - "market_volatility"
      - "digital_transformation"
    
    recommended_tools:
      - "risk_analyzer"
      - "compliance_monitor"
      - "portfolio_optimizer"
    
    specialized_prompts:
      analysis: "Focus on risk metrics, compliance, and financial performance"
      marketing: "Emphasize security, returns, and regulatory compliance"

# Company size-specific context
company_size_contexts:
  startup:
    typical_resources:
      - "limited_budget"
      - "small_team"
      - "high_agility"
      - "growth_focus"
    
    common_constraints:
      - "resource_limitations"
      - "time_pressure"
      - "market_uncertainty"
      - "funding_challenges"
    
    recommended_approaches:
      - "lean_methodologies"
      - "rapid_prototyping"
      - "growth_hacking"
      - "cost_optimization"
  
  small_business:
    typical_resources:
      - "moderate_budget"
      - "focused_team"
      - "local_market_knowledge"
      - "customer_relationships"
    
    common_constraints:
      - "limited_scale"
      - "resource_efficiency"
      - "market_competition"
      - "technology_adoption"
    
    recommended_approaches:
      - "customer_focus"
      - "operational_efficiency"
      - "niche_specialization"
      - "community_engagement"
  
  enterprise:
    typical_resources:
      - "substantial_budget"
      - "large_teams"
      - "established_processes"
      - "market_presence"
    
    common_constraints:
      - "organizational_complexity"
      - "change_management"
      - "regulatory_requirements"
      - "legacy_systems"
    
    recommended_approaches:
      - "strategic_planning"
      - "process_optimization"
      - "digital_transformation"
      - "stakeholder_management"

# Dynamic customization rules
customization_rules:
  # Persona-specific customizations
  persona_customizations:
    analysis:
      industry_focus:
        technology: "technical_metrics"
        healthcare: "patient_outcomes"
        financial: "risk_metrics"
      
      company_size_focus:
        startup: "growth_metrics"
        small_business: "efficiency_metrics"
        enterprise: "strategic_metrics"
    
    marketing:
      industry_focus:
        technology: "innovation_messaging"
        healthcare: "safety_messaging"
        financial: "trust_messaging"
      
      company_size_focus:
        startup: "growth_messaging"
        small_business: "value_messaging"
        enterprise: "scale_messaging"
  
  # Tool customizations based on profile
  tool_customizations:
    enable_industry_tools: true
    enable_size_specific_tools: true
    
    industry_tool_mappings:
      technology:
        - "technical_analyzer"
        - "user_behavior_tracker"
        - "product_metrics_tool"
      
      healthcare:
        - "compliance_checker"
        - "patient_outcome_analyzer"
        - "hipaa_validator"
      
      financial:
        - "risk_calculator"
        - "compliance_monitor"
        - "portfolio_analyzer"
    
    size_tool_mappings:
      startup:
        - "growth_tracker"
        - "lean_analyzer"
        - "mvp_validator"
      
      enterprise:
        - "strategic_planner"
        - "process_optimizer"
        - "stakeholder_manager"

# Context enrichment strategies
enrichment_strategies:
  # Automatic enrichment
  automatic_enrichment:
    enable_industry_data: true
    enable_market_data: true
    enable_competitor_data: true
    enable_trend_data: true
  
  # External data sources
  external_sources:
    industry_databases:
      enabled: false
      api_endpoints: []
    
    market_research:
      enabled: false
      data_providers: []
    
    competitor_intelligence:
      enabled: false
      monitoring_tools: []
  
  # Real-time enrichment
  realtime_enrichment:
    enable_live_updates: false
    update_frequency_minutes: 60
    cache_enriched_data: true

# Integration with persona system
persona_integration:
  # Prompt customization
  prompt_customization:
    enable_dynamic_prompts: true
    include_industry_context: true
    include_company_context: true
    include_goals_context: true
  
  # Tool selection
  tool_selection:
    enable_profile_based_selection: true
    prioritize_industry_tools: true
    consider_company_size: true
  
  # Response customization
  response_customization:
    adapt_language_style: true
    include_relevant_examples: true
    focus_on_profile_goals: true

# Performance and caching
performance:
  # Caching strategies
  caching:
    cache_profile_data: true
    cache_enriched_context: true
    cache_industry_data: true
    cache_duration_minutes: 60
  
  # Optimization
  optimization:
    lazy_load_context: true
    batch_enrichment: true
    parallel_processing: true
  
  # Resource limits
  resource_limits:
    max_context_size_kb: 100
    max_enrichment_time_seconds: 10
    max_concurrent_enrichments: 5

# Validation and security
validation:
  # Data validation
  data_validation:
    validate_profile_data: true
    sanitize_inputs: true
    check_data_completeness: true
  
  # Privacy and security
  privacy_security:
    anonymize_sensitive_data: true
    encrypt_stored_profiles: true
    audit_profile_access: true
    respect_data_retention_policies: true

# Monitoring and analytics
monitoring:
  # Usage tracking
  usage_tracking:
    track_profile_usage: true
    track_enrichment_performance: true
    track_customization_effectiveness: true
  
  # Analytics
  analytics:
    measure_persona_performance: true
    analyze_profile_impact: true
    optimize_customizations: true
  
  # Reporting
  reporting:
    generate_usage_reports: true
    create_performance_dashboards: true
    provide_optimization_recommendations: true
