#!/usr/bin/env node
/**
 * Frontend Persona ID Update Script
 *
 * This script updates legacy persona IDs to unified persona IDs throughout the frontend codebase.
 * Run this script from the frontend directory: node update-persona-ids.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Persona ID mappings
const PERSONA_MAPPINGS = {
  'composable-analysis-ai': 'analysis',
  'composable-marketing-ai': 'marketing',
  'concierge-agent': 'concierge',
  'classification-agent': 'classification'
};

// File extensions to process
const FILE_EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

// Directories to search
const SEARCH_DIRECTORIES = ['src'];

/**
 * Check if file should be processed
 */
function shouldProcessFile(filePath) {
  const ext = path.extname(filePath);
  return FILE_EXTENSIONS.includes(ext);
}

/**
 * Get all files recursively
 */
function getAllFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath);

  files.forEach(file => {
    const fullPath = path.join(dirPath, file);
    
    if (fs.statSync(fullPath).isDirectory()) {
      // Skip node_modules and other irrelevant directories
      if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
        arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
      }
    } else if (shouldProcessFile(fullPath)) {
      arrayOfFiles.push(fullPath);
    }
  });

  return arrayOfFiles;
}

/**
 * Update persona IDs in file content
 */
function updatePersonaIds(content) {
  let updatedContent = content;
  let hasChanges = false;

  // Update each persona ID mapping
  Object.entries(PERSONA_MAPPINGS).forEach(([oldId, newId]) => {
    // Match various patterns where persona IDs might appear
    const patterns = [
      // String literals with single quotes
      new RegExp(`'${oldId.replace(/[-]/g, '\\-')}'`, 'g'),
      // String literals with double quotes  
      new RegExp(`"${oldId.replace(/[-]/g, '\\-')}"`, 'g'),
      // Template literals
      new RegExp(`\`${oldId.replace(/[-]/g, '\\-')}\``, 'g'),
      // Object property access
      new RegExp(`\\.${oldId.replace(/[-]/g, '\\-')}`, 'g'),
      // URL paths
      new RegExp(`/${oldId.replace(/[-]/g, '\\-')}/`, 'g'),
      // API endpoints
      new RegExp(`/${oldId.replace(/[-]/g, '\\-')}`, 'g')
    ];

    patterns.forEach(pattern => {
      const originalContent = updatedContent;
      updatedContent = updatedContent.replace(pattern, (match) => {
        hasChanges = true;
        // Preserve the quote/delimiter style
        if (match.startsWith("'")) return `'${newId}'`;
        if (match.startsWith('"')) return `"${newId}"`;
        if (match.startsWith('`')) return `\`${newId}\``;
        if (match.startsWith('.')) return `.${newId}`;
        if (match.startsWith('/') && match.endsWith('/')) return `/${newId}/`;
        if (match.startsWith('/')) return `/${newId}`;
        return newId;
      });
    });
  });

  return { content: updatedContent, hasChanges };
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const { content: updatedContent, hasChanges } = updatePersonaIds(content);

    if (hasChanges) {
      fs.writeFileSync(filePath, updatedContent, 'utf8');
      console.log(`✅ Updated: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  No changes: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

/**
 * Main execution function
 */
function main() {
  console.log('🚀 Starting Frontend Persona ID Update...\n');
  
  console.log('📋 Persona ID Mappings:');
  Object.entries(PERSONA_MAPPINGS).forEach(([oldId, newId]) => {
    console.log(`   ${oldId} → ${newId}`);
  });
  console.log('');

  let totalFiles = 0;
  let updatedFiles = 0;

  // Process each search directory
  SEARCH_DIRECTORIES.forEach(searchDir => {
    if (!fs.existsSync(searchDir)) {
      console.log(`⚠️  Directory not found: ${searchDir}`);
      return;
    }

    console.log(`🔍 Searching directory: ${searchDir}`);
    const files = getAllFiles(searchDir);
    
    files.forEach(filePath => {
      totalFiles++;
      if (processFile(filePath)) {
        updatedFiles++;
      }
    });
  });

  console.log('\n📊 Summary:');
  console.log(`   Total files processed: ${totalFiles}`);
  console.log(`   Files updated: ${updatedFiles}`);
  console.log(`   Files unchanged: ${totalFiles - updatedFiles}`);

  if (updatedFiles > 0) {
    console.log('\n🎉 Frontend persona ID update completed successfully!');
    console.log('📝 Next steps:');
    console.log('   1. Review the changes in your version control system');
    console.log('   2. Test the application thoroughly');
    console.log('   3. Update any remaining hardcoded references manually');
    console.log('   4. Deploy the changes');
  } else {
    console.log('\n✨ No updates needed - all persona IDs are already current!');
  }
}

// Run the script
main();

export {
  updatePersonaIds,
  PERSONA_MAPPINGS
};
