"""
Comprehensive tests for the Enhanced MCP Integration system.

This test suite validates all components of the enhanced MCP integration,
including server management, tool discovery, agent integration, and security.
"""

import asyncio
import json
import pytest
import uuid
from unittest.mock import Mock, AsyncMock, patch
from sqlalchemy.orm import Session

from app.models.mcp_server import (
    MCPServerCreate, MCPJSONConfiguration, MCPServerDefinition,
    MCPInputDefinition, MCPServerConfig, MCPTransportType
)
from app.services.mcp_server_manager import MCPServerManager
from app.services.mcp_agent_integration import mcp_agent_integration
from app.services.agent_mcp_integration import agent_mcp_integration_service
from app.security.mcp_security import mcp_security_validator, mcp_error_handler
from app.database import MCPServer, MCPTool, MCPResource, MCPPrompt
from agents.tools.mcp.agent_integration import AgentMCPTool, mcp_tool_manager
from agents.components.base_component import AgentContext


class TestMCPServerManager:
    """Test suite for MCP Server Manager."""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def server_manager(self):
        """MCP Server Manager instance."""
        return MCPServerManager()
    
    @pytest.fixture
    def sample_json_config(self):
        """Sample JSON configuration."""
        return MCPJSONConfiguration(
            inputs=[
                MCPInputDefinition(
                    type="promptString",
                    id="api-key",
                    description="API Key for the service",
                    password=True
                )
            ],
            servers={
                "github": MCPServerDefinition(
                    url="https://api.githubcopilot.com/mcp/"
                ),
                "filesystem": MCPServerDefinition(
                    type="stdio",
                    command="npx",
                    args=["-y", "@modelcontextprotocol/server-filesystem", "/workspace"],
                    env={"API_KEY": "${input:api-key}"}
                )
            }
        )
    
    @pytest.fixture
    def sample_server_create(self, sample_json_config):
        """Sample server creation request."""
        return MCPServerCreate(
            name="Test MCP Server",
            description="Test server for integration testing",
            config_type="json",
            json_config=sample_json_config
        )
    
    @pytest.mark.asyncio
    async def test_create_server_from_json(self, server_manager, mock_db, sample_server_create):
        """Test creating MCP server from JSON configuration."""
        # Mock database operations
        mock_db.add = Mock()
        mock_db.commit = Mock()
        mock_db.refresh = Mock()
        
        # Create server
        server = await server_manager.create_server_from_json(
            db=mock_db,
            user_id=1,
            server_data=sample_server_create
        )
        
        # Verify server creation
        assert server is not None
        assert server.name == "Test MCP Server"
        assert server.config_type == "json"
        assert "servers" in server.configuration
        
        # Verify database operations
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_server_from_form(self, server_manager, mock_db):
        """Test creating MCP server from form configuration."""
        # Create form-based server request
        server_config = MCPServerConfig(
            name="HTTP Server",
            transport_type=MCPTransportType.HTTP,
            url="https://api.example.com/mcp"
        )
        
        server_create = MCPServerCreate(
            name="Form MCP Server",
            description="Form-based server",
            config_type="form",
            server_config=server_config
        )
        
        # Mock database operations
        mock_db.add = Mock()
        mock_db.commit = Mock()
        mock_db.refresh = Mock()
        
        # Create server
        server = await server_manager.create_server_from_form(
            db=mock_db,
            user_id=1,
            server_data=server_create
        )
        
        # Verify server creation
        assert server is not None
        assert server.name == "Form MCP Server"
        assert server.config_type == "form"
        assert server.transport_type == MCPTransportType.HTTP
    
    @pytest.mark.asyncio
    async def test_test_server_connection(self, server_manager, mock_db):
        """Test server connection testing."""
        # Mock server
        mock_server = Mock(spec=MCPServer)
        mock_server.id = "test-server-id"
        mock_server.configuration = {"transport_type": "http", "url": "https://api.example.com"}
        
        mock_db.query.return_value.filter.return_value.first.return_value = mock_server
        mock_db.commit = Mock()
        
        # Mock successful connection test
        with patch('app.utils.mcp_client.test_mcp_connection', return_value=True):
            success, error = await server_manager.test_server_connection(mock_db, "test-server-id")
        
        # Verify results
        assert success is True
        assert error is None
        assert mock_server.status == "active"


class TestMCPAgentIntegration:
    """Test suite for MCP Agent Integration."""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def sample_agent_context(self):
        """Sample agent context."""
        context = AgentContext(user_id="1", message="Test message")
        context.set_field("business_profile_id", "test-profile-id")
        context.set_field("agent_id", "test-agent")
        return context
    
    @pytest.mark.asyncio
    async def test_initialize_for_business_profile(self, mock_db):
        """Test initializing MCP tools for business profile."""
        # Mock active servers
        mock_server = Mock(spec=MCPServer)
        mock_server.id = "test-server-id"
        mock_server.name = "Test Server"
        
        mock_db.query.return_value.filter.return_value.all.return_value = [mock_server]
        
        # Mock server manager
        with patch.object(mcp_agent_integration.mcp_manager, 'start_server', return_value=True):
            with patch.object(mcp_agent_integration, '_get_server_capabilities', return_value={
                "tools": [{"id": "test-tool", "name": "test_tool", "description": "Test tool"}],
                "resources": [],
                "prompts": []
            }):
                capabilities = await mcp_agent_integration.initialize_for_business_profile(
                    db=mock_db,
                    business_profile_id="test-profile-id"
                )
        
        # Verify capabilities
        assert "tools" in capabilities
        assert "resources" in capabilities
        assert "prompts" in capabilities
        assert len(capabilities["tools"]) == 1
    
    @pytest.mark.asyncio
    async def test_get_available_tools_for_agent(self, mock_db):
        """Test getting available tools for an agent."""
        # Mock servers and tools
        mock_server = Mock(spec=MCPServer)
        mock_server.id = "test-server-id"
        mock_server.name = "Test Server"
        
        mock_tool = Mock(spec=MCPTool)
        mock_tool.tool_name = "test_tool"
        mock_tool.tool_description = "Test tool description"
        mock_tool.parameters = {"type": "object"}
        mock_tool.capabilities = {"read": True}
        mock_tool.is_enabled = True
        mock_tool.usage_count = 0
        
        mock_db.query.return_value.filter.return_value.all.return_value = [mock_server]
        mock_db.query.return_value.filter.return_value.filter.return_value.all.return_value = [mock_tool]
        
        # Get available tools
        tools = await mcp_agent_integration.get_available_tools_for_agent(
            db=mock_db,
            user_id=1,
            business_profile_id="test-profile-id"
        )
        
        # Verify tools
        assert len(tools) == 1
        assert tools[0]["name"] == "test_tool"
        assert tools[0]["type"] == "mcp_tool"
    
    @pytest.mark.asyncio
    async def test_execute_mcp_tool(self, mock_db):
        """Test executing an MCP tool."""
        # Mock server and client
        mock_server = Mock(spec=MCPServer)
        mock_server.id = "test-server-id"
        mock_server.name = "Test Server"
        
        mock_client = AsyncMock()
        mock_client.call_tool.return_value = {"content": "Tool result"}
        
        mock_tool = Mock(spec=MCPTool)
        mock_tool.usage_count = 0
        
        mock_db.query.return_value.filter.return_value.first.return_value = mock_server
        mock_db.query.return_value.filter.return_value.filter.return_value.first.return_value = mock_tool
        mock_db.commit = Mock()
        
        # Mock active client
        mcp_agent_integration.mcp_manager.active_clients["test-server-id"] = mock_client
        
        # Execute tool
        result = await mcp_agent_integration.execute_mcp_tool(
            db=mock_db,
            tool_id="mcp_test-server-id_test_tool",
            arguments={"param": "value"},
            user_id=1
        )
        
        # Verify execution
        assert result["success"] is True
        assert "result" in result
        assert mock_tool.usage_count == 1


class TestMCPSecurity:
    """Test suite for MCP Security validation."""
    
    @pytest.fixture
    def sample_safe_config(self):
        """Sample safe configuration."""
        return MCPJSONConfiguration(
            servers={
                "safe_server": MCPServerDefinition(
                    url="https://api.example.com/mcp"
                )
            }
        )
    
    @pytest.fixture
    def sample_unsafe_config(self):
        """Sample unsafe configuration."""
        return MCPJSONConfiguration(
            servers={
                "unsafe_server": MCPServerDefinition(
                    type="stdio",
                    command="rm",
                    args=["-rf", "/"]
                )
            }
        )
    
    @pytest.mark.asyncio
    async def test_validate_safe_configuration(self, sample_safe_config):
        """Test validation of safe configuration."""
        is_valid, errors = await mcp_security_validator.validate_server_configuration(
            config=sample_safe_config,
            user_id=1
        )
        
        assert is_valid is True
        assert len(errors) == 0
    
    @pytest.mark.asyncio
    async def test_validate_unsafe_configuration(self, sample_unsafe_config):
        """Test validation of unsafe configuration."""
        is_valid, errors = await mcp_security_validator.validate_server_configuration(
            config=sample_unsafe_config,
            user_id=1
        )
        
        assert is_valid is False
        assert len(errors) > 0
        assert any("not allowed" in error for error in errors)
    
    @pytest.mark.asyncio
    async def test_validate_blocked_domain(self):
        """Test validation of blocked domains."""
        config = MCPJSONConfiguration(
            servers={
                "blocked_server": MCPServerDefinition(
                    url="http://localhost:8080/mcp"
                )
            }
        )
        
        is_valid, errors = await mcp_security_validator.validate_server_configuration(
            config=config,
            user_id=1
        )
        
        assert is_valid is False
        assert any("Blocked domain" in error for error in errors)
    
    @pytest.mark.asyncio
    async def test_validate_tool_execution(self):
        """Test tool execution validation."""
        # Test safe execution
        is_valid, error = await mcp_security_validator.validate_tool_execution(
            tool_name="safe_tool",
            arguments={"param": "safe_value"},
            user_id=1
        )
        
        assert is_valid is True
        assert error is None
        
        # Test unsafe execution
        is_valid, error = await mcp_security_validator.validate_tool_execution(
            tool_name="unsafe_tool",
            arguments={"command": "rm -rf /"},
            user_id=1
        )
        
        assert is_valid is False
        assert "Dangerous value" in error


class TestMCPErrorHandling:
    """Test suite for MCP Error Handling."""
    
    def test_handle_configuration_error(self):
        """Test configuration error handling."""
        error = ValueError("Invalid configuration format")
        
        response = mcp_error_handler.handle_configuration_error(error)
        
        assert response["success"] is False
        assert response["category"] == "Configuration Error"
        assert "configuration" in response["message"].lower()
        assert len(response["suggestions"]) > 0
    
    def test_handle_connection_error(self):
        """Test connection error handling."""
        error = ConnectionError("Unable to connect to server")
        server_info = {"name": "Test Server", "url": "https://api.example.com"}
        
        response = mcp_error_handler.handle_connection_error(error, server_info)
        
        assert response["success"] is False
        assert response["category"] == "Connection Error"
        assert "connect" in response["message"].lower()
        assert response["context"]["server_info"] == server_info
    
    def test_handle_execution_error(self):
        """Test execution error handling."""
        error = RuntimeError("Tool execution failed")
        
        response = mcp_error_handler.handle_execution_error(
            error, 
            tool_name="test_tool",
            arguments={"param": "value"}
        )
        
        assert response["success"] is False
        assert response["category"] == "Execution Error"
        assert "test_tool" in response["message"]
        assert response["context"]["tool_name"] == "test_tool"
    
    def test_handle_security_error(self):
        """Test security error handling."""
        error = SecurityError("Blocked operation")
        
        response = mcp_error_handler.handle_security_error(error)
        
        assert response["success"] is False
        assert response["category"] == "Security Error"
        assert "security" in response["message"].lower()


class TestAgentMCPTool:
    """Test suite for Agent MCP Tool wrapper."""
    
    @pytest.fixture
    def mock_integration_service(self):
        """Mock integration service."""
        service = AsyncMock()
        service.execute_mcp_tool.return_value = {
            "success": True,
            "result": {"content": "Tool executed successfully"},
            "server_name": "Test Server"
        }
        return service
    
    @pytest.fixture
    def agent_tool(self, mock_integration_service):
        """Agent MCP tool instance."""
        return AgentMCPTool(
            name="test_tool",
            description="Test tool for agent integration",
            server_id="test-server-id",
            tool_name="test_tool",
            parameters={"type": "object", "properties": {"param": {"type": "string"}}},
            integration_service=mock_integration_service
        )
    
    @pytest.fixture
    def agent_context(self):
        """Agent context for testing."""
        context = AgentContext(user_id="1", message="Test message")
        context.set_field("db_session", Mock())
        return context
    
    @pytest.mark.asyncio
    async def test_execute_tool(self, agent_tool, agent_context, mock_integration_service):
        """Test tool execution through agent wrapper."""
        result = await agent_tool.execute(context=agent_context, param="test_value")
        
        assert result["success"] is True
        assert "result" in result
        assert result["tool_name"] == "test_tool"
        
        # Verify integration service was called
        mock_integration_service.execute_mcp_tool.assert_called_once()
    
    def test_validate_arguments(self, agent_tool):
        """Test argument validation."""
        # Valid arguments
        errors = agent_tool.validate_arguments({"param": "valid_value"})
        assert len(errors) == 0
        
        # Invalid arguments (missing required param if it was required)
        # This test would need to be adjusted based on actual parameter schema
    
    def test_get_schema(self, agent_tool):
        """Test getting tool schema."""
        schema = agent_tool.get_schema()
        
        assert schema["name"] == "test_tool"
        assert schema["type"] == "mcp_server_tool"
        assert "parameters" in schema
        assert "metadata" in schema


class TestIntegrationWorkflow:
    """Integration tests for complete MCP workflow."""
    
    @pytest.mark.asyncio
    async def test_complete_mcp_workflow(self):
        """Test complete MCP integration workflow."""
        # This would be a comprehensive integration test
        # that tests the entire flow from server creation
        # to tool execution through agents
        
        # 1. Create MCP server configuration
        # 2. Validate security
        # 3. Initialize server
        # 4. Discover tools
        # 5. Register with agent system
        # 6. Execute tool through agent
        # 7. Verify results
        
        # For now, this is a placeholder for the full integration test
        assert True  # Placeholder
    
    @pytest.mark.asyncio
    async def test_error_recovery_workflow(self):
        """Test error recovery in MCP workflow."""
        # Test various error scenarios and recovery mechanisms
        assert True  # Placeholder
    
    @pytest.mark.asyncio
    async def test_security_enforcement_workflow(self):
        """Test security enforcement throughout MCP workflow."""
        # Test that security measures are enforced at each step
        assert True  # Placeholder


# Custom exception for testing
class SecurityError(Exception):
    """Custom security error for testing."""
    pass


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
