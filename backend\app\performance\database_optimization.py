"""
Database optimization module for Phase 1 performance improvements.

This module implements database-level optimizations including:
- Index creation and optimization
- Query performance monitoring
- Database statistics collection
- Connection pool optimization
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
from sqlalchemy import text, Index, event
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Session

from ..database import engine, get_db
from ..models.dashboard_customization import Dashboard, DashboardSection, DashboardWidget
from ..config import DATABASE_URL

logger = logging.getLogger(__name__)


class DatabaseOptimizer:
    """
    Database optimization manager for Phase 1 performance improvements.
    
    Target improvements:
    - Reduce dashboard load time from ~2.5s to < 1.5s
    - Optimize database queries per load from ~15 to < 8
    - Improve query response times
    """

    def __init__(self):
        """Initialize the database optimizer."""
        self.optimization_stats = {
            "indexes_created": 0,
            "queries_optimized": 0,
            "performance_improvements": []
        }
        self.is_postgresql = "postgresql" in DATABASE_URL.lower()

    async def apply_dashboard_optimizations(self) -> Dict[str, Any]:
        """Apply all dashboard-specific database optimizations."""
        logger.info("Starting Phase 1 database optimizations...")
        
        results = {
            "indexes_created": 0,
            "optimizations_applied": [],
            "errors": []
        }
        
        try:
            # Create performance indexes
            indexes_created = await self._create_performance_indexes()
            results["indexes_created"] = indexes_created
            
            # Apply PostgreSQL-specific optimizations
            if self.is_postgresql:
                pg_optimizations = await self._apply_postgresql_optimizations()
                results["optimizations_applied"].extend(pg_optimizations)
            
            # Update database statistics
            await self._update_database_statistics()
            results["optimizations_applied"].append("database_statistics_updated")
            
            logger.info(f"Database optimizations completed: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Database optimization failed: {e}")
            results["errors"].append(str(e))
            return results

    async def _create_performance_indexes(self) -> int:
        """Create performance indexes for dashboard queries."""
        indexes_to_create = [
            # Dashboard table indexes
            {
                "table": "dashboards",
                "name": "idx_dashboards_user_default",
                "columns": ["user_id", "is_default"],
                "condition": "WHERE is_default = true"
            },
            {
                "table": "dashboards",
                "name": "idx_dashboards_user_updated",
                "columns": ["user_id", "updated_at"],
                "condition": None
            },
            
            # Dashboard sections indexes
            {
                "table": "dashboard_sections",
                "name": "idx_dashboard_sections_dashboard_position",
                "columns": ["dashboard_id", "position"],
                "condition": "WHERE is_active = true"
            },
            {
                "table": "dashboard_sections",
                "name": "idx_dashboard_sections_user_active",
                "columns": ["user_id", "is_active"],
                "condition": "WHERE is_active = true"
            },
            
            # Dashboard widgets indexes
            {
                "table": "dashboard_widgets",
                "name": "idx_dashboard_widgets_section_active",
                "columns": ["section_id", "is_active"],
                "condition": "WHERE is_active = true"
            },
            {
                "table": "dashboard_widgets",
                "name": "idx_dashboard_widgets_user_type",
                "columns": ["user_id", "widget_type"],
                "condition": None
            },
            
            # GIN indexes for JSON columns (PostgreSQL only)
            {
                "table": "dashboard_widgets",
                "name": "idx_dashboard_widgets_data_config_gin",
                "columns": ["data_config"],
                "type": "GIN",
                "condition": "WHERE data_config IS NOT NULL"
            },
            {
                "table": "dashboard_sections",
                "name": "idx_dashboard_sections_layout_config_gin",
                "columns": ["layout_config"],
                "type": "GIN",
                "condition": "WHERE layout_config IS NOT NULL"
            }
        ]
        
        created_count = 0
        db = next(get_db())
        
        try:
            for index_def in indexes_to_create:
                try:
                    # Skip GIN indexes for non-PostgreSQL databases
                    if index_def.get("type") == "GIN" and not self.is_postgresql:
                        continue
                    
                    # Check if index already exists
                    if await self._index_exists(db, index_def["name"]):
                        logger.info(f"Index {index_def['name']} already exists, skipping")
                        continue
                    
                    # Create the index
                    await self._create_index(db, index_def)
                    created_count += 1
                    logger.info(f"Created index: {index_def['name']}")
                    
                except Exception as e:
                    logger.warning(f"Failed to create index {index_def['name']}: {e}")
                    
        finally:
            db.close()
        
        return created_count

    async def _create_index(self, db: Session, index_def: Dict[str, Any]):
        """Create a single database index."""
        table_name = index_def["table"]
        index_name = index_def["name"]
        columns = index_def["columns"]
        index_type = index_def.get("type", "BTREE")
        condition = index_def.get("condition", "")

        if self.is_postgresql:
            # PostgreSQL syntax - use autocommit for CONCURRENTLY indexes
            columns_str = ", ".join(columns)
            if index_type == "GIN":
                sql = f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name} USING GIN ({columns_str})"
            else:
                sql = f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name} ({columns_str})"

            if condition:
                sql += f" {condition}"

            # For PostgreSQL, we need to use autocommit mode for CONCURRENTLY
            # But since CONCURRENTLY can cause issues, we'll use regular CREATE INDEX
            connection = db.get_bind().connect()
            try:
                # Use autocommit mode
                connection = connection.execution_options(autocommit=True)
                connection.execute(text(sql))
            finally:
                connection.close()
        else:
            # Generic SQL syntax
            columns_str = ", ".join(columns)
            sql = f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name} ({columns_str})"
            db.execute(text(sql))
            db.commit()

    async def _index_exists(self, db: Session, index_name: str) -> bool:
        """Check if an index already exists."""
        try:
            if self.is_postgresql:
                result = db.execute(text("""
                    SELECT 1 FROM pg_indexes
                    WHERE indexname = :index_name
                """), {"index_name": index_name})
            else:
                # SQLite
                result = db.execute(text("""
                    SELECT 1 FROM sqlite_master
                    WHERE type = 'index' AND name = :index_name
                """), {"index_name": index_name})

            return result.fetchone() is not None
        except Exception as e:
            logger.warning(f"Error checking if index {index_name} exists: {e}")
            # If we can't check, assume it doesn't exist and try to create it
            return False

    async def _apply_postgresql_optimizations(self) -> List[str]:
        """Apply PostgreSQL-specific optimizations."""
        optimizations = []

        try:
            # Use engine directly for ALTER SYSTEM commands (cannot run in transaction)
            with engine.connect() as conn:
                # Set autocommit mode for ALTER SYSTEM commands
                conn.execute(text("COMMIT"))  # End any existing transaction

                try:
                    # Enable auto-vacuum for better performance
                    conn.execute(text("ALTER SYSTEM SET autovacuum = on"))
                    optimizations.append("autovacuum_enabled")
                except Exception as e:
                    logger.warning(f"Failed to set autovacuum: {e}")

                try:
                    # Optimize shared buffers for dashboard workload
                    conn.execute(text("ALTER SYSTEM SET shared_buffers = '256MB'"))
                    optimizations.append("shared_buffers_optimized")
                except Exception as e:
                    logger.warning(f"Failed to set shared_buffers: {e}")

                try:
                    # Optimize work memory for complex queries
                    conn.execute(text("ALTER SYSTEM SET work_mem = '64MB'"))
                    optimizations.append("work_mem_optimized")
                except Exception as e:
                    logger.warning(f"Failed to set work_mem: {e}")

                try:
                    # Reload configuration
                    conn.execute(text("SELECT pg_reload_conf()"))
                    optimizations.append("configuration_reloaded")
                except Exception as e:
                    logger.warning(f"Failed to reload configuration: {e}")

            logger.info(f"Applied PostgreSQL optimizations: {optimizations}")

        except Exception as e:
            logger.warning(f"PostgreSQL optimization failed: {e}")

        return optimizations

    async def _update_database_statistics(self):
        """Update database statistics for better query planning."""
        db = next(get_db())
        
        try:
            if self.is_postgresql:
                # Update statistics for dashboard tables
                tables = ["dashboards", "dashboard_sections", "dashboard_widgets"]
                for table in tables:
                    db.execute(text(f"ANALYZE {table}"))
                logger.info("PostgreSQL statistics updated")
            else:
                # SQLite
                db.execute(text("ANALYZE"))
                logger.info("SQLite statistics updated")
                
        except Exception as e:
            logger.warning(f"Statistics update failed: {e}")
        finally:
            db.close()

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get database performance metrics."""
        db = next(get_db())
        metrics = {}

        # Check if performance monitoring is disabled
        import os
        if os.getenv("DISABLE_DB_PERFORMANCE_MONITORING", "false").lower() == "true":
            logger.info("Database performance monitoring is disabled")
            return {
                "table_stats": [],
                "index_stats": [],
                "dashboard_count": 0,
                "widget_count": 0,
                "monitoring_disabled": True
            }

        try:
            if self.is_postgresql:
                # Get PostgreSQL-specific metrics with version compatibility
                try:
                    # Try with relname first (more compatible) - remove await since db.execute is synchronous
                    result = db.execute(text("""
                        SELECT
                            schemaname,
                            relname as table_name,
                            n_tup_ins as inserts,
                            n_tup_upd as updates,
                            n_tup_del as deletes,
                            n_live_tup as live_tuples,
                            n_dead_tup as dead_tuples
                        FROM pg_stat_user_tables
                        WHERE relname IN ('dashboards', 'dashboard_sections', 'dashboard_widgets')
                    """))

                    # Convert rows to dictionaries safely
                    rows = result.fetchall()
                    metrics["table_stats"] = []
                    for row in rows:
                        try:
                            if hasattr(row, '_mapping'):
                                # SQLAlchemy Row object with _mapping attribute
                                metrics["table_stats"].append(dict(row._mapping))
                            elif hasattr(row, 'keys'):
                                # Row object with keys method
                                metrics["table_stats"].append({key: row[key] for key in row.keys()})
                            else:
                                # Fallback: assume it's a tuple and create dict with column names
                                columns = ['schemaname', 'table_name', 'inserts', 'updates', 'deletes', 'live_tuples', 'dead_tuples']
                                metrics["table_stats"].append(dict(zip(columns, row)))
                        except Exception as row_error:
                            logger.warning(f"Failed to convert row to dict: {row_error}")
                            continue

                except Exception as e:
                    logger.warning(f"Failed to get table stats with relname, trying alternative: {e}")
                    try:
                        # Fallback: try with basic query without specific table filtering
                        result = db.execute(text("""
                            SELECT
                                schemaname,
                                'unknown' as table_name,
                                COALESCE(SUM(n_tup_ins), 0) as inserts,
                                COALESCE(SUM(n_tup_upd), 0) as updates,
                                COALESCE(SUM(n_tup_del), 0) as deletes,
                                COALESCE(SUM(n_live_tup), 0) as live_tuples,
                                COALESCE(SUM(n_dead_tup), 0) as dead_tuples
                            FROM pg_stat_user_tables
                            WHERE schemaname = 'public'
                            GROUP BY schemaname
                        """))

                        # Convert rows to dictionaries safely
                        rows = result.fetchall()
                        metrics["table_stats"] = []
                        for row in rows:
                            try:
                                if hasattr(row, '_mapping'):
                                    metrics["table_stats"].append(dict(row._mapping))
                                elif hasattr(row, 'keys'):
                                    metrics["table_stats"].append({key: row[key] for key in row.keys()})
                                else:
                                    columns = ['schemaname', 'table_name', 'inserts', 'updates', 'deletes', 'live_tuples', 'dead_tuples']
                                    metrics["table_stats"].append(dict(zip(columns, row)))
                            except Exception as row_error:
                                logger.warning(f"Failed to convert row to dict: {row_error}")
                                continue

                    except Exception as e2:
                        logger.warning(f"Failed to get table stats with fallback query: {e2}")
                        metrics["table_stats"] = []

                # Get index usage statistics with error handling
                try:
                    result = db.execute(text("""
                        SELECT
                            indexrelname as index_name,
                            idx_tup_read,
                            idx_tup_fetch
                        FROM pg_stat_user_indexes
                        WHERE schemaname = 'public'
                        AND indexrelname LIKE 'idx_dashboard%'
                    """))

                    # Convert rows to dictionaries safely
                    rows = result.fetchall()
                    metrics["index_stats"] = []
                    for row in rows:
                        try:
                            if hasattr(row, '_mapping'):
                                metrics["index_stats"].append(dict(row._mapping))
                            elif hasattr(row, 'keys'):
                                metrics["index_stats"].append({key: row[key] for key in row.keys()})
                            else:
                                columns = ['index_name', 'idx_tup_read', 'idx_tup_fetch']
                                metrics["index_stats"].append(dict(zip(columns, row)))
                        except Exception as row_error:
                            logger.warning(f"Failed to convert index row to dict: {row_error}")
                            continue

                except Exception as e:
                    logger.warning(f"Failed to get index stats: {e}")
                    metrics["index_stats"] = []
            
            # Get general table sizes
            try:
                result = db.execute(text("""
                    SELECT
                        COUNT(*) as dashboard_count
                    FROM dashboards
                """))
                metrics["dashboard_count"] = result.fetchone()[0]

                result = db.execute(text("""
                    SELECT
                        COUNT(*) as widget_count
                    FROM dashboard_widgets
                """))
                metrics["widget_count"] = result.fetchone()[0]
            except Exception as e:
                logger.warning(f"Failed to get table counts: {e}")
                metrics["dashboard_count"] = 0
                metrics["widget_count"] = 0
            
        except Exception as e:
            logger.error(f"Failed to get performance metrics: {e}")
        finally:
            db.close()
        
        return metrics


# Global database optimizer instance
db_optimizer = DatabaseOptimizer()


# Event listener for query performance monitoring
@event.listens_for(Engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """Record query start time."""
    context._query_start_time = datetime.now()


@event.listens_for(Engine, "after_cursor_execute")
def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """Record query execution time."""
    if hasattr(context, '_query_start_time'):
        execution_time = (datetime.now() - context._query_start_time).total_seconds()
        
        # Log slow queries
        if execution_time > 1.0:  # 1 second threshold
            logger.warning(f"Slow query detected: {execution_time:.2f}s - {statement[:100]}...")
        
        # Update pool manager statistics
        from .optimization import pool_manager
        if hasattr(pool_manager, 'record_query_time'):
            query_type = "dashboard" if "dashboard" in statement.lower() else "other"
            pool_manager.record_query_time(execution_time, query_type)
