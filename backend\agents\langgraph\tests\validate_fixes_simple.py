#!/usr/bin/env python3
"""
Simple Production Fixes Validation Script.

This script validates the key production-ready fixes without complex imports.
"""

import sys
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


def test_files_exist():
    """Test that all new files were created."""
    logger.info("🧪 Testing file creation...")
    
    base_path = os.path.join(os.path.dirname(__file__), '..')
    
    required_files = [
        'core/routing_constants.py',
        'core/routing_utils.py',
        'core/__init__.py'
    ]
    
    for file_path in required_files:
        full_path = os.path.join(base_path, file_path)
        if not os.path.exists(full_path):
            logger.error(f"❌ Missing file: {file_path}")
            return False
        logger.info(f"✅ Found file: {file_path}")
    
    logger.info("✅ All required files exist")
    return True


def test_constants_defined():
    """Test that routing constants are properly defined."""
    logger.info("🧪 Testing constants definition...")
    
    try:
        # Read the constants file
        constants_path = os.path.join(os.path.dirname(__file__), '..', 'core', 'routing_constants.py')
        with open(constants_path, 'r') as f:
            content = f.read()
        
        # Check for key constants
        required_constants = [
            'AGENT_PREFIX = "agent_"',
            'CONCIERGE_AGENT_PATTERN = "concierge"',
            'ROUTING_FALLBACK = "routing"',
            'class RoutingConstants:',
            'def format_agent_node_name(',
            'def is_concierge_agent('
        ]
        
        for constant in required_constants:
            if constant not in content:
                logger.error(f"❌ Missing constant: {constant}")
                return False
            logger.info(f"✅ Found constant: {constant}")
        
        logger.info("✅ All constants properly defined")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error reading constants file: {e}")
        return False


def test_routing_utils_structure():
    """Test that routing utils have proper structure."""
    logger.info("🧪 Testing routing utils structure...")
    
    try:
        # Read the routing utils file
        utils_path = os.path.join(os.path.dirname(__file__), '..', 'core', 'routing_utils.py')
        with open(utils_path, 'r') as f:
            content = f.read()
        
        # Check for key classes and functions
        required_elements = [
            'class RoutingValidator:',
            'class FallbackAgentSelector:',
            'class RoutingDecisionMaker:',
            'def create_routing_decision_maker(',
            'def validate_state_parameter(',
            'def determine_entry_point(',
            'def determine_tool_routing_target('
        ]
        
        for element in required_elements:
            if element not in content:
                logger.error(f"❌ Missing element: {element}")
                return False
            logger.info(f"✅ Found element: {element}")
        
        logger.info("✅ Routing utils properly structured")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error reading routing utils file: {e}")
        return False


def test_workflow_manager_updates():
    """Test that workflow manager was properly updated."""
    logger.info("🧪 Testing workflow manager updates...")
    
    try:
        # Read the workflow manager file
        manager_path = os.path.join(os.path.dirname(__file__), '..', 'core', 'workflow_manager.py')
        with open(manager_path, 'r') as f:
            content = f.read()
        
        # Check for key updates
        required_updates = [
            'from .routing_constants import',
            'from .routing_utils import',
            'self.routing_decision_maker',
            'create_routing_decision_maker',
            'RoutingConstants.ROUTING_FALLBACK',
            'ValidationError',
            'AgentNotFoundError'
        ]
        
        for update in required_updates:
            if update not in content:
                logger.error(f"❌ Missing update: {update}")
                return False
            logger.info(f"✅ Found update: {update}")
        
        logger.info("✅ Workflow manager properly updated")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error reading workflow manager file: {e}")
        return False


def test_unified_state_updates():
    """Test that unified state was properly updated."""
    logger.info("🧪 Testing unified state updates...")
    
    try:
        # Read the unified state file
        state_path = os.path.join(os.path.dirname(__file__), '..', 'states', 'unified_state.py')
        with open(state_path, 'r') as f:
            content = f.read()
        
        # Check for key updates
        required_updates = [
            'import logging',
            'logger = logging.getLogger(__name__)',
            'def get_routing_target(state: UnifiedDatageniusState) -> Optional[str]:'
        ]
        
        # Check that inline import was removed
        if 'import logging' in content and 'logger = logging.getLogger(__name__)' in content:
            # Check that there's no inline import in the function
            lines = content.split('\n')
            in_function = False
            for line in lines:
                if 'def get_routing_target(' in line:
                    in_function = True
                elif in_function and line.strip().startswith('def '):
                    in_function = False
                elif in_function and 'import logging' in line and 'logger = logging.getLogger(__name__)' in line:
                    logger.error("❌ Still has inline logging import")
                    return False
        
        for update in required_updates:
            if update not in content:
                logger.error(f"❌ Missing update: {update}")
                return False
            logger.info(f"✅ Found update: {update}")
        
        logger.info("✅ Unified state properly updated")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error reading unified state file: {e}")
        return False


def test_error_handling_patterns():
    """Test that error handling patterns are implemented."""
    logger.info("🧪 Testing error handling patterns...")
    
    try:
        # Read the workflow manager file
        manager_path = os.path.join(os.path.dirname(__file__), '..', 'core', 'workflow_manager.py')
        with open(manager_path, 'r') as f:
            content = f.read()
        
        # Check for error handling patterns
        error_patterns = [
            'try:',
            'except ValidationError',
            'except AgentNotFoundError',
            'except Exception as e:',
            'self.logger.error('
        ]
        
        for pattern in error_patterns:
            if pattern not in content:
                logger.error(f"❌ Missing error pattern: {pattern}")
                return False
            logger.info(f"✅ Found error pattern: {pattern}")
        
        logger.info("✅ Error handling patterns implemented")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error checking error handling patterns: {e}")
        return False


def main():
    """Run all simple validation tests."""
    logger.info("🚀 Starting simple production fixes validation...")
    
    tests = [
        test_files_exist,
        test_constants_defined,
        test_routing_utils_structure,
        test_workflow_manager_updates,
        test_unified_state_updates,
        test_error_handling_patterns
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
                logger.error(f"❌ Test failed: {test.__name__}")
        except Exception as e:
            failed += 1
            logger.error(f"❌ Test error in {test.__name__}: {e}")
    
    logger.info(f"📊 Validation Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        logger.info("🎉 All production fixes validated successfully!")
        logger.info("✅ Code structure is ready for production deployment")
        return True
    else:
        logger.error("💥 Some validation tests failed!")
        logger.error("❌ Additional fixes needed before production")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
