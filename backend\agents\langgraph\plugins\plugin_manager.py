"""
Agent Plugin Manager for LangGraph-based Datagenius System.

This module provides comprehensive plugin management capabilities including:
- Third-party agent plugin registration and validation
- Security and capability validation
- Plugin marketplace integration
- Dynamic plugin loading and management
"""

import logging
import asyncio
import importlib
import inspect
from typing import Dict, Any, List, Optional, Type, Set
from datetime import datetime
from dataclasses import dataclass, field
from pathlib import Path
from abc import ABC, abstractmethod
import hashlib
import json

from ..nodes.base_agent_node import BaseAgentNode

logger = logging.getLogger(__name__)


@dataclass
class PluginInfo:
    """Plugin metadata and information."""
    name: str
    version: str
    author: str
    description: str
    supported_industries: List[str]
    required_tools: List[str]
    marketplace_category: Optional[str] = None
    price: Optional[float] = None
    subscription_model: Optional[str] = None
    compliance_certifications: List[str] = field(default_factory=list)
    plugin_id: Optional[str] = None


@dataclass
class SecurityValidationResult:
    """Result of security validation."""
    is_safe: bool
    security_score: float
    issues: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)


@dataclass
class CapabilityValidationResult:
    """Result of capability validation."""
    is_compatible: bool
    compatibility_score: float
    issues: List[str] = field(default_factory=list)
    missing_dependencies: List[str] = field(default_factory=list)
    supported_features: List[str] = field(default_factory=list)


@dataclass
class PluginPackage:
    """Complete plugin package with code and metadata."""
    plugin_info: PluginInfo
    plugin_class: Type
    source_code: str
    dependencies: List[str] = field(default_factory=list)
    marketplace_listing: Optional[Dict[str, Any]] = None
    installation_config: Dict[str, Any] = field(default_factory=dict)


class DatageniusAgentPlugin(ABC):
    """
    Base class for creating custom Datagenius agent plugins.
    
    All third-party agent plugins must inherit from this class and implement
    the required abstract methods to ensure compatibility with the Datagenius
    platform.
    """
    
    @abstractmethod
    def get_plugin_info(self) -> PluginInfo:
        """Return plugin metadata and capabilities."""
        pass
    
    @abstractmethod
    async def create_agent(self, config: Dict[str, Any]) -> BaseAgentNode:
        """Create agent instance from configuration."""
        pass
    
    @abstractmethod
    def get_required_tools(self) -> List[str]:
        """Return list of required MCP tools."""
        pass
    
    @abstractmethod
    def get_supported_industries(self) -> List[str]:
        """Return list of supported industry verticals."""
        pass
    
    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """Validate plugin configuration (optional override)."""
        return True
    
    def get_security_requirements(self) -> Dict[str, Any]:
        """Get plugin security requirements (optional override)."""
        return {
            "sandbox_execution": False,
            "network_isolation": False,
            "data_encryption": False,
            "audit_logging": False
        }


class SecurityValidator:
    """Validates plugin security and safety."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.security_rules = self._load_security_rules()
    
    async def validate(self, plugin_package: PluginPackage) -> SecurityValidationResult:
        """Validate plugin security."""
        try:
            issues = []
            warnings = []
            recommendations = []
            security_score = 100.0
            
            # Check for dangerous imports
            dangerous_imports = self._check_dangerous_imports(plugin_package.source_code)
            if dangerous_imports:
                issues.extend(dangerous_imports)
                security_score -= len(dangerous_imports) * 10
            
            # Check for file system access
            file_access_issues = self._check_file_access(plugin_package.source_code)
            if file_access_issues:
                warnings.extend(file_access_issues)
                security_score -= len(file_access_issues) * 5
            
            # Check for network access
            network_issues = self._check_network_access(plugin_package.source_code)
            if network_issues:
                warnings.extend(network_issues)
                security_score -= len(network_issues) * 3
            
            # Check for subprocess execution
            subprocess_issues = self._check_subprocess_usage(plugin_package.source_code)
            if subprocess_issues:
                issues.extend(subprocess_issues)
                security_score -= len(subprocess_issues) * 15
            
            # Validate plugin class structure
            class_issues = self._validate_plugin_class(plugin_package.plugin_class)
            if class_issues:
                issues.extend(class_issues)
                security_score -= len(class_issues) * 8
            
            # Generate recommendations
            if security_score < 80:
                recommendations.append("Consider implementing additional security measures")
            if security_score < 60:
                recommendations.append("Plugin requires security review before approval")
            
            is_safe = security_score >= 70 and len(issues) == 0
            
            return SecurityValidationResult(
                is_safe=is_safe,
                security_score=max(0, security_score),
                issues=issues,
                warnings=warnings,
                recommendations=recommendations
            )
            
        except Exception as e:
            self.logger.error(f"Error validating plugin security: {e}")
            return SecurityValidationResult(
                is_safe=False,
                security_score=0,
                issues=[f"Security validation failed: {str(e)}"]
            )
    
    def _load_security_rules(self) -> Dict[str, List[str]]:
        """Load security validation rules."""
        return {
            "dangerous_imports": [
                "os", "sys", "subprocess", "eval", "exec", "compile",
                "importlib", "__import__", "globals", "locals"
            ],
            "dangerous_functions": [
                "eval", "exec", "compile", "open", "file", "input",
                "raw_input", "__import__", "reload"
            ],
            "network_modules": [
                "urllib", "requests", "socket", "http", "ftplib",
                "smtplib", "telnetlib", "xmlrpc"
            ]
        }
    
    def _check_dangerous_imports(self, source_code: str) -> List[str]:
        """Check for dangerous imports in source code."""
        issues = []
        dangerous_imports = self.security_rules["dangerous_imports"]
        
        for dangerous_import in dangerous_imports:
            if f"import {dangerous_import}" in source_code or f"from {dangerous_import}" in source_code:
                issues.append(f"Dangerous import detected: {dangerous_import}")
        
        return issues
    
    def _check_file_access(self, source_code: str) -> List[str]:
        """Check for file system access in source code."""
        warnings = []
        file_functions = ["open(", "file(", "Path(", "pathlib"]
        
        for func in file_functions:
            if func in source_code:
                warnings.append(f"File system access detected: {func}")
        
        return warnings
    
    def _check_network_access(self, source_code: str) -> List[str]:
        """Check for network access in source code."""
        warnings = []
        network_modules = self.security_rules["network_modules"]
        
        for module in network_modules:
            if module in source_code:
                warnings.append(f"Network access detected: {module}")
        
        return warnings
    
    def _check_subprocess_usage(self, source_code: str) -> List[str]:
        """Check for subprocess usage in source code."""
        issues = []
        subprocess_patterns = ["subprocess.", "os.system", "os.popen", "commands."]
        
        for pattern in subprocess_patterns:
            if pattern in source_code:
                issues.append(f"Subprocess execution detected: {pattern}")
        
        return issues
    
    def _validate_plugin_class(self, plugin_class: Type) -> List[str]:
        """Validate plugin class structure."""
        issues = []
        
        # Check if class inherits from DatageniusAgentPlugin
        if not issubclass(plugin_class, DatageniusAgentPlugin):
            issues.append("Plugin class must inherit from DatageniusAgentPlugin")
        
        # Check required methods
        required_methods = ["get_plugin_info", "create_agent", "get_required_tools", "get_supported_industries"]
        for method in required_methods:
            if not hasattr(plugin_class, method):
                issues.append(f"Missing required method: {method}")
        
        return issues


class CapabilityValidator:
    """Validates plugin capabilities and compatibility."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def validate(self, plugin_package: PluginPackage) -> CapabilityValidationResult:
        """Validate plugin capabilities."""
        try:
            issues = []
            missing_dependencies = []
            supported_features = []
            compatibility_score = 100.0
            
            # Check required tools availability
            required_tools = plugin_package.plugin_info.required_tools
            available_tools = await self._get_available_tools()
            
            for tool in required_tools:
                if tool not in available_tools:
                    missing_dependencies.append(f"MCP tool: {tool}")
                    compatibility_score -= 10
                else:
                    supported_features.append(f"MCP tool: {tool}")
            
            # Check industry support
            supported_industries = plugin_package.plugin_info.supported_industries
            available_industries = await self._get_available_industries()
            
            for industry in supported_industries:
                if industry in available_industries:
                    supported_features.append(f"Industry: {industry}")
                else:
                    issues.append(f"Unsupported industry: {industry}")
                    compatibility_score -= 5
            
            # Check dependencies
            for dependency in plugin_package.dependencies:
                if not await self._check_dependency_available(dependency):
                    missing_dependencies.append(f"Python package: {dependency}")
                    compatibility_score -= 8
            
            # Validate plugin info
            if not plugin_package.plugin_info.name:
                issues.append("Plugin name is required")
                compatibility_score -= 5
            
            if not plugin_package.plugin_info.version:
                issues.append("Plugin version is required")
                compatibility_score -= 5
            
            is_compatible = compatibility_score >= 80 and len(missing_dependencies) == 0
            
            return CapabilityValidationResult(
                is_compatible=is_compatible,
                compatibility_score=max(0, compatibility_score),
                issues=issues,
                missing_dependencies=missing_dependencies,
                supported_features=supported_features
            )
            
        except Exception as e:
            self.logger.error(f"Error validating plugin capabilities: {e}")
            return CapabilityValidationResult(
                is_compatible=False,
                compatibility_score=0,
                issues=[f"Capability validation failed: {str(e)}"]
            )
    
    async def _get_available_tools(self) -> List[str]:
        """Get list of available MCP tools."""
        # Implementation would query the MCP tool registry
        return [
            "data_access", "visualization", "statistical_analysis",
            "text_processing", "image_processing", "web_scraping",
            "email_sender", "file_processor", "database_query"
        ]
    
    async def _get_available_industries(self) -> List[str]:
        """Get list of available industry specializations."""
        return [
            "healthcare", "finance", "retail", "manufacturing",
            "education", "technology", "legal", "government"
        ]
    
    async def _check_dependency_available(self, dependency: str) -> bool:
        """Check if Python dependency is available."""
        try:
            importlib.import_module(dependency)
            return True
        except ImportError:
            return False


class PluginRegistry:
    """Registry for managing installed plugins."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.plugins: Dict[str, PluginPackage] = {}
        self.plugin_instances: Dict[str, DatageniusAgentPlugin] = {}
    
    async def register(self, plugin_package: PluginPackage) -> str:
        """Register a new plugin."""
        try:
            # Generate plugin ID
            plugin_id = self._generate_plugin_id(plugin_package)
            plugin_package.plugin_info.plugin_id = plugin_id
            
            # Store plugin
            self.plugins[plugin_id] = plugin_package
            
            # Create plugin instance
            plugin_instance = plugin_package.plugin_class()
            self.plugin_instances[plugin_id] = plugin_instance
            
            self.logger.info(f"Registered plugin: {plugin_id}")
            return plugin_id
            
        except Exception as e:
            self.logger.error(f"Error registering plugin: {e}")
            raise
    
    async def get_plugin(self, plugin_id: str) -> Optional[DatageniusAgentPlugin]:
        """Get plugin instance by ID."""
        return self.plugin_instances.get(plugin_id)
    
    async def unregister(self, plugin_id: str) -> bool:
        """Unregister a plugin."""
        try:
            if plugin_id in self.plugins:
                del self.plugins[plugin_id]
            if plugin_id in self.plugin_instances:
                del self.plugin_instances[plugin_id]
            
            self.logger.info(f"Unregistered plugin: {plugin_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error unregistering plugin {plugin_id}: {e}")
            return False
    
    def _generate_plugin_id(self, plugin_package: PluginPackage) -> str:
        """Generate unique plugin ID."""
        plugin_info = plugin_package.plugin_info
        id_string = f"{plugin_info.name}_{plugin_info.version}_{plugin_info.author}"
        return hashlib.md5(id_string.encode()).hexdigest()[:16]


class AgentPluginManager:
    """
    Manages third-party agent plugins and custom integrations.
    
    Provides comprehensive plugin management including:
    - Plugin registration and validation
    - Security and capability assessment
    - Marketplace integration
    - Dynamic plugin loading
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.plugin_registry = PluginRegistry()
        self.security_validator = SecurityValidator()
        self.capability_validator = CapabilityValidator()
        
        # Marketplace integration (would be implemented)
        self.marketplace_client = None

    async def initialize(self) -> None:
        """Initialize the agent plugin manager."""
        try:
            self.logger.info("Initializing AgentPluginManager...")

            # Initialize plugin registry
            if hasattr(self.plugin_registry, 'initialize'):
                await self.plugin_registry.initialize()

            # Initialize validators
            if hasattr(self.security_validator, 'initialize'):
                await self.security_validator.initialize()

            if hasattr(self.capability_validator, 'initialize'):
                await self.capability_validator.initialize()

            self.logger.info("AgentPluginManager initialized successfully")
        except Exception as e:
            self.logger.error(f"Error initializing AgentPluginManager: {e}")
            raise

    async def register_plugin(self, plugin_package: PluginPackage) -> str:
        """Register a new third-party agent plugin."""
        try:
            self.logger.info(f"Registering plugin: {plugin_package.plugin_info.name}")
            
            # Validate plugin security
            security_result = await self.security_validator.validate(plugin_package)
            if not security_result.is_safe:
                raise SecurityValidationError(f"Security validation failed: {security_result.issues}")
            
            # Validate plugin capabilities
            capability_result = await self.capability_validator.validate(plugin_package)
            if not capability_result.is_compatible:
                raise CapabilityValidationError(f"Capability validation failed: {capability_result.issues}")
            
            # Register plugin
            plugin_id = await self.plugin_registry.register(plugin_package)
            
            # Add to marketplace if approved
            if plugin_package.marketplace_listing and self.marketplace_client:
                await self.marketplace_client.submit_listing(plugin_package)
            
            self.logger.info(f"Successfully registered plugin: {plugin_id}")
            return plugin_id
            
        except Exception as e:
            self.logger.error(f"Error registering plugin: {e}")
            raise
    
    async def load_plugin_agent(self, plugin_id: str, config: Dict[str, Any]) -> BaseAgentNode:
        """Load agent from registered plugin."""
        try:
            plugin = await self.plugin_registry.get_plugin(plugin_id)
            if not plugin:
                raise ValueError(f"Plugin not found: {plugin_id}")
            
            # Validate configuration
            if not plugin.validate_configuration(config):
                raise ValueError("Invalid plugin configuration")
            
            # Create agent instance
            agent_node = await plugin.create_agent(config)
            
            self.logger.info(f"Successfully loaded agent from plugin: {plugin_id}")
            return agent_node
            
        except Exception as e:
            self.logger.error(f"Error loading plugin agent {plugin_id}: {e}")
            raise
    
    async def get_available_plugins(self) -> List[PluginInfo]:
        """Get list of available plugins."""
        plugins = []
        for plugin_package in self.plugin_registry.plugins.values():
            plugins.append(plugin_package.plugin_info)
        return plugins


class SecurityValidationError(Exception):
    """Exception raised when plugin security validation fails."""
    pass


class CapabilityValidationError(Exception):
    """Exception raised when plugin capability validation fails."""
    pass


# Global instance
agent_plugin_manager = AgentPluginManager()
