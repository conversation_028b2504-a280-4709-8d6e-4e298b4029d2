# MCP Tools Configuration for Extensible Persona System
# This configuration defines tool mappings and capabilities without hardcoded values

# Global MCP tool settings
global_settings:
  enable_mcp_integration: true
  enable_auto_discovery: true
  enable_third_party_tools: true
  tool_execution_timeout: 30
  max_concurrent_tools: 5

# Dynamic persona-tool mappings (loaded from configuration)
persona_tool_mappings:
  # Analysis personas
  analysis:
    - "data_analyzer"
    - "chart_generator"
    - "report_generator"
    - "pandasai_query"
    - "statistical_analyzer"
    - "trend_detector"
    - "data_cleaner"
    - "visualization_engine"
  
  # Marketing personas
  marketing:
    - "content_generator"
    - "social_media_poster"
    - "campaign_analyzer"
    - "brand_strategy_generator"
    - "audience_analyzer"
    - "performance_tracker"
    - "competitor_analyzer"
    - "email_marketing_generator"
  
  # Concierge personas
  concierge:
    - "persona_recommender"
    - "data_attachment_assistant"
    - "context_manager"
    - "conversation_state_manager"
    - "intent_analyzer"
    - "workflow_coordinator"
  
  # Classification personas
  classification:
    - "text_classifier"
    - "data_categorizer"
    - "sentiment_analyzer"
    - "entity_extractor"
    - "topic_modeler"
    - "document_classifier"
    - "content_organizer"

# Tool capabilities mapping (for auto-discovery)
tool_capabilities:
  # Data analysis capabilities
  data_analysis:
    - "data_analyzer"
    - "statistical_analyzer"
    - "trend_detector"
    - "data_cleaner"
  
  # Visualization capabilities
  visualization:
    - "chart_generator"
    - "visualization_engine"
    - "dashboard_generator"
  
  # Content generation capabilities
  content_generation:
    - "content_generator"
    - "email_marketing_generator"
    - "social_media_poster"
  
  # Classification capabilities
  text_classification:
    - "text_classifier"
    - "sentiment_analyzer"
    - "entity_extractor"
    - "topic_modeler"
  
  # Conversation management capabilities
  conversation_management:
    - "persona_recommender"
    - "context_manager"
    - "conversation_state_manager"
    - "intent_analyzer"

# Tool configuration details
tool_configurations:
  data_analyzer:
    description: "Analyzes data and generates insights"
    input_types: ["csv", "json", "excel"]
    output_types: ["json", "report"]
    capabilities: ["data_analysis", "statistical_analysis"]
    timeout: 60
    
  chart_generator:
    description: "Creates charts and visualizations"
    input_types: ["data", "json"]
    output_types: ["image", "html", "json"]
    capabilities: ["visualization", "chart_creation"]
    timeout: 30
    
  content_generator:
    description: "Generates marketing content"
    input_types: ["text", "json"]
    output_types: ["text", "html"]
    capabilities: ["content_generation", "marketing"]
    timeout: 45
    
  text_classifier:
    description: "Classifies text into categories"
    input_types: ["text"]
    output_types: ["json"]
    capabilities: ["text_classification", "categorization"]
    timeout: 20
    
  persona_recommender:
    description: "Recommends appropriate personas"
    input_types: ["text", "context"]
    output_types: ["json"]
    capabilities: ["conversation_management", "routing"]
    timeout: 15

# Third-party tool integration
third_party_tools:
  enable_custom_tools: true
  custom_tool_directories:
    - "custom_tools"
    - "plugins/tools"
  
  # External tool APIs
  external_apis:
    openai_tools:
      enabled: false
      api_key_env: "OPENAI_API_KEY"
      tools: ["gpt_analyzer", "dalle_generator"]
    
    anthropic_tools:
      enabled: false
      api_key_env: "ANTHROPIC_API_KEY"
      tools: ["claude_analyzer"]
    
    google_tools:
      enabled: false
      api_key_env: "GOOGLE_API_KEY"
      tools: ["gemini_analyzer"]

# Tool execution settings
execution_settings:
  # Parallel execution
  parallel_execution:
    enable_parallel: true
    max_parallel_tools: 3
    parallel_timeout: 60
  
  # Caching
  caching:
    enable_tool_caching: true
    cache_duration_seconds: 1800
    cache_size_limit: 1000
  
  # Error handling
  error_handling:
    max_retries: 3
    retry_delay_seconds: 2
    fallback_tools: true
  
  # Monitoring
  monitoring:
    enable_performance_tracking: true
    enable_usage_analytics: true
    log_tool_executions: true

# Tool discovery rules
discovery_rules:
  # Auto-discovery patterns
  auto_discovery:
    scan_directories:
      - "agents/tools/mcp"
      - "custom_tools"
    
    file_patterns:
      - "*.py"
      - "tool_*.py"
      - "*_tool.py"
    
    class_patterns:
      - "*Tool"
      - "*MCPTool"
      - "MCP*"
  
  # Capability inference
  capability_inference:
    enable_auto_inference: true
    inference_rules:
      - pattern: "data.*"
        capabilities: ["data_analysis"]
      - pattern: "chart.*|viz.*|visual.*"
        capabilities: ["visualization"]
      - pattern: "content.*|marketing.*"
        capabilities: ["content_generation"]
      - pattern: "classify.*|categorize.*"
        capabilities: ["text_classification"]

# Integration with existing systems
system_integration:
  # Business profile integration
  business_profile:
    enable_context_injection: true
    context_fields:
      - "industry"
      - "company_size"
      - "business_goals"
    
    tool_customization:
      enable_industry_tools: true
      enable_size_specific_tools: true
  
  # Database integration
  database:
    enable_tool_persistence: true
    store_execution_history: true
    store_performance_metrics: true
  
  # API integration
  api:
    enable_tool_endpoints: true
    endpoint_prefix: "/api/tools"
    authentication_required: true

# Validation and security
validation:
  # Tool validation
  tool_validation:
    validate_inputs: true
    validate_outputs: true
    sanitize_inputs: true
  
  # Security settings
  security:
    enable_sandboxing: true
    restrict_file_access: true
    limit_network_access: true
    audit_tool_usage: true

# Performance optimization
performance:
  # Resource limits
  resource_limits:
    max_memory_mb: 512
    max_cpu_percent: 50
    max_execution_time: 300
  
  # Optimization strategies
  optimization:
    enable_lazy_loading: true
    enable_tool_pooling: true
    enable_result_streaming: true
