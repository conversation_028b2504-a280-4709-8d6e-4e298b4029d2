#!/usr/bin/env python3
"""
Database Persona ID Standardization Script

This script standardizes persona IDs in the database to resolve frontend/backend inconsistencies.
Specifically updates 'concierge-agent' to 'concierge' and ensures data consistency.

Usage: python scripts/standardize_persona_ids.py
"""

import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.orm import Session
from app.database import get_db, engine, Persona, PurchasedItem
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Persona ID mappings for standardization
PERSONA_ID_MAPPINGS = {
    'concierge-agent': 'concierge',
    'composable-analysis-ai': 'analysis',
    'composable-marketing-ai': 'marketing',
    'composable-classifier-ai': 'classification'
}

def get_database_session() -> Session:
    """Get a database session."""
    db_gen = get_db()
    return next(db_gen)

def backup_data(db: Session) -> dict:
    """Create a backup of current data before making changes."""
    logger.info("Creating data backup...")
    
    backup = {
        'personas': [],
        'purchased_items': []
    }
    
    # Backup personas
    personas = db.query(Persona).all()
    for persona in personas:
        backup['personas'].append({
            'id': persona.id,
            'name': persona.name,
            'is_active': persona.is_active,
            'price': persona.price
        })
    
    # Backup purchased items
    purchased_items = db.query(PurchasedItem).all()
    for item in purchased_items:
        backup['purchased_items'].append({
            'id': item.id,
            'persona_id': item.persona_id,
            'purchase_id': item.purchase_id,
            'quantity': item.quantity,
            'price': item.price
        })
    
    logger.info(f"Backup created: {len(backup['personas'])} personas, {len(backup['purchased_items'])} purchased items")
    return backup

def update_persona_ids(db: Session) -> dict:
    """Update persona IDs according to the standardization mappings."""
    logger.info("Starting persona ID standardization...")
    
    results = {
        'personas_updated': 0,
        'purchased_items_updated': 0,
        'errors': []
    }
    
    try:
        # Update personas table
        for old_id, new_id in PERSONA_ID_MAPPINGS.items():
            logger.info(f"Updating persona ID: {old_id} -> {new_id}")
            
            # Check if persona with old ID exists
            persona = db.query(Persona).filter(Persona.id == old_id).first()
            if persona:
                # Check if new ID already exists
                existing_persona = db.query(Persona).filter(Persona.id == new_id).first()
                if existing_persona:
                    logger.warning(f"Persona with new ID '{new_id}' already exists. Merging data...")
                    # Update the existing persona with any missing data
                    if not existing_persona.is_active and persona.is_active:
                        existing_persona.is_active = persona.is_active
                    # Delete the old persona
                    db.delete(persona)
                else:
                    # Simply update the ID
                    persona.id = new_id
                
                results['personas_updated'] += 1
                logger.info(f"Updated persona: {old_id} -> {new_id}")
        
        # Update purchased_items table
        for old_id, new_id in PERSONA_ID_MAPPINGS.items():
            logger.info(f"Updating purchased items for persona: {old_id} -> {new_id}")
            
            purchased_items = db.query(PurchasedItem).filter(PurchasedItem.persona_id == old_id).all()
            for item in purchased_items:
                item.persona_id = new_id
                results['purchased_items_updated'] += 1
                logger.info(f"Updated purchased item {item.id}: {old_id} -> {new_id}")
        
        # Ensure concierge persona is active and properly configured
        concierge_persona = db.query(Persona).filter(Persona.id == 'concierge').first()
        if concierge_persona:
            concierge_persona.is_active = True
            if concierge_persona.price is None or concierge_persona.price > 0:
                concierge_persona.price = 0.0  # Make concierge free
            logger.info("Ensured concierge persona is active and free")
        
        # Commit all changes
        db.commit()
        logger.info("All changes committed successfully")
        
    except Exception as e:
        logger.error(f"Error during update: {e}")
        db.rollback()
        results['errors'].append(str(e))
        raise
    
    return results

def verify_updates(db: Session) -> dict:
    """Verify that the updates were applied correctly."""
    logger.info("Verifying updates...")
    
    verification = {
        'personas': [],
        'purchased_items_by_persona': {},
        'concierge_status': {}
    }
    
    # Check personas
    personas = db.query(Persona).all()
    for persona in personas:
        verification['personas'].append({
            'id': persona.id,
            'name': persona.name,
            'is_active': persona.is_active,
            'price': persona.price
        })
    
    # Check purchased items grouped by persona
    purchased_items = db.query(PurchasedItem).all()
    for item in purchased_items:
        if item.persona_id not in verification['purchased_items_by_persona']:
            verification['purchased_items_by_persona'][item.persona_id] = 0
        verification['purchased_items_by_persona'][item.persona_id] += 1
    
    # Check concierge specifically
    concierge = db.query(Persona).filter(Persona.id == 'concierge').first()
    if concierge:
        verification['concierge_status'] = {
            'exists': True,
            'is_active': concierge.is_active,
            'price': concierge.price,
            'name': concierge.name
        }
    else:
        verification['concierge_status'] = {'exists': False}
    
    return verification

def main():
    """Main function to run the standardization process."""
    logger.info("Starting persona ID standardization process...")
    
    db = None
    try:
        # Get database session
        db = get_database_session()
        
        # Create backup
        backup = backup_data(db)
        logger.info("Data backup completed")
        
        # Perform updates
        results = update_persona_ids(db)
        
        # Verify updates
        verification = verify_updates(db)
        
        # Print results
        logger.info("=== STANDARDIZATION RESULTS ===")
        logger.info(f"Personas updated: {results['personas_updated']}")
        logger.info(f"Purchased items updated: {results['purchased_items_updated']}")
        
        if results['errors']:
            logger.error(f"Errors encountered: {results['errors']}")
        
        logger.info("=== VERIFICATION RESULTS ===")
        logger.info("Current personas in database:")
        for persona in verification['personas']:
            logger.info(f"  - ID: {persona['id']}, Name: {persona['name']}, Active: {persona['is_active']}, Price: {persona['price']}")
        
        logger.info("Purchased items by persona:")
        for persona_id, count in verification['purchased_items_by_persona'].items():
            logger.info(f"  - {persona_id}: {count} items")
        
        logger.info("Concierge status:")
        concierge_status = verification['concierge_status']
        if concierge_status['exists']:
            logger.info(f"  - Exists: Yes")
            logger.info(f"  - Active: {concierge_status['is_active']}")
            logger.info(f"  - Price: {concierge_status['price']}")
            logger.info(f"  - Name: {concierge_status['name']}")
        else:
            logger.info("  - Exists: No")
        
        logger.info("Persona ID standardization completed successfully!")
        
    except Exception as e:
        logger.error(f"Standardization failed: {e}")
        if db:
            db.rollback()
        sys.exit(1)
    
    finally:
        if db:
            db.close()

if __name__ == "__main__":
    main()
