#!/usr/bin/env python3
"""
Phase 2 Test Runner

This script runs all Phase 2 tests and provides a summary of the implementation status.
It can be used to validate that all Phase 2 features are working correctly.
"""

import sys
import asyncio
import logging
from pathlib import Path
from typing import Dict, Any, List

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(backend_dir))

from agents.langgraph.nodes.routing_node import AgentSwitchNode, RoutingNode
from agents.langgraph.nodes.base_agent_node import BaseAgentNode
from agents.langgraph.core.workflow_manager import WorkflowManager
from agents.langgraph.states.unified_state import (
    create_unified_state,
    ConversationMode,
    set_selected_agent
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class Phase2TestRunner:
    """Test runner for Phase 2 features."""
    
    def __init__(self):
        self.test_results = []
        self.mock_agents = {
            "concierge-agent": self._create_mock_agent("concierge-agent", "concierge"),
            "analysis-agent": self._create_mock_agent("analysis-agent", "analysis"),
            "marketing-agent": self._create_mock_agent("marketing-agent", "marketing")
        }
    
    def _create_mock_agent(self, agent_id: str, agent_type: str):
        """Create a mock agent for testing."""
        class MockAgent(BaseAgentNode):
            async def _process_message(self, state):
                return state
        
        return MockAgent(agent_id, agent_type, {})
    
    def _log_test_result(self, test_name: str, success: bool, message: str = ""):
        """Log test result."""
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status}: {test_name}")
        if message:
            logger.info(f"    {message}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message
        })
    
    async def test_agent_switch_node_creation(self):
        """Test AgentSwitchNode can be created."""
        try:
            agent_switch_node = AgentSwitchNode(self.mock_agents)
            self._log_test_result(
                "AgentSwitchNode Creation",
                True,
                f"Created with {len(self.mock_agents)} agents"
            )
            return agent_switch_node
        except Exception as e:
            self._log_test_result("AgentSwitchNode Creation", False, str(e))
            return None
    
    async def test_explicit_switch_detection(self, agent_switch_node):
        """Test explicit switch detection."""
        if not agent_switch_node:
            self._log_test_result("Explicit Switch Detection", False, "AgentSwitchNode not available")
            return
        
        try:
            test_cases = [
                ("switch to analysis", True),
                ("talk to marketing", True),
                ("Hello, how are you?", False),
                ("I need help with data", False)
            ]
            
            all_passed = True
            for message, expected in test_cases:
                result = agent_switch_node._is_explicit_switch_request(message)
                if result != expected:
                    all_passed = False
                    break
            
            self._log_test_result(
                "Explicit Switch Detection",
                all_passed,
                f"Tested {len(test_cases)} cases"
            )
        except Exception as e:
            self._log_test_result("Explicit Switch Detection", False, str(e))
    
    async def test_target_agent_extraction(self, agent_switch_node):
        """Test target agent extraction."""
        if not agent_switch_node:
            self._log_test_result("Target Agent Extraction", False, "AgentSwitchNode not available")
            return
        
        try:
            test_cases = [
                ("switch to analysis", "analysis-agent"),
                ("talk to marketing", "marketing-agent"),
                ("connect me with concierge", "concierge-agent"),
                ("switch to nonexistent", None)
            ]
            
            all_passed = True
            for message, expected in test_cases:
                result = agent_switch_node._extract_target_agent(message)
                if result != expected:
                    all_passed = False
                    break
            
            self._log_test_result(
                "Target Agent Extraction",
                all_passed,
                f"Tested {len(test_cases)} cases"
            )
        except Exception as e:
            self._log_test_result("Target Agent Extraction", False, str(e))
    
    async def test_agent_coordination(self):
        """Test agent coordination features."""
        try:
            agent = self.mock_agents["concierge-agent"]
            state = create_unified_state("test_user", "test_conv", "default")
            state["available_agents"] = list(self.mock_agents.keys())
            
            # Test specialist coordination
            result = await agent._coordinate_with_specialist(
                state, "analysis-agent", {"test": "context"}
            )
            
            coordination_success = (
                result["success"] is True and
                result["specialist_agent"] == "analysis-agent" and
                len(result["insights"]) > 0
            )
            
            self._log_test_result(
                "Agent Coordination",
                coordination_success,
                f"Coordination result: {result['success']}"
            )
        except Exception as e:
            self._log_test_result("Agent Coordination", False, str(e))
    
    async def test_handoff_suggestion(self):
        """Test handoff suggestion functionality."""
        try:
            agent = self.mock_agents["concierge-agent"]
            state = create_unified_state("test_user", "test_conv", "default")
            state["available_agents"] = list(self.mock_agents.keys())
            
            # Test handoff suggestion
            result_state = await agent._suggest_handoff(
                state, "analysis-agent", "better for analysis", 0.8
            )
            
            suggestion_success = (
                "pending_handoff_suggestion" in result_state and
                result_state["pending_handoff_suggestion"]["target_agent"] == "analysis-agent"
            )
            
            self._log_test_result(
                "Handoff Suggestion",
                suggestion_success,
                "Handoff suggestion created successfully"
            )
        except Exception as e:
            self._log_test_result("Handoff Suggestion", False, str(e))
    
    async def test_workflow_initialization(self):
        """Test enhanced workflow initialization."""
        try:
            # Mock WorkflowManager initialization
            class MockWorkflowManager:
                def __init__(self):
                    self.agent_nodes = self.mock_agents
                
                def _determine_initial_agent(self, selected_agent):
                    if selected_agent and selected_agent in self.agent_nodes:
                        return selected_agent
                    return "concierge-agent"
                
                def _initialize_workflow_state(self, user_id, conversation_id, selected_agent=None, context=None):
                    state = create_unified_state(user_id, conversation_id, "default")
                    initial_agent = self._determine_initial_agent(selected_agent)
                    state = set_selected_agent(state, initial_agent, ConversationMode.CONVERSATION)
                    state["available_agents"] = list(self.agent_nodes.keys())
                    state["agent_coordination_log"] = []
                    return state
            
            MockWorkflowManager.mock_agents = self.mock_agents
            manager = MockWorkflowManager()
            
            # Test with selected agent
            state = manager._initialize_workflow_state(
                "test_user", "test_conv", "analysis-agent", {"message": "test"}
            )
            
            init_success = (
                state["selected_agent"] == "analysis-agent" and
                state["conversation_mode"] == ConversationMode.CONVERSATION and
                len(state["available_agents"]) == len(self.mock_agents)
            )
            
            self._log_test_result(
                "Workflow Initialization",
                init_success,
                f"Initialized with agent: {state['selected_agent']}"
            )
        except Exception as e:
            self._log_test_result("Workflow Initialization", False, str(e))
    
    async def test_backward_compatibility(self):
        """Test backward compatibility with RoutingNode."""
        try:
            routing_node = RoutingNode(self.mock_agents)
            
            # Test that RoutingNode extends AgentSwitchNode
            is_instance = isinstance(routing_node, AgentSwitchNode)
            
            # Test legacy methods exist
            has_legacy_methods = (
                hasattr(routing_node, '_detect_intent') and
                hasattr(routing_node, '_analyze_context') and
                hasattr(routing_node, '_score_agents')
            )
            
            compatibility_success = is_instance and has_legacy_methods
            
            self._log_test_result(
                "Backward Compatibility",
                compatibility_success,
                "RoutingNode extends AgentSwitchNode with legacy methods"
            )
        except Exception as e:
            self._log_test_result("Backward Compatibility", False, str(e))
    
    async def run_all_tests(self):
        """Run all Phase 2 tests."""
        logger.info("🚀 Starting Phase 2 Feature Tests")
        logger.info("=" * 50)
        
        # Test 1: AgentSwitchNode Creation
        agent_switch_node = await self.test_agent_switch_node_creation()
        
        # Test 2: Explicit Switch Detection
        await self.test_explicit_switch_detection(agent_switch_node)
        
        # Test 3: Target Agent Extraction
        await self.test_target_agent_extraction(agent_switch_node)
        
        # Test 4: Agent Coordination
        await self.test_agent_coordination()
        
        # Test 5: Handoff Suggestion
        await self.test_handoff_suggestion()
        
        # Test 6: Workflow Initialization
        await self.test_workflow_initialization()
        
        # Test 7: Backward Compatibility
        await self.test_backward_compatibility()
        
        # Summary
        self._print_summary()
    
    def _print_summary(self):
        """Print test summary."""
        logger.info("=" * 50)
        logger.info("📊 Phase 2 Test Summary")
        logger.info("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests} ✅")
        logger.info(f"Failed: {failed_tests} ❌")
        logger.info(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            logger.info("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    logger.info(f"  - {result['test']}: {result['message']}")
        
        logger.info("\n🎉 Phase 2 Implementation Status:")
        if failed_tests == 0:
            logger.info("✅ All Phase 2 features are working correctly!")
        else:
            logger.info(f"⚠️  {failed_tests} issues need to be addressed")


async def main():
    """Main test runner function."""
    runner = Phase2TestRunner()
    await runner.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
