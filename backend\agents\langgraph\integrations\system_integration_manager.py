"""
System Integration Manager for Unified Persona System.

This module provides seamless integration with existing systems including
MCP tools, business profiles, PostgreSQL database, and FastAPI architecture
while maintaining the extensible, configuration-driven approach.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import json

from ..tools.mcp_integration import MCPToolManager
from ..config.dynamic_config_loader import dynamic_config_loader
from ..nodes.unified_persona_node import create_unified_persona_node_from_config
from ..states.unified_state import UnifiedDatageniusState

logger = logging.getLogger(__name__)


class MCPToolIntegrationAdapter:
    """
    Adapter for integrating the unified persona system with MCP tools.
    
    Provides dynamic tool discovery and execution without hardcoded mappings.
    """
    
    def __init__(self):
        """Initialize the MCP tool integration adapter."""
        self.logger = logging.getLogger(f"{__name__}.MCPToolIntegrationAdapter")
        self.mcp_manager = MCPToolManager()
        
        # Dynamic tool mappings loaded from configuration
        self.tool_mappings: Dict[str, List[str]] = {}
        self.tool_capabilities: Dict[str, Dict[str, Any]] = {}
        
        # Initialize tool discovery
        asyncio.create_task(self._initialize_tool_discovery())
    
    async def _initialize_tool_discovery(self) -> None:
        """Initialize dynamic tool discovery from configuration."""
        try:
            # Load tool configuration dynamically
            tool_config = await dynamic_config_loader.load_config("mcp_tools")
            
            if tool_config:
                self.tool_mappings = tool_config.get("persona_tool_mappings", {})
                self.tool_capabilities = tool_config.get("tool_capabilities", {})
                
                self.logger.info("Initialized MCP tool discovery from configuration")
            else:
                # Fallback to auto-discovery
                await self._auto_discover_tools()
                
        except Exception as e:
            self.logger.error(f"Error initializing tool discovery: {e}")
            await self._auto_discover_tools()
    
    async def _auto_discover_tools(self) -> None:
        """Auto-discover available MCP tools."""
        try:
            # Get available tools from MCP manager
            available_tools = await self.mcp_manager.list_available_tools()
            
            # Categorize tools by capability
            for tool_name, tool_info in available_tools.items():
                capabilities = tool_info.get("capabilities", [])
                
                for capability in capabilities:
                    if capability not in self.tool_capabilities:
                        self.tool_capabilities[capability] = []
                    
                    self.tool_capabilities[capability].append(tool_name)
            
            self.logger.info(f"Auto-discovered {len(available_tools)} MCP tools")
            
        except Exception as e:
            self.logger.error(f"Error auto-discovering tools: {e}")
    
    async def get_tools_for_persona(
        self,
        persona_type: str,
        capabilities: Optional[List[str]] = None
    ) -> List[str]:
        """
        Get tools for a persona type based on configuration or capabilities.
        
        Args:
            persona_type: Type of persona
            capabilities: Optional list of required capabilities
            
        Returns:
            List of tool names
        """
        # Check configured mappings first
        if persona_type in self.tool_mappings:
            return self.tool_mappings[persona_type]
        
        # Fallback to capability-based discovery
        if capabilities:
            tools = set()
            for capability in capabilities:
                if capability in self.tool_capabilities:
                    tools.update(self.tool_capabilities[capability])
            return list(tools)
        
        return []
    
    async def execute_tools_for_persona(
        self,
        persona_type: str,
        tools: List[str],
        context: Dict[str, Any],
        state: UnifiedDatageniusState
    ) -> Dict[str, Any]:
        """
        Execute tools for a specific persona with context.
        
        Args:
            persona_type: Type of persona
            tools: List of tools to execute
            context: Execution context
            state: Current workflow state
            
        Returns:
            Tool execution results
        """
        try:
            # Add persona context to execution
            execution_context = context.copy()
            execution_context["persona_type"] = persona_type
            execution_context["execution_timestamp"] = datetime.now().isoformat()
            
            # Execute tools through MCP manager
            results = await self.mcp_manager.execute_tools(tools, execution_context, state)
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error executing tools for {persona_type}: {e}")
            return {"error": str(e), "success": False}


class BusinessProfileIntegrationAdapter:
    """
    Adapter for integrating business profiles with the unified persona system.
    
    Provides dynamic business context injection without hardcoded mappings.
    """
    
    def __init__(self):
        """Initialize the business profile integration adapter."""
        self.logger = logging.getLogger(f"{__name__}.BusinessProfileIntegrationAdapter")
        
        # Configuration for business profile integration
        self.integration_config: Dict[str, Any] = {}
        
        # Initialize integration configuration
        asyncio.create_task(self._load_integration_config())
    
    async def _load_integration_config(self) -> None:
        """Load business profile integration configuration."""
        try:
            config = await dynamic_config_loader.load_config("business_profile_integration")
            
            if config:
                self.integration_config = config
                self.logger.info("Loaded business profile integration configuration")
            else:
                # Default configuration
                self.integration_config = {
                    "enable_auto_injection": True,
                    "enable_industry_context": True,
                    "enable_company_context": True,
                    "context_fields": [
                        "industry",
                        "company_size",
                        "business_goals",
                        "target_audience",
                        "brand_voice"
                    ]
                }
                
        except Exception as e:
            self.logger.error(f"Error loading business profile integration config: {e}")
    
    async def enrich_persona_context(
        self,
        context: Dict[str, Any],
        business_profile: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Enrich persona context with business profile information.
        
        Args:
            context: Base context
            business_profile: Business profile data
            
        Returns:
            Enriched context
        """
        if not business_profile or not self.integration_config.get("enable_auto_injection", True):
            return context
        
        enriched_context = context.copy()
        
        # Add configured business context fields
        context_fields = self.integration_config.get("context_fields", [])
        
        for field in context_fields:
            if field in business_profile:
                enriched_context[field] = business_profile[field]
        
        # Add industry-specific context if enabled
        if self.integration_config.get("enable_industry_context", True):
            industry = business_profile.get("industry")
            if industry:
                enriched_context["industry_context"] = await self._get_industry_context(industry)
        
        # Add company-specific context if enabled
        if self.integration_config.get("enable_company_context", True):
            company_size = business_profile.get("company_size")
            if company_size:
                enriched_context["company_context"] = await self._get_company_context(company_size)
        
        return enriched_context
    
    async def _get_industry_context(self, industry: str) -> Dict[str, Any]:
        """Get industry-specific context."""
        try:
            # Load industry context from configuration
            industry_config = await dynamic_config_loader.load_config(f"industry_{industry}")
            
            if industry_config:
                return industry_config.get("context", {})
            
            # Fallback to basic industry context
            return {
                "industry_name": industry,
                "common_metrics": [],
                "typical_challenges": [],
                "best_practices": []
            }
            
        except Exception as e:
            self.logger.error(f"Error getting industry context for {industry}: {e}")
            return {}
    
    async def _get_company_context(self, company_size: str) -> Dict[str, Any]:
        """Get company size-specific context."""
        try:
            # Load company context from configuration
            company_config = await dynamic_config_loader.load_config(f"company_size_{company_size}")
            
            if company_config:
                return company_config.get("context", {})
            
            # Fallback to basic company context
            return {
                "size_category": company_size,
                "typical_resources": [],
                "common_constraints": [],
                "recommended_approaches": []
            }
            
        except Exception as e:
            self.logger.error(f"Error getting company context for {company_size}: {e}")
            return {}


class DatabaseIntegrationAdapter:
    """
    Adapter for integrating with PostgreSQL database.
    
    Provides dynamic database operations without hardcoded schemas.
    """
    
    def __init__(self):
        """Initialize the database integration adapter."""
        self.logger = logging.getLogger(f"{__name__}.DatabaseIntegrationAdapter")
        
        # Database configuration
        self.db_config: Dict[str, Any] = {}
        
        # Initialize database configuration
        asyncio.create_task(self._load_db_config())
    
    async def _load_db_config(self) -> None:
        """Load database configuration."""
        try:
            config = await dynamic_config_loader.load_config("database_integration")
            
            if config:
                self.db_config = config
                self.logger.info("Loaded database integration configuration")
            else:
                # Default configuration
                self.db_config = {
                    "enable_persona_persistence": True,
                    "enable_conversation_history": True,
                    "enable_performance_tracking": True,
                    "tables": {
                        "personas": "unified_personas",
                        "conversations": "persona_conversations",
                        "metrics": "persona_metrics"
                    }
                }
                
        except Exception as e:
            self.logger.error(f"Error loading database integration config: {e}")
    
    async def save_persona_configuration(
        self,
        persona_id: str,
        configuration: Dict[str, Any]
    ) -> bool:
        """
        Save persona configuration to database.
        
        Args:
            persona_id: Persona identifier
            configuration: Persona configuration
            
        Returns:
            True if saved successfully
        """
        if not self.db_config.get("enable_persona_persistence", True):
            return False
        
        try:
            # This would use actual database connection
            # For now, log the operation
            self.logger.info(f"Saving persona configuration for {persona_id}")
            
            # Save to file as fallback
            return await dynamic_config_loader.save_config(persona_id, configuration)
            
        except Exception as e:
            self.logger.error(f"Error saving persona configuration {persona_id}: {e}")
            return False
    
    async def load_persona_configuration(self, persona_id: str) -> Optional[Dict[str, Any]]:
        """
        Load persona configuration from database.
        
        Args:
            persona_id: Persona identifier
            
        Returns:
            Persona configuration or None
        """
        try:
            # Load from dynamic config loader (which can include database sources)
            return await dynamic_config_loader.load_config(persona_id)
            
        except Exception as e:
            self.logger.error(f"Error loading persona configuration {persona_id}: {e}")
            return None


class FastAPIIntegrationAdapter:
    """
    Adapter for integrating with FastAPI architecture.
    
    Provides API endpoints for the unified persona system.
    """
    
    def __init__(self):
        """Initialize the FastAPI integration adapter."""
        self.logger = logging.getLogger(f"{__name__}.FastAPIIntegrationAdapter")
        
        # API configuration
        self.api_config: Dict[str, Any] = {}
        
        # Initialize API configuration
        asyncio.create_task(self._load_api_config())
    
    async def _load_api_config(self) -> None:
        """Load API configuration."""
        try:
            config = await dynamic_config_loader.load_config("api_integration")
            
            if config:
                self.api_config = config
                self.logger.info("Loaded API integration configuration")
            else:
                # Default configuration
                self.api_config = {
                    "enable_persona_endpoints": True,
                    "enable_configuration_endpoints": True,
                    "enable_metrics_endpoints": True,
                    "base_path": "/api/personas",
                    "authentication_required": True
                }
                
        except Exception as e:
            self.logger.error(f"Error loading API integration config: {e}")
    
    async def handle_persona_request(
        self,
        persona_id: str,
        request_data: Dict[str, Any],
        user_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Handle API request for persona interaction.
        
        Args:
            persona_id: Persona identifier
            request_data: Request data
            user_context: Optional user context
            
        Returns:
            API response
        """
        try:
            # Create persona node from configuration
            business_profile = user_context.get("business_profile") if user_context else None
            
            persona_node = await create_unified_persona_node_from_config(
                persona_id,
                business_profile=business_profile
            )
            
            if not persona_node:
                return {
                    "success": False,
                    "error": f"Persona {persona_id} not found",
                    "status_code": 404
                }
            
            # Process request through persona node
            # This would integrate with the actual workflow processing
            response = {
                "success": True,
                "persona_id": persona_id,
                "message": "Request processed successfully",
                "status_code": 200
            }
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error handling persona request for {persona_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "status_code": 500
            }


class SystemIntegrationManager:
    """
    Main manager for coordinating all system integrations.
    
    Provides a unified interface for all integration adapters.
    """
    
    def __init__(self):
        """Initialize the system integration manager."""
        self.logger = logging.getLogger(__name__)
        
        # Integration adapters
        self.mcp_adapter = MCPToolIntegrationAdapter()
        self.business_profile_adapter = BusinessProfileIntegrationAdapter()
        self.database_adapter = DatabaseIntegrationAdapter()
        self.api_adapter = FastAPIIntegrationAdapter()
        
        self.logger.info("SystemIntegrationManager initialized")
    
    async def create_integrated_persona(
        self,
        persona_id: str,
        user_context: Optional[Dict[str, Any]] = None,
        config_override: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Create a fully integrated persona with all system connections.
        
        Args:
            persona_id: Persona identifier
            user_context: Optional user context
            config_override: Optional configuration override
            
        Returns:
            Integrated persona information or None
        """
        try:
            # Load persona configuration
            persona_config = await self.database_adapter.load_persona_configuration(persona_id)
            
            if not persona_config:
                self.logger.warning(f"No configuration found for persona: {persona_id}")
                return None
            
            # Apply configuration override
            if config_override:
                persona_config.update(config_override)
            
            # Get business profile from user context
            business_profile = user_context.get("business_profile") if user_context else None
            
            # Enrich context with business profile
            enriched_context = await self.business_profile_adapter.enrich_persona_context(
                user_context or {},
                business_profile
            )
            
            # Get tools for persona
            persona_type = persona_config.get("agent_type", "default")
            capabilities = persona_config.get("capabilities", [])
            
            available_tools = await self.mcp_adapter.get_tools_for_persona(
                persona_type,
                capabilities
            )
            
            # Create integrated persona information
            integrated_persona = {
                "persona_id": persona_id,
                "configuration": persona_config,
                "available_tools": available_tools,
                "enriched_context": enriched_context,
                "integration_status": {
                    "mcp_tools": len(available_tools) > 0,
                    "business_profile": business_profile is not None,
                    "database_connected": True,
                    "api_ready": True
                }
            }
            
            return integrated_persona
            
        except Exception as e:
            self.logger.error(f"Error creating integrated persona {persona_id}: {e}")
            return None
    
    async def get_integration_status(self) -> Dict[str, Any]:
        """
        Get status of all system integrations.
        
        Returns:
            Integration status information
        """
        return {
            "mcp_tools": {
                "status": "connected",
                "tool_count": len(self.mcp_adapter.tool_capabilities)
            },
            "business_profiles": {
                "status": "connected",
                "auto_injection": self.business_profile_adapter.integration_config.get("enable_auto_injection", False)
            },
            "database": {
                "status": "connected",
                "persistence_enabled": self.database_adapter.db_config.get("enable_persona_persistence", False)
            },
            "api": {
                "status": "connected",
                "endpoints_enabled": self.api_adapter.api_config.get("enable_persona_endpoints", False)
            }
        }


# Global integration manager instance
system_integration_manager = SystemIntegrationManager()
