"""
Production-ready tests for Phase 3 AI-powered systems.

This module provides realistic integration tests that minimize mocking
and test actual functionality with real data and scenarios.
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, AsyncMock
from datetime import datetime, timezone

from ...agents.langgraph.integrations.phase3_integration_service import Phase3IntegrationService
from ...agents.langgraph.ai.predictive_optimizer import PredictiveOptimizer
from ...agents.langgraph.ai.self_healing import SelfHealingSystem
from ...agents.langgraph.collaboration.advanced_patterns import (
    SkillMatcher, TeamFormationEngine, CollaborativeLearning
)
from backend.agents.langgraph.events.event_bus import event_bus
from backend.agents.langgraph.events.types import WorkflowStartedEvent
from backend.config.phase3_config import get_phase3_config


class TestPhase3ProductionReadiness:
    """Test Phase 3 components with minimal mocking for production readiness."""
    
    @pytest.fixture
    def temp_model_dir(self):
        """Create temporary directory for model storage."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def phase3_config(self):
        """Get Phase 3 configuration."""
        return get_phase3_config()
    
    @pytest.fixture
    async def predictive_optimizer(self, temp_model_dir):
        """Create PredictiveOptimizer with real initialization."""
        optimizer = PredictiveOptimizer()
        # Use temporary directory for model storage
        optimizer.pattern_learning_engine.model_path = temp_model_dir
        await optimizer.initialize()
        return optimizer
    
    @pytest.fixture
    async def self_healing_system(self):
        """Create SelfHealingSystem with real initialization."""
        healing_system = SelfHealingSystem()
        await healing_system.initialize()
        return healing_system
    
    @pytest.fixture
    def skill_matcher(self):
        """Create SkillMatcher with real data."""
        matcher = SkillMatcher()
        # Add some realistic agent skills
        matcher.register_agent_skills("analyst-agent", {
            "data_analysis": 4,
            "statistics": 3,
            "visualization": 2,
            "reporting": 3
        })
        matcher.register_agent_skills("researcher-agent", {
            "research": 4,
            "data_collection": 3,
            "analysis": 2,
            "writing": 3
        })
        matcher.register_agent_skills("writer-agent", {
            "writing": 4,
            "editing": 4,
            "research": 2,
            "communication": 4
        })
        return matcher
    
    @pytest.fixture
    async def phase3_service(self, temp_model_dir):
        """Create Phase3IntegrationService with minimal mocking."""
        service = Phase3IntegrationService()
        
        # Only mock external dependencies, not core functionality
        with patch('backend.agents.langgraph.ai.predictive_optimizer.joblib.dump'):
            with patch('backend.agents.langgraph.ai.predictive_optimizer.joblib.load'):
                await service.initialize()
        
        return service
    
    @pytest.mark.asyncio
    async def test_predictive_optimizer_real_functionality(self, predictive_optimizer):
        """Test PredictiveOptimizer with real data processing."""
        # Create realistic workflow configuration
        workflow_config = {
            'workflow_id': 'test-workflow-001',
            'workflow_type': 'data_analysis',
            'estimated_duration': 45.0,
            'agents_involved': ['analyst-agent'],
            'complexity_score': 3,
            'data_size_mb': 100,
            'user_priority': 'high'
        }
        
        # Test performance prediction (should work even without trained models)
        prediction = await predictive_optimizer.predict_workflow_performance(workflow_config)
        
        # Should return None if no models are trained, which is expected behavior
        assert prediction is None or hasattr(prediction, 'execution_time')
        
        # Test optimization suggestions generation
        suggestions = await predictive_optimizer.generate_optimization_suggestions(
            'test-workflow-001', workflow_config
        )
        
        # Should return a list (may be empty if no optimizations are applicable)
        assert isinstance(suggestions, list)
        
        # If suggestions exist, they should have proper structure
        for suggestion in suggestions:
            assert hasattr(suggestion, 'optimization_type')
            assert hasattr(suggestion, 'expected_improvement')
            assert hasattr(suggestion, 'confidence')
    
    @pytest.mark.asyncio
    async def test_self_healing_real_functionality(self, self_healing_system):
        """Test SelfHealingSystem with real error scenarios."""
        # Test healing attempt with realistic error
        healing_result = await self_healing_system.attempt_healing(
            workflow_id='test-workflow-001',
            error_message='Connection timeout after 30 seconds',
            error_type='TimeoutError'
        )
        
        # Should return a structured result
        assert isinstance(healing_result, dict)
        assert 'workflow_id' in healing_result
        assert 'healing_attempted' in healing_result
        assert 'success' in healing_result
        assert 'actions_taken' in healing_result
        
        # Actions taken should be a list
        assert isinstance(healing_result['actions_taken'], list)
        
        # Test system health monitoring
        health_status = await self_healing_system.get_system_health()
        assert isinstance(health_status, dict)
        assert 'status' in health_status
        assert 'timestamp' in health_status
    
    @pytest.mark.asyncio
    async def test_skill_matcher_real_functionality(self, skill_matcher):
        """Test SkillMatcher with real agent data."""
        # Test skill-based matching
        task_requirements = {
            'data_analysis': 3,
            'writing': 2,
            'research': 2
        }
        
        matches = skill_matcher.find_best_matches(task_requirements, max_agents=2)
        
        # Should return matches
        assert isinstance(matches, list)
        assert len(matches) <= 2
        
        # Each match should have proper structure
        for match in matches:
            assert hasattr(match, 'agent_id')
            assert hasattr(match, 'match_score')
            assert hasattr(match, 'skill_alignment')
            assert 0 <= match.match_score <= 1
    
    @pytest.mark.asyncio
    async def test_team_formation_real_functionality(self, skill_matcher):
        """Test TeamFormationEngine with real scenarios."""
        team_engine = TeamFormationEngine(skill_matcher)
        
        # Test team formation for complex task
        task_requirements = {
            'data_analysis': 4,
            'research': 3,
            'writing': 3,
            'visualization': 2
        }
        
        team_config = await team_engine.form_team(
            task_requirements=task_requirements,
            strategy='balanced',
            team_size=3
        )
        
        # Should return team configuration
        assert team_config is not None
        assert hasattr(team_config, 'team_id')
        assert hasattr(team_config, 'agents')
        assert hasattr(team_config, 'coordination_strategy')
        
        # Team should not exceed requested size
        assert len(team_config.agents) <= 3
    
    @pytest.mark.asyncio
    async def test_phase3_service_resource_management(self, phase3_service):
        """Test Phase3IntegrationService resource management."""
        # Test concurrent optimization limits
        workflow_configs = [
            {
                'workflow_id': f'test-workflow-{i:03d}',
                'workflow_type': 'analysis',
                'estimated_duration': 30.0
            }
            for i in range(10)  # More than the default limit
        ]
        
        # Submit multiple optimization requests
        tasks = [
            phase3_service.optimize_workflow(config['workflow_id'], config)
            for config in workflow_configs
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Some should succeed, some should hit resource limits
        successful_results = [r for r in results if isinstance(r, dict) and 'error' not in r]
        resource_limited = [r for r in results if isinstance(r, dict) and r.get('error') == 'resource_limit_exceeded']
        
        # Should have both successful and resource-limited results
        assert len(successful_results) > 0
        assert len(resource_limited) > 0
        
        # Total should equal input
        assert len(successful_results) + len(resource_limited) == len(workflow_configs)
    
    @pytest.mark.asyncio
    async def test_phase3_service_configuration_integration(self, phase3_service, phase3_config):
        """Test Phase3IntegrationService uses configuration properly."""
        # Verify configuration is loaded
        assert phase3_service.config is not None
        assert phase3_service.resource_limits is not None
        
        # Verify resource limits are applied
        assert phase3_service.max_concurrent_optimizations == phase3_service.resource_limits['max_concurrent_optimizations']
        
        # Test configuration-driven behavior
        assert phase3_service.config.enable_phase3 == phase3_config.enable_phase3
        assert phase3_service.config.optimization_level == phase3_config.optimization_level
    
    @pytest.mark.asyncio
    async def test_event_driven_integration(self, phase3_service):
        """Test event-driven integration with minimal mocking."""
        # Create realistic workflow event
        event = WorkflowStartedEvent(
            workflow_id='test-workflow-001',
            workflow_type='data_analysis',
            user_id='test-user-001',
            agents_involved=['analyst-agent'],
            estimated_duration=30.0
        )
        
        # Track metrics before event
        initial_metrics = phase3_service.integration_metrics.copy()
        
        # Publish event (this should trigger Phase 3 processing)
        await event_bus.publish(event)
        
        # Allow time for event processing
        await asyncio.sleep(0.1)
        
        # Verify event was processed (metrics may or may not change depending on implementation)
        # This is a realistic test - we don't mock the event handling
        current_metrics = phase3_service.integration_metrics
        assert isinstance(current_metrics, dict)
        assert all(isinstance(v, int) for v in current_metrics.values())
    
    @pytest.mark.asyncio
    async def test_error_handling_and_recovery(self, phase3_service):
        """Test error handling with realistic error scenarios."""
        # Test with invalid workflow configuration
        invalid_config = {
            'workflow_id': None,  # Invalid ID
            'workflow_type': '',  # Empty type
            'estimated_duration': -1  # Invalid duration
        }
        
        result = await phase3_service.optimize_workflow('invalid-workflow', invalid_config)
        
        # Should handle errors gracefully
        assert isinstance(result, dict)
        # Should either succeed with warnings or fail gracefully
        assert 'workflow_id' in result or 'error' in result
    
    @pytest.mark.asyncio
    async def test_system_status_reporting(self, phase3_service):
        """Test comprehensive system status reporting."""
        status = await phase3_service.get_system_status()
        
        # Should return comprehensive status
        assert isinstance(status, dict)
        assert 'service_status' in status
        assert 'integration_metrics' in status
        
        # Service status should have required fields
        service_status = status['service_status']
        assert 'initialized' in service_status
        assert 'active' in service_status
        assert 'timestamp' in service_status
        
        # Integration metrics should be present
        metrics = status['integration_metrics']
        assert isinstance(metrics, dict)
        assert all(isinstance(v, int) for v in metrics.values())
    
    @pytest.mark.asyncio
    async def test_performance_under_load(self, phase3_service):
        """Test Phase 3 performance under realistic load."""
        import time
        
        # Create multiple realistic workflow configurations
        workflow_configs = [
            {
                'workflow_id': f'load-test-{i:03d}',
                'workflow_type': 'analysis' if i % 2 == 0 else 'research',
                'estimated_duration': 20.0 + (i % 10) * 5,
                'complexity_score': (i % 5) + 1,
                'user_priority': 'high' if i % 3 == 0 else 'normal'
            }
            for i in range(20)
        ]
        
        # Measure performance
        start_time = time.time()
        
        # Process workflows in batches to respect resource limits
        batch_size = 3
        all_results = []
        
        for i in range(0, len(workflow_configs), batch_size):
            batch = workflow_configs[i:i + batch_size]
            batch_tasks = [
                phase3_service.optimize_workflow(config['workflow_id'], config)
                for config in batch
            ]
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            all_results.extend(batch_results)
            
            # Small delay between batches
            await asyncio.sleep(0.1)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Performance assertions
        assert total_time < 30.0  # Should complete within 30 seconds
        assert len(all_results) == len(workflow_configs)
        
        # Most results should be successful
        successful_results = [r for r in all_results if isinstance(r, dict) and 'error' not in r]
        success_rate = len(successful_results) / len(all_results)
        assert success_rate >= 0.7  # At least 70% success rate
