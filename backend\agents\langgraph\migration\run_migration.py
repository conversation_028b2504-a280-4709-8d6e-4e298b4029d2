#!/usr/bin/env python3
"""
Migration Runner Script for Extensible Persona System.

This script executes the migration from legacy agent implementations
to the new extensible, configuration-driven unified persona system.
"""

import asyncio
import logging
import sys
import argparse
import os
from pathlib import Path
from typing import Dict, Any, Optional

# Set up the Python path correctly
current_dir = Path(__file__).parent
backend_dir = current_dir.parent.parent.parent
sys.path.insert(0, str(backend_dir))

# Set environment to avoid import issues
os.environ.setdefault('PYTHONPATH', str(backend_dir))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('migration.log')
    ]
)

logger = logging.getLogger(__name__)


async def run_full_migration():
    """Run the complete migration process."""
    logger.info("🚀 Starting Extensible Persona System Migration")
    logger.info("=" * 60)
    
    try:
        # Initialize migration
        logger.info("📋 Initializing migration...")
        init_success = await migration_execution_manager.initialize_migration()
        
        if not init_success:
            logger.error("❌ Migration initialization failed")
            status = migration_execution_manager.get_migration_status()
            for error in status["status"]["errors"]:
                logger.error(f"   Error: {error}")
            return False
        
        logger.info("✅ Migration initialization successful")
        
        # Execute migration phases
        phases = [
            MigrationPhase.FOUNDATION_SETUP,
            MigrationPhase.PARALLEL_DEPLOYMENT,
            MigrationPhase.CONFIGURATION_MIGRATION,
            MigrationPhase.GRADUAL_TRAFFIC_MIGRATION,
            MigrationPhase.LEGACY_DEPRECATION
        ]
        
        for phase in phases:
            logger.info(f"\n🔄 Executing Phase: {phase.value.replace('_', ' ').title()}")
            logger.info("-" * 50)
            
            success = await migration_execution_manager.execute_phase(phase)
            
            if success:
                logger.info(f"✅ Phase {phase.value} completed successfully")
            else:
                logger.error(f"❌ Phase {phase.value} failed")
                
                # Get detailed error information
                status = migration_execution_manager.get_migration_status()
                for error in status["status"]["errors"]:
                    logger.error(f"   Error: {error}")
                
                # Ask user if they want to continue or rollback
                response = input("\nPhase failed. Continue anyway? (y/N): ")
                if response.lower() != 'y':
                    logger.info("🔄 Migration stopped by user")
                    return False
        
        # Migration completed
        logger.info("\n🎉 Migration Completed Successfully!")
        logger.info("=" * 60)
        
        # Display final status
        await display_migration_status()
        
        return True
        
    except Exception as e:
        logger.error(f"💥 Migration failed with exception: {e}")
        return False


async def run_single_phase(phase_name: str):
    """Run a single migration phase."""
    try:
        phase = MigrationPhase(phase_name)
        logger.info(f"🔄 Running single phase: {phase.value}")
        
        # Initialize if needed
        await migration_execution_manager.initialize_migration()
        
        # Execute the phase
        success = await migration_execution_manager.execute_phase(phase)
        
        if success:
            logger.info(f"✅ Phase {phase.value} completed successfully")
        else:
            logger.error(f"❌ Phase {phase.value} failed")
        
        await display_migration_status()
        return success
        
    except ValueError:
        logger.error(f"❌ Invalid phase name: {phase_name}")
        logger.info("Valid phases: foundation_setup, parallel_deployment, configuration_migration, gradual_traffic_migration, legacy_deprecation")
        return False
    except Exception as e:
        logger.error(f"💥 Phase execution failed: {e}")
        return False


async def display_migration_status():
    """Display current migration status."""
    status = migration_execution_manager.get_migration_status()
    
    logger.info("\n📊 Migration Status:")
    logger.info("-" * 30)
    logger.info(f"Current Phase: {status['status']['current_phase']}")
    logger.info(f"Mode: {status['status']['mode']}")
    logger.info(f"Traffic Percentage: {status['status']['traffic_percentage']}%")
    logger.info(f"Start Time: {status['status']['start_time']}")
    logger.info(f"Last Update: {status['status']['last_update']}")
    
    if status['status']['errors']:
        logger.info(f"\n⚠️  Errors ({len(status['status']['errors'])}):")
        for error in status['status']['errors']:
            logger.info(f"   - {error}")
    
    if status['status']['warnings']:
        logger.info(f"\n⚠️  Warnings ({len(status['status']['warnings'])}):")
        for warning in status['status']['warnings']:
            logger.info(f"   - {warning}")
    
    logger.info(f"\n📈 Metrics:")
    for key, value in status['metrics'].items():
        logger.info(f"   {key}: {value}")


async def check_prerequisites():
    """Check migration prerequisites."""
    logger.info("🔍 Checking migration prerequisites...")
    
    try:
        # Check if extensible system components are available
        from agents.langgraph.strategies.extensible_strategy_system import extensible_strategy_registry
        from agents.langgraph.config.dynamic_config_loader import dynamic_config_loader
        from agents.langgraph.integrations.system_integration_manager import system_integration_manager
        
        logger.info("✅ Extensible system components available")
        
        # Check configuration directory
        config_dir = Path("agents/langgraph/config")
        if config_dir.exists():
            logger.info("✅ Configuration directory exists")
        else:
            logger.warning("⚠️  Configuration directory not found")
        
        # Check for existing configurations
        configs = await dynamic_config_loader.list_all_configs()
        logger.info(f"✅ Found {len(configs)} existing configurations")
        
        # Check integration status
        integration_status = await system_integration_manager.get_integration_status()
        logger.info("✅ System integrations checked")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Missing required components: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Prerequisites check failed: {e}")
        return False


async def emergency_rollback():
    """Perform emergency rollback to legacy system."""
    logger.warning("🚨 Performing emergency rollback to legacy system")
    
    try:
        # Set migration mode to legacy
        migration_execution_manager.status.mode = MigrationMode.LEGACY
        migration_execution_manager.status.traffic_percentage = 0
        
        logger.info("✅ Emergency rollback completed")
        logger.info("   All traffic routed to legacy system")
        
        await display_migration_status()
        
    except Exception as e:
        logger.error(f"💥 Emergency rollback failed: {e}")


def main():
    """Main entry point for migration script."""
    parser = argparse.ArgumentParser(description="Extensible Persona System Migration")
    parser.add_argument(
        "command",
        choices=["full", "phase", "status", "prerequisites", "rollback"],
        help="Migration command to execute"
    )
    parser.add_argument(
        "--phase",
        choices=["foundation_setup", "parallel_deployment", "configuration_migration", 
                "gradual_traffic_migration", "legacy_deprecation"],
        help="Specific phase to execute (required for 'phase' command)"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Run the appropriate command
    if args.command == "full":
        success = asyncio.run(run_full_migration())
        sys.exit(0 if success else 1)
    
    elif args.command == "phase":
        if not args.phase:
            logger.error("❌ --phase argument required for 'phase' command")
            sys.exit(1)
        
        success = asyncio.run(run_single_phase(args.phase))
        sys.exit(0 if success else 1)
    
    elif args.command == "status":
        asyncio.run(display_migration_status())
    
    elif args.command == "prerequisites":
        success = asyncio.run(check_prerequisites())
        sys.exit(0 if success else 1)
    
    elif args.command == "rollback":
        asyncio.run(emergency_rollback())
    
    else:
        logger.error(f"❌ Unknown command: {args.command}")
        sys.exit(1)


if __name__ == "__main__":
    main()
