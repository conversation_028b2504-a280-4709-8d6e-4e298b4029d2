#!/usr/bin/env python3
"""
Performance Improvement Testing Script

This script tests the performance improvements implemented in the Datagenius
LangGraph system to verify that startup times and response times have improved.
"""

import asyncio
import logging
import sys
import time
from datetime import datetime
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_agent_factory_performance():
    """Test agent factory initialization performance."""
    logger.info("🧪 Testing agent factory performance...")
    
    start_time = time.time()
    
    try:
        from agents.langgraph.core.agent_factory import agent_factory
        
        # Test agent discovery
        available_agents = agent_factory.get_available_agents()
        discovery_time = time.time() - start_time
        
        logger.info(f"✅ Agent discovery completed in {discovery_time:.3f}s")
        logger.info(f"📊 Found {len(available_agents)} agents: {available_agents}")
        
        # Test lazy loading
        if available_agents:
            test_agent = available_agents[0]
            lazy_start = time.time()
            
            # This should trigger lazy loading
            agent_node = agent_factory.create_agent_node(test_agent)
            lazy_time = time.time() - lazy_start
            
            logger.info(f"✅ Lazy agent loading completed in {lazy_time:.3f}s")
            
            if agent_node:
                logger.info(f"✅ Successfully created agent node for {test_agent}")
            else:
                logger.warning(f"⚠️ Failed to create agent node for {test_agent}")
        
        return discovery_time, len(available_agents)
        
    except Exception as e:
        logger.error(f"❌ Agent factory test failed: {e}")
        return None, 0


async def test_workflow_manager_performance():
    """Test workflow manager initialization performance."""
    logger.info("🧪 Testing workflow manager performance...")
    
    start_time = time.time()
    
    try:
        from agents.langgraph.core.workflow_manager import WorkflowManager
        
        # Test workflow manager initialization
        workflow_manager = WorkflowManager()
        init_time = time.time() - start_time
        
        logger.info(f"✅ WorkflowManager initialization completed in {init_time:.3f}s")
        
        # Test lazy initialization
        lazy_start = time.time()
        await workflow_manager._ensure_agents_initialized()
        lazy_init_time = time.time() - lazy_start
        
        logger.info(f"✅ Lazy agent initialization completed in {lazy_init_time:.3f}s")
        
        # Check metrics
        metrics = workflow_manager.get_metrics()
        logger.info(f"📊 Workflow manager metrics: {metrics}")
        
        return init_time, lazy_init_time
        
    except Exception as e:
        logger.error(f"❌ Workflow manager test failed: {e}")
        return None, None


async def test_database_performance():
    """Test database connection performance."""
    logger.info("🧪 Testing database performance...")
    
    start_time = time.time()
    
    try:
        from app.database import get_db, SessionLocal
        
        # Test database connection
        db = next(get_db())
        connection_time = time.time() - start_time
        
        logger.info(f"✅ Database connection completed in {connection_time:.3f}s")
        
        # Test session creation
        session_start = time.time()
        session = SessionLocal()
        session_time = time.time() - session_start
        
        logger.info(f"✅ Database session creation completed in {session_time:.3f}s")
        
        # Cleanup
        session.close()
        db.close()
        
        return connection_time, session_time
        
    except Exception as e:
        logger.error(f"❌ Database test failed: {e}")
        return None, None


async def test_caching_performance():
    """Test caching system performance."""
    logger.info("🧪 Testing caching performance...")
    
    try:
        from agents.langgraph.persistence.unified_checkpointer import UnifiedCheckpointer
        
        # Test checkpointer initialization
        start_time = time.time()
        checkpointer = UnifiedCheckpointer()
        init_time = time.time() - start_time
        
        logger.info(f"✅ Checkpointer initialization completed in {init_time:.3f}s")
        
        # Test cache operations
        test_workflow_id = "test_workflow_123"
        test_state = {"test": "data", "timestamp": datetime.now().isoformat()}
        
        # Test cache store
        cache_start = time.time()
        checkpointer._store_in_cache(test_workflow_id, test_state)
        store_time = time.time() - cache_start
        
        # Test cache retrieve
        retrieve_start = time.time()
        cached_state = checkpointer._get_from_cache(test_workflow_id)
        retrieve_time = time.time() - retrieve_start
        
        logger.info(f"✅ Cache store completed in {store_time:.3f}s")
        logger.info(f"✅ Cache retrieve completed in {retrieve_time:.3f}s")
        
        if cached_state:
            logger.info("✅ Cache hit successful")
        else:
            logger.warning("⚠️ Cache miss (unexpected)")
        
        # Test cache metrics
        logger.info(f"📊 Cache hits: {checkpointer.cache_hits}")
        logger.info(f"📊 Cache misses: {checkpointer.cache_misses}")
        
        return init_time, store_time, retrieve_time
        
    except Exception as e:
        logger.error(f"❌ Caching test failed: {e}")
        return None, None, None


async def run_performance_tests():
    """Run all performance tests."""
    logger.info("🚀 Starting performance improvement tests...")
    logger.info("=" * 60)
    
    total_start = time.time()
    
    # Test agent factory
    agent_discovery_time, agent_count = await test_agent_factory_performance()
    
    # Test workflow manager
    wm_init_time, wm_lazy_time = await test_workflow_manager_performance()
    
    # Test database
    db_connection_time, db_session_time = await test_database_performance()
    
    # Test caching
    cache_init_time, cache_store_time, cache_retrieve_time = await test_caching_performance()
    
    total_time = time.time() - total_start
    
    # Generate performance report
    logger.info("=" * 60)
    logger.info("📊 PERFORMANCE TEST RESULTS")
    logger.info("=" * 60)
    
    if agent_discovery_time is not None:
        logger.info(f"Agent Discovery: {agent_discovery_time:.3f}s ({agent_count} agents)")
    
    if wm_init_time is not None:
        logger.info(f"WorkflowManager Init: {wm_init_time:.3f}s")
        if wm_lazy_time is not None:
            logger.info(f"Lazy Agent Init: {wm_lazy_time:.3f}s")
    
    if db_connection_time is not None:
        logger.info(f"Database Connection: {db_connection_time:.3f}s")
        if db_session_time is not None:
            logger.info(f"Database Session: {db_session_time:.3f}s")
    
    if cache_init_time is not None:
        logger.info(f"Cache Init: {cache_init_time:.3f}s")
        if cache_store_time is not None and cache_retrieve_time is not None:
            logger.info(f"Cache Store/Retrieve: {cache_store_time:.3f}s / {cache_retrieve_time:.3f}s")
    
    logger.info(f"Total Test Time: {total_time:.3f}s")
    
    # Performance assessment
    logger.info("=" * 60)
    logger.info("🎯 PERFORMANCE ASSESSMENT")
    logger.info("=" * 60)
    
    excellent_count = 0
    good_count = 0
    needs_work_count = 0
    
    # Assess each metric
    metrics = [
        ("Agent Discovery", agent_discovery_time, 2.0, 5.0),
        ("WorkflowManager Init", wm_init_time, 1.0, 3.0),
        ("Database Connection", db_connection_time, 0.5, 1.0),
        ("Cache Operations", cache_store_time, 0.01, 0.1)
    ]
    
    for name, time_val, excellent_threshold, good_threshold in metrics:
        if time_val is None:
            continue
            
        if time_val < excellent_threshold:
            logger.info(f"✅ {name}: EXCELLENT ({time_val:.3f}s)")
            excellent_count += 1
        elif time_val < good_threshold:
            logger.info(f"✅ {name}: GOOD ({time_val:.3f}s)")
            good_count += 1
        else:
            logger.info(f"⚠️ {name}: NEEDS OPTIMIZATION ({time_val:.3f}s)")
            needs_work_count += 1
    
    # Overall assessment
    total_metrics = excellent_count + good_count + needs_work_count
    if total_metrics > 0:
        excellent_pct = (excellent_count / total_metrics) * 100
        good_pct = (good_count / total_metrics) * 100
        
        logger.info("=" * 60)
        if excellent_pct >= 75:
            logger.info("🎉 OVERALL PERFORMANCE: EXCELLENT")
        elif excellent_pct + good_pct >= 75:
            logger.info("✅ OVERALL PERFORMANCE: GOOD")
        else:
            logger.info("⚠️ OVERALL PERFORMANCE: NEEDS OPTIMIZATION")
        
        logger.info(f"📊 Excellent: {excellent_count}/{total_metrics} ({excellent_pct:.1f}%)")
        logger.info(f"📊 Good: {good_count}/{total_metrics} ({good_pct:.1f}%)")
        logger.info(f"📊 Needs Work: {needs_work_count}/{total_metrics}")


if __name__ == "__main__":
    asyncio.run(run_performance_tests())
