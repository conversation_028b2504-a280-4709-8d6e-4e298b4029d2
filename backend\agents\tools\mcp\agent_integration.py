"""
Agent Integration Tool for MCP Servers.

This module provides a wrapper that integrates MCP server tools
with the Datagenius agent system, making them available to all AI personas.
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from .base import BaseMCPTool

logger = logging.getLogger(__name__)


class AgentMCPTool(BaseMCPTool):
    """
    Agent-compatible wrapper for MCP server tools.
    
    This class wraps MCP server tools to make them compatible with
    the Datagenius agent system while maintaining full functionality.
    """
    
    def __init__(
        self,
        name: str,
        description: str,
        server_id: str,
        tool_name: str,
        parameters: Dict[str, Any],
        integration_service: Any = None
    ):
        """
        Initialize the agent MCP tool.
        
        Args:
            name: Tool name
            description: Tool description
            server_id: MCP server ID
            tool_name: Original tool name on the server
            parameters: Tool parameter schema
            integration_service: MCP agent integration service
        """
        super().__init__()
        self.name = name
        self.description = description
        self.server_id = server_id
        self.tool_name = tool_name
        self.parameters = parameters
        self.integration_service = integration_service
        
        # Tool metadata
        self.tool_type = "mcp_server_tool"
        self.version = "1.0.0"
        self.last_used = None
        self.usage_count = 0
    
    async def execute(self, context: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Execute the MCP tool through the integration service.
        
        Args:
            context: Agent processing context
            **kwargs: Tool arguments
            
        Returns:
            Tool execution result
        """
        try:
            if not self.integration_service:
                raise RuntimeError("MCP integration service not available")
            
            # Extract user information from context
            user_id = context.get("user_id")
            if not user_id:
                raise ValueError("User ID not found in context")
            
            # Get database session from context
            db = context.get("db_session")
            if not db:
                raise ValueError("Database session not found in context")
            
            # Prepare tool arguments
            tool_arguments = self._prepare_arguments(kwargs)
            
            # Execute through integration service
            tool_id = f"mcp_{self.server_id}_{self.tool_name}"
            result = await self.integration_service.execute_mcp_tool(
                db=db,
                tool_id=tool_id,
                arguments=tool_arguments,
                user_id=user_id
            )
            
            # Update usage statistics
            self.last_used = datetime.utcnow()
            self.usage_count += 1
            
            if result.get("success"):
                return {
                    "success": True,
                    "result": result.get("result", {}),
                    "tool_name": self.name,
                    "server_name": result.get("server_name", "Unknown"),
                    "execution_time": datetime.utcnow().isoformat(),
                    "metadata": {
                        "tool_type": self.tool_type,
                        "server_id": self.server_id,
                        "usage_count": self.usage_count
                    }
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "Unknown error"),
                    "tool_name": self.name,
                    "execution_time": datetime.utcnow().isoformat()
                }
                
        except Exception as e:
            logger.error(f"Error executing MCP tool {self.name}: {e}")
            return {
                "success": False,
                "error": str(e),
                "tool_name": self.name,
                "execution_time": datetime.utcnow().isoformat()
            }
    
    def _prepare_arguments(self, kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare and validate tool arguments based on parameter schema.
        
        Args:
            kwargs: Raw arguments
            
        Returns:
            Prepared arguments
        """
        prepared_args = {}
        
        # If no parameter schema, pass through all arguments
        if not self.parameters:
            return kwargs
        
        # Extract parameters based on schema
        schema_properties = self.parameters.get("properties", {})
        required_params = self.parameters.get("required", [])
        
        # Validate required parameters
        for param in required_params:
            if param not in kwargs:
                raise ValueError(f"Required parameter '{param}' not provided")
        
        # Process parameters according to schema
        for param_name, param_schema in schema_properties.items():
            if param_name in kwargs:
                value = kwargs[param_name]
                
                # Basic type validation
                param_type = param_schema.get("type")
                if param_type == "string" and not isinstance(value, str):
                    value = str(value)
                elif param_type == "number" and not isinstance(value, (int, float)):
                    try:
                        value = float(value)
                    except (ValueError, TypeError):
                        raise ValueError(f"Parameter '{param_name}' must be a number")
                elif param_type == "integer" and not isinstance(value, int):
                    try:
                        value = int(value)
                    except (ValueError, TypeError):
                        raise ValueError(f"Parameter '{param_name}' must be an integer")
                elif param_type == "boolean" and not isinstance(value, bool):
                    value = bool(value)
                
                prepared_args[param_name] = value
        
        return prepared_args
    
    def get_schema(self) -> Dict[str, Any]:
        """
        Get the tool schema for agent registration.
        
        Returns:
            Tool schema dictionary
        """
        return {
            "name": self.name,
            "description": self.description,
            "parameters": self.parameters,
            "type": self.tool_type,
            "version": self.version,
            "server_id": self.server_id,
            "tool_name": self.tool_name,
            "metadata": {
                "last_used": self.last_used.isoformat() if self.last_used else None,
                "usage_count": self.usage_count,
                "capabilities": ["execute", "validate", "schema"]
            }
        }
    
    def validate_arguments(self, arguments: Dict[str, Any]) -> List[str]:
        """
        Validate tool arguments against the parameter schema.
        
        Args:
            arguments: Arguments to validate
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        if not self.parameters:
            return errors
        
        schema_properties = self.parameters.get("properties", {})
        required_params = self.parameters.get("required", [])
        
        # Check required parameters
        for param in required_params:
            if param not in arguments:
                errors.append(f"Required parameter '{param}' is missing")
        
        # Validate parameter types
        for param_name, value in arguments.items():
            if param_name in schema_properties:
                param_schema = schema_properties[param_name]
                param_type = param_schema.get("type")
                
                if param_type == "string" and not isinstance(value, str):
                    errors.append(f"Parameter '{param_name}' must be a string")
                elif param_type == "number" and not isinstance(value, (int, float)):
                    errors.append(f"Parameter '{param_name}' must be a number")
                elif param_type == "integer" and not isinstance(value, int):
                    errors.append(f"Parameter '{param_name}' must be an integer")
                elif param_type == "boolean" and not isinstance(value, bool):
                    errors.append(f"Parameter '{param_name}' must be a boolean")
                elif param_type == "array" and not isinstance(value, list):
                    errors.append(f"Parameter '{param_name}' must be an array")
                elif param_type == "object" and not isinstance(value, dict):
                    errors.append(f"Parameter '{param_name}' must be an object")
        
        return errors
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """
        Get tool usage statistics.
        
        Returns:
            Usage statistics dictionary
        """
        return {
            "tool_name": self.name,
            "server_id": self.server_id,
            "usage_count": self.usage_count,
            "last_used": self.last_used.isoformat() if self.last_used else None,
            "tool_type": self.tool_type,
            "version": self.version
        }
    
    def __str__(self) -> str:
        """String representation of the tool."""
        return f"AgentMCPTool(name='{self.name}', server_id='{self.server_id}')"
    
    def __repr__(self) -> str:
        """Detailed string representation of the tool."""
        return (
            f"AgentMCPTool("
            f"name='{self.name}', "
            f"server_id='{self.server_id}', "
            f"tool_name='{self.tool_name}', "
            f"usage_count={self.usage_count}"
            f")"
        )


class MCPToolManager:
    """
    Manager for MCP tools in the agent system.
    
    This class manages the lifecycle of MCP tools within the agent system,
    including registration, discovery, and execution coordination.
    """
    
    def __init__(self):
        """Initialize the MCP tool manager."""
        self.registered_tools: Dict[str, AgentMCPTool] = {}
        self.tool_categories: Dict[str, List[str]] = {}
        self.logger = logging.getLogger(__name__)
    
    def register_tool(self, tool: AgentMCPTool, category: str = "general") -> None:
        """
        Register an MCP tool with the agent system.
        
        Args:
            tool: MCP tool to register
            category: Tool category for organization
        """
        tool_id = f"mcp_{tool.server_id}_{tool.tool_name}"
        self.registered_tools[tool_id] = tool
        
        if category not in self.tool_categories:
            self.tool_categories[category] = []
        
        if tool_id not in self.tool_categories[category]:
            self.tool_categories[category].append(tool_id)
        
        self.logger.info(f"Registered MCP tool: {tool.name} (ID: {tool_id})")
    
    def unregister_tool(self, tool_id: str) -> bool:
        """
        Unregister an MCP tool from the agent system.
        
        Args:
            tool_id: Tool ID to unregister
            
        Returns:
            True if tool was unregistered, False if not found
        """
        if tool_id in self.registered_tools:
            del self.registered_tools[tool_id]
            
            # Remove from categories
            for category, tools in self.tool_categories.items():
                if tool_id in tools:
                    tools.remove(tool_id)
            
            self.logger.info(f"Unregistered MCP tool: {tool_id}")
            return True
        
        return False
    
    def get_tool(self, tool_id: str) -> Optional[AgentMCPTool]:
        """
        Get a registered MCP tool by ID.
        
        Args:
            tool_id: Tool ID
            
        Returns:
            MCP tool or None if not found
        """
        return self.registered_tools.get(tool_id)
    
    def get_tools_by_category(self, category: str) -> List[AgentMCPTool]:
        """
        Get all tools in a specific category.
        
        Args:
            category: Tool category
            
        Returns:
            List of MCP tools in the category
        """
        tool_ids = self.tool_categories.get(category, [])
        return [self.registered_tools[tool_id] for tool_id in tool_ids if tool_id in self.registered_tools]
    
    def get_all_tools(self) -> Dict[str, AgentMCPTool]:
        """
        Get all registered MCP tools.
        
        Returns:
            Dictionary of all registered tools
        """
        return self.registered_tools.copy()
    
    def get_tool_schemas(self) -> List[Dict[str, Any]]:
        """
        Get schemas for all registered tools.
        
        Returns:
            List of tool schemas
        """
        return [tool.get_schema() for tool in self.registered_tools.values()]
    
    def get_usage_statistics(self) -> Dict[str, Any]:
        """
        Get usage statistics for all tools.
        
        Returns:
            Usage statistics dictionary
        """
        stats = {
            "total_tools": len(self.registered_tools),
            "categories": {cat: len(tools) for cat, tools in self.tool_categories.items()},
            "tool_stats": [tool.get_usage_stats() for tool in self.registered_tools.values()]
        }
        
        return stats


# Global tool manager instance
mcp_tool_manager = MCPToolManager()
