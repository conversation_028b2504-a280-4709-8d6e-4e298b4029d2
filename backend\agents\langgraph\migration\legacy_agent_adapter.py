"""
Legacy Agent Adapter for LangGraph Migration.

This module provides adapters that bridge the existing agent implementations
with the new unified LangGraph-based system, ensuring backward compatibility
during the migration process.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Union, Type
from datetime import datetime

from ..nodes.unified_persona_node import UnifiedPersonaNode, create_unified_persona_node
from ..strategies.persona_strategy_registry import persona_strategy_registry
from ..managers.unified_tool_manager import unified_tool_manager
from ..states.unified_state import UnifiedDatageniusState, create_unified_state

logger = logging.getLogger(__name__)


class LegacyAgentAdapter:
    """
    Adapter that bridges legacy agent implementations with the unified system.
    
    This adapter allows existing code to continue working while gradually
    migrating to the new LangGraph-based architecture.
    """
    
    def __init__(self):
        """Initialize the legacy agent adapter."""
        self.logger = logging.getLogger(__name__)
        
        # Legacy agent mappings
        self.legacy_agent_mappings = {
            "composable-analysis-ai": {
                "persona_type": "analysis",
                "agent_class": "agents.analysis_agent.composable_agent.ComposableAnalysisAgent"
            },
            "composable-marketing-ai": {
                "persona_type": "marketing", 
                "agent_class": "agents.marketing_agent.composable_agent.ComposableMarketingAgent"
            },
            "concierge-agent": {
                "persona_type": "concierge",
                "agent_class": "agents.concierge_agent.concierge.ConciergeAgent"
            },
            "classification-agent": {
                "persona_type": "classification",
                "agent_class": "agents.classification.composable_agent.ComposableClassificationAgent"
            }
        }
        
        # Unified persona node cache
        self.unified_nodes: Dict[str, UnifiedPersonaNode] = {}
        
        # Migration status tracking
        self.migration_status = {
            "agents_migrated": 0,
            "agents_pending": len(self.legacy_agent_mappings),
            "migration_errors": []
        }
        
        self.logger.info("LegacyAgentAdapter initialized")
    
    async def get_unified_agent(
        self,
        agent_id: str,
        config: Optional[Dict[str, Any]] = None,
        business_profile: Optional[Dict[str, Any]] = None
    ) -> UnifiedPersonaNode:
        """
        Get a unified persona node for the given agent ID.
        
        Args:
            agent_id: Legacy agent identifier
            config: Optional configuration override
            business_profile: Optional business profile context
            
        Returns:
            UnifiedPersonaNode instance
        """
        # Check cache first
        cache_key = f"{agent_id}_{hash(str(config)) if config else 'default'}"
        if cache_key in self.unified_nodes:
            return self.unified_nodes[cache_key]
        
        # Get legacy agent mapping
        if agent_id not in self.legacy_agent_mappings:
            self.logger.warning(f"Unknown legacy agent ID: {agent_id}, using default")
            persona_type = "default"
        else:
            persona_type = self.legacy_agent_mappings[agent_id]["persona_type"]
        
        # Create persona configuration
        persona_config = await self._create_persona_config(agent_id, persona_type, config)
        
        # Create unified persona node
        unified_node = create_unified_persona_node(persona_config, business_profile)
        
        # Cache the node
        self.unified_nodes[cache_key] = unified_node
        
        self.logger.info(f"Created unified node for legacy agent: {agent_id}")
        return unified_node
    
    async def _create_persona_config(
        self,
        agent_id: str,
        persona_type: str,
        config_override: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create persona configuration from legacy agent information.
        
        Args:
            agent_id: Legacy agent identifier
            persona_type: Persona type
            config_override: Optional configuration override
            
        Returns:
            Persona configuration dictionary
        """
        # Base configuration
        base_config = {
            "persona_id": agent_id,
            "agent_type": persona_type,
            "name": self._get_agent_display_name(agent_id),
            "description": self._get_agent_description(agent_id),
            "capabilities": self._get_agent_capabilities(persona_type),
            "supported_intents": self._get_supported_intents(persona_type),
            "tools": unified_tool_manager.get_tools_for_persona(persona_type),
            "methodology_framework": self._get_methodology_framework(persona_type)
        }
        
        # Apply configuration override
        if config_override:
            base_config.update(config_override)
        
        return base_config
    
    def _get_agent_display_name(self, agent_id: str) -> str:
        """Get display name for legacy agent."""
        name_mappings = {
            "composable-analysis-ai": "Data Analysis AI",
            "composable-marketing-ai": "Marketing AI",
            "concierge-agent": "Datagenius Concierge",
            "classification-agent": "Classification AI"
        }
        return name_mappings.get(agent_id, agent_id.replace("-", " ").title())
    
    def _get_agent_description(self, agent_id: str) -> str:
        """Get description for legacy agent."""
        description_mappings = {
            "composable-analysis-ai": "Expert data analyst specializing in insights and visualization",
            "composable-marketing-ai": "Marketing strategist and content creator",
            "concierge-agent": "Helpful guide for persona recommendations and assistance",
            "classification-agent": "Text and data classification specialist"
        }
        return description_mappings.get(agent_id, "AI assistant")
    
    def _get_agent_capabilities(self, persona_type: str) -> List[str]:
        """Get capabilities for persona type."""
        capability_mappings = {
            "analysis": [
                "data_analysis",
                "visualization_creation",
                "statistical_analysis",
                "report_generation",
                "trend_analysis",
                "data_exploration"
            ],
            "marketing": [
                "content_generation",
                "campaign_creation",
                "social_media_management",
                "brand_strategy",
                "marketing_analysis",
                "audience_targeting"
            ],
            "concierge": [
                "persona_recommendation",
                "intent_analysis",
                "conversation_management",
                "data_attachment_assistance",
                "workflow_coordination",
                "user_guidance"
            ],
            "classification": [
                "text_classification",
                "data_categorization",
                "content_organization",
                "sentiment_analysis",
                "document_sorting",
                "entity_extraction"
            ]
        }
        return capability_mappings.get(persona_type, ["general_assistance"])
    
    def _get_supported_intents(self, persona_type: str) -> List[str]:
        """Get supported intents for persona type."""
        intent_mappings = {
            "analysis": [
                "data_analysis",
                "visualization_request",
                "statistical_analysis",
                "report_generation",
                "trend_analysis",
                "data_exploration"
            ],
            "marketing": [
                "content_creation",
                "campaign_planning",
                "social_media_post",
                "marketing_strategy",
                "brand_development",
                "audience_analysis"
            ],
            "concierge": [
                "persona_request",
                "general_inquiry",
                "data_attachment",
                "workflow_coordination",
                "help_request",
                "greeting"
            ],
            "classification": [
                "text_classification",
                "data_categorization",
                "content_organization",
                "sentiment_analysis",
                "document_sorting",
                "entity_extraction"
            ]
        }
        return intent_mappings.get(persona_type, ["general_inquiry"])
    
    def _get_methodology_framework(self, persona_type: str) -> Optional[str]:
        """Get methodology framework for persona type."""
        framework_mappings = {
            "analysis": "UNDERSTAND_ASSESS_EXECUTE_DELIVER",
            "marketing": None,  # No specific framework
            "concierge": None,  # No specific framework
            "classification": None  # No specific framework
        }
        return framework_mappings.get(persona_type)
    
    async def migrate_legacy_request(
        self,
        agent_id: str,
        request_data: Dict[str, Any],
        business_profile: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Migrate a legacy agent request to the unified system.
        
        Args:
            agent_id: Legacy agent identifier
            request_data: Legacy request data
            business_profile: Optional business profile context
            
        Returns:
            Unified system response
        """
        try:
            # Get unified agent
            unified_agent = await self.get_unified_agent(agent_id, business_profile=business_profile)
            
            # Convert legacy request to unified state
            unified_state = await self._convert_legacy_request(request_data)
            
            # Process through unified agent
            result_state = await unified_agent(unified_state)
            
            # Convert back to legacy response format
            legacy_response = await self._convert_to_legacy_response(result_state, agent_id)
            
            return legacy_response
            
        except Exception as e:
            self.logger.error(f"Error migrating legacy request for {agent_id}: {e}")
            self.migration_status["migration_errors"].append({
                "agent_id": agent_id,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            
            # Return error response in legacy format
            return {
                "success": False,
                "error": str(e),
                "message": "Migration error occurred"
            }
    
    async def _convert_legacy_request(self, request_data: Dict[str, Any]) -> UnifiedDatageniusState:
        """
        Convert legacy request data to unified state.
        
        Args:
            request_data: Legacy request data
            
        Returns:
            UnifiedDatageniusState instance
        """
        # Extract message from legacy request
        message = request_data.get("message", "")
        user_id = request_data.get("user_id", "unknown")
        conversation_id = request_data.get("conversation_id", "unknown")
        
        # Create unified state
        state = create_unified_state(
            user_id=user_id,
            conversation_id=conversation_id,
            initial_message=message
        )
        
        # Add legacy context to workflow context
        state.workflow_context["legacy_request"] = request_data
        state.workflow_context["migration_adapter"] = True
        
        return state
    
    async def _convert_to_legacy_response(
        self,
        state: UnifiedDatageniusState,
        agent_id: str
    ) -> Dict[str, Any]:
        """
        Convert unified state back to legacy response format.
        
        Args:
            state: Unified state after processing
            agent_id: Legacy agent identifier
            
        Returns:
            Legacy response format
        """
        # Extract response message
        response_message = ""
        if state.messages:
            response_message = state.messages[-1].content
        
        # Build legacy response
        legacy_response = {
            "success": True,
            "message": response_message,
            "agent_id": agent_id,
            "metadata": {
                "workflow_status": state.workflow_status.value if state.workflow_status else "unknown",
                "tools_executed": state.workflow_context.get("tools_executed", False),
                "processing_time": state.workflow_context.get("processing_time", 0),
                "unified_migration": True
            }
        }
        
        # Add tool results if available
        if "tool_results" in state.workflow_context:
            legacy_response["tool_results"] = state.workflow_context["tool_results"]
        
        return legacy_response
    
    def get_migration_status(self) -> Dict[str, Any]:
        """
        Get migration status information.
        
        Returns:
            Migration status dictionary
        """
        return {
            "total_agents": len(self.legacy_agent_mappings),
            "agents_migrated": self.migration_status["agents_migrated"],
            "agents_pending": self.migration_status["agents_pending"],
            "migration_errors": self.migration_status["migration_errors"],
            "unified_nodes_cached": len(self.unified_nodes)
        }
    
    def clear_cache(self) -> None:
        """Clear the unified nodes cache."""
        self.unified_nodes.clear()
        self.logger.info("Cleared unified nodes cache")


# Global adapter instance
legacy_agent_adapter = LegacyAgentAdapter()
