"""
Persona-Driven Workflow Builder for LangGraph-based Datagenius System.

This module provides dynamic workflow creation capabilities based on selected
personas and user context, enabling personalized AI experiences with industry
specialization and cross-agent intelligence.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
import uuid

from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from ..states.unified_state import (
    UnifiedDatageniusState, 
    create_unified_state,
    WorkflowStatus
)
from ..nodes.base_agent_node import BaseAgentNode
from ..nodes.routing_node import RoutingNode
from ..nodes.tool_execution_node import ToolExecutionNode
from ..persistence.unified_checkpointer import UnifiedCheckpointer
from .marketplace_agent_factory import (
    MarketplaceAgentFactory, 
    PersonaRegistry, 
    UserContext
)
from ..intelligence.cross_agent_intelligence import CrossAgentIntelligenceManager
from ..industry.specialization_manager import IndustrySpecializationManager

logger = logging.getLogger(__name__)


class PersonaConfig:
    """Configuration class for persona-specific settings."""
    
    def __init__(self, config_data: Dict[str, Any]):
        self.persona_id = config_data.get("persona_id")
        self.name = config_data.get("name", "Unknown Persona")
        self.industry_specialization = config_data.get("industry_specialization")
        self.methodology_framework = config_data.get("methodology_framework", "UNDERSTAND_ASSESS_EXECUTE_DELIVER")
        self.enable_cross_agent_intelligence = config_data.get("enable_cross_agent_intelligence", True)
        self.enable_business_profile_context = config_data.get("enable_business_profile_context", True)
        self.enable_dynamic_adaptation = config_data.get("enable_dynamic_adaptation", True)
        self.specialized_tools = config_data.get("specialized_tools", [])
        self.compliance_requirements = config_data.get("compliance_requirements", [])
        self.workflow_patterns = config_data.get("workflow_patterns", [])
        self.performance_optimization = config_data.get("performance_optimization", {})


class BusinessProfile:
    """Business profile information for context injection."""
    
    def __init__(self, profile_data: Dict[str, Any]):
        self.profile_id = profile_data.get("id")
        self.company_name = profile_data.get("company_name", "")
        self.industry = profile_data.get("industry", "")
        self.business_type = profile_data.get("business_type", "")
        self.target_audience = profile_data.get("target_audience", "")
        self.key_products = profile_data.get("key_products", [])
        self.compliance_requirements = profile_data.get("compliance_requirements", [])
        self.data_sources = profile_data.get("data_sources", [])
        self.business_goals = profile_data.get("business_goals", [])


class PersonaWorkflowBuilder:
    """
    Builds LangGraph workflows dynamically based on selected persona and user context.
    
    This builder creates optimized workflows that adapt to:
    - Persona-specific capabilities and methodologies
    - Industry specialization requirements
    - Business profile context
    - Cross-agent intelligence integration
    - Compliance and regulatory requirements
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.checkpointer = UnifiedCheckpointer()
        self.memory_saver = MemorySaver()
        self.persona_registry = PersonaRegistry()
        self.agent_factory = MarketplaceAgentFactory()
        self.cross_agent_intelligence = CrossAgentIntelligenceManager()
        self.industry_specialization = IndustrySpecializationManager()
        
        # Workflow cache for performance
        self.workflow_cache: Dict[str, StateGraph] = {}
        self.cache_ttl = 3600  # 1 hour cache TTL
        
        # Methodology framework implementations
        self.methodology_frameworks = {
            "UNDERSTAND_ASSESS_EXECUTE_DELIVER": self._build_uaed_framework,
            "AGILE_ANALYTICS": self._build_agile_framework,
            "LEAN_STARTUP": self._build_lean_framework,
            "DESIGN_THINKING": self._build_design_thinking_framework
        }
    
    async def create_persona_workflow(
        self, 
        persona_id: str, 
        user_context: UserContext,
        business_profile: Optional[BusinessProfile] = None,
        conversation_id: Optional[str] = None
    ) -> StateGraph:
        """Create optimized workflow for specific persona and context."""
        try:
            self.logger.info(f"Creating persona workflow for {persona_id}")
            
            # Check cache first
            cache_key = self._generate_cache_key(persona_id, user_context, business_profile)
            if cache_key in self.workflow_cache:
                self.logger.info(f"Using cached workflow for {persona_id}")
                return self.workflow_cache[cache_key]
            
            # Get persona configuration
            persona_config_data = await self.persona_registry.get_persona_config(persona_id)
            persona_config = PersonaConfig(persona_config_data)
            
            # Build workflow graph
            workflow = StateGraph(UnifiedDatageniusState)
            
            # Add core routing node
            await self._add_routing_node(workflow, persona_config)
            
            # Add persona-specific nodes
            await self._add_persona_nodes(workflow, persona_config)
            
            # Add methodology framework nodes
            if persona_config.methodology_framework:
                await self._add_methodology_nodes(workflow, persona_config)
            
            # Add cross-agent intelligence nodes if enabled
            if persona_config.enable_cross_agent_intelligence and business_profile:
                await self._add_intelligence_nodes(workflow, business_profile)
            
            # Add industry-specific specialization
            if persona_config.industry_specialization:
                await self._add_industry_nodes(workflow, persona_config)
            
            # Add specialized tools
            if persona_config.specialized_tools:
                await self._add_specialized_tool_nodes(workflow, persona_config)
            
            # Add compliance nodes if required
            if persona_config.compliance_requirements:
                await self._add_compliance_nodes(workflow, persona_config)
            
            # Compile workflow
            compiled_workflow = workflow.compile(checkpointer=self.checkpointer)
            
            # Cache the workflow
            self.workflow_cache[cache_key] = compiled_workflow
            
            self.logger.info(f"Successfully created persona workflow for {persona_id}")
            return compiled_workflow
            
        except Exception as e:
            self.logger.error(f"Error creating persona workflow for {persona_id}: {e}")
            raise
    
    async def _add_routing_node(self, workflow: StateGraph, persona_config: PersonaConfig) -> None:
        """Add routing node for workflow orchestration."""
        routing_node = RoutingNode(
            persona_id=persona_config.persona_id,
            routing_strategy="persona_priority"
        )
        
        workflow.add_node("routing", routing_node.execute)
        workflow.set_entry_point("routing")
    
    async def _add_persona_nodes(self, workflow: StateGraph, persona_config: PersonaConfig) -> None:
        """Add persona-specific agent nodes."""
        try:
            # Create main persona agent node
            agent_node = await self.agent_factory.create_agent(
                persona_config.persona_id, 
                persona_config.__dict__
            )
            
            workflow.add_node(f"agent_{persona_config.persona_id}", agent_node.execute)
            
            # Add routing from routing node to persona agent
            workflow.add_edge("routing", f"agent_{persona_config.persona_id}")
            
        except Exception as e:
            self.logger.error(f"Error adding persona nodes: {e}")
            raise
    
    async def _add_methodology_nodes(self, workflow: StateGraph, persona_config: PersonaConfig) -> None:
        """Add methodology framework-specific nodes."""
        framework = persona_config.methodology_framework
        
        if framework in self.methodology_frameworks:
            await self.methodology_frameworks[framework](workflow, persona_config)
        else:
            self.logger.warning(f"Unknown methodology framework: {framework}")
    
    async def _build_uaed_framework(self, workflow: StateGraph, persona_config: PersonaConfig) -> None:
        """Build UNDERSTAND → ASSESS → EXECUTE → DELIVER framework."""
        
        # Understand phase
        understand_node = ToolExecutionNode(
            node_id="understand_phase",
            tools=["context_analyzer", "requirement_extractor", "data_profiler"]
        )
        workflow.add_node("understand", understand_node.execute)
        
        # Assess phase
        assess_node = ToolExecutionNode(
            node_id="assess_phase",
            tools=["feasibility_analyzer", "risk_assessor", "resource_planner"]
        )
        workflow.add_node("assess", assess_node.execute)
        
        # Execute phase
        execute_node = ToolExecutionNode(
            node_id="execute_phase",
            tools=["task_executor", "progress_monitor", "quality_checker"]
        )
        workflow.add_node("execute", execute_node.execute)
        
        # Deliver phase
        deliver_node = ToolExecutionNode(
            node_id="deliver_phase",
            tools=["result_formatter", "insight_generator", "report_creator"]
        )
        workflow.add_node("deliver", deliver_node.execute)
        
        # Add sequential edges
        workflow.add_edge(f"agent_{persona_config.persona_id}", "understand")
        workflow.add_edge("understand", "assess")
        workflow.add_edge("assess", "execute")
        workflow.add_edge("execute", "deliver")
        workflow.add_edge("deliver", END)
    
    async def _build_agile_framework(self, workflow: StateGraph, persona_config: PersonaConfig) -> None:
        """Build Agile Analytics framework with iterative cycles."""
        
        # Sprint planning
        planning_node = ToolExecutionNode(
            node_id="sprint_planning",
            tools=["backlog_analyzer", "sprint_planner", "story_estimator"]
        )
        workflow.add_node("planning", planning_node.execute)
        
        # Sprint execution
        execution_node = ToolExecutionNode(
            node_id="sprint_execution",
            tools=["task_executor", "daily_standup", "impediment_tracker"]
        )
        workflow.add_node("execution", execution_node.execute)
        
        # Sprint review
        review_node = ToolExecutionNode(
            node_id="sprint_review",
            tools=["demo_creator", "feedback_collector", "retrospective_analyzer"]
        )
        workflow.add_node("review", review_node.execute)
        
        # Add edges with conditional routing for iterations
        workflow.add_edge(f"agent_{persona_config.persona_id}", "planning")
        workflow.add_edge("planning", "execution")
        workflow.add_edge("execution", "review")
        
        # Conditional edge for iteration or completion
        workflow.add_conditional_edges(
            "review",
            self._should_continue_sprint,
            {
                "continue": "planning",
                "complete": END
            }
        )
    
    async def _add_intelligence_nodes(self, workflow: StateGraph, business_profile: BusinessProfile) -> None:
        """Add cross-agent intelligence nodes."""
        
        # Business context injection node
        context_node = ToolExecutionNode(
            node_id="business_context",
            tools=["business_profile_analyzer", "context_injector"]
        )
        workflow.add_node("business_context", context_node.execute)
        
        # Cross-agent knowledge sharing node
        knowledge_node = ToolExecutionNode(
            node_id="knowledge_sharing",
            tools=["agent_knowledge_retriever", "insight_aggregator"]
        )
        workflow.add_node("knowledge_sharing", knowledge_node.execute)
        
        # Add edges to integrate intelligence
        workflow.add_edge("routing", "business_context")
        workflow.add_edge("business_context", "knowledge_sharing")
    
    async def _add_industry_nodes(self, workflow: StateGraph, persona_config: PersonaConfig) -> None:
        """Add industry-specific specialization nodes."""
        
        industry_template = await self.industry_specialization.get_industry_template(
            persona_config.industry_specialization
        )
        
        if industry_template:
            # Add industry-specific nodes
            for node_config in industry_template.specialized_nodes:
                node = ToolExecutionNode(
                    node_id=node_config["name"],
                    tools=node_config.get("tools", [])
                )
                workflow.add_node(node_config["name"], node.execute)
    
    async def _add_specialized_tool_nodes(self, workflow: StateGraph, persona_config: PersonaConfig) -> None:
        """Add specialized tool execution nodes."""
        
        for tool_config in persona_config.specialized_tools:
            tool_node = ToolExecutionNode(
                node_id=f"tool_{tool_config['name']}",
                tools=[tool_config["name"]],
                configuration=tool_config.get("configuration", {})
            )
            workflow.add_node(f"tool_{tool_config['name']}", tool_node.execute)
    
    async def _add_compliance_nodes(self, workflow: StateGraph, persona_config: PersonaConfig) -> None:
        """Add compliance and regulatory requirement nodes."""
        
        compliance_node = ToolExecutionNode(
            node_id="compliance_checker",
            tools=["compliance_validator", "regulatory_checker", "audit_logger"]
        )
        workflow.add_node("compliance", compliance_node.execute)
        
        # Add compliance checks at key points
        workflow.add_edge("routing", "compliance")
    
    def _generate_cache_key(
        self, 
        persona_id: str, 
        user_context: UserContext,
        business_profile: Optional[BusinessProfile]
    ) -> str:
        """Generate cache key for workflow caching."""
        key_parts = [
            persona_id,
            user_context.user_id,
            str(user_context.industry_preferences),
            str(user_context.compliance_requirements)
        ]
        
        if business_profile:
            key_parts.extend([
                business_profile.industry,
                business_profile.business_type,
                str(business_profile.compliance_requirements)
            ])
        
        return "_".join(key_parts)
    
    def _should_continue_sprint(self, state: UnifiedDatageniusState) -> str:
        """Determine if agile sprint should continue or complete."""
        # Implementation logic for sprint continuation
        sprint_goals_met = state.get("sprint_goals_met", False)
        max_iterations = state.get("max_iterations", 3)
        current_iteration = state.get("current_iteration", 0)
        
        if sprint_goals_met or current_iteration >= max_iterations:
            return "complete"
        else:
            return "continue"


# Global instance
persona_workflow_builder = PersonaWorkflowBuilder()
