"""
Enhanced Marketplace Models for Datagenius Backend.

This module provides SQLAlchemy models for the enhanced marketplace architecture
including persona configurations, agent plugins, hierarchical messaging, and
workflow adaptations.
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from sqlalchemy import (
    Column, String, Text, Boolean, Integer, Float, DateTime, 
    ForeignKey, Index, UniqueConstraint
)
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime

from ..database import Base

def get_utc_now():
    """Get current UTC timestamp."""
    return datetime.utcnow()


class PersonaConfiguration(Base):
    """Model for enhanced persona configurations."""
    __tablename__ = "persona_configurations"

    id = Column(UUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    persona_id = Column(String(255), nullable=False, index=True)
    configuration = Column(JSONB, nullable=False)
    industry_specialization = Column(String(100), nullable=True, index=True)
    methodology_framework = Column(String(100), nullable=True)
    enable_cross_agent_intelligence = Column(Boolean, default=True)
    specialized_tools = Column(JSONB, default=lambda: [])
    compliance_requirements = Column(JSONB, default=lambda: [])
    workflow_patterns = Column(JSONB, default=lambda: [])
    performance_optimization = Column(JSONB, default=lambda: {})
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)


class AgentPlugin(Base):
    """Model for agent plugins registry."""
    __tablename__ = "agent_plugins"

    id = Column(UUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    plugin_id = Column(String(255), unique=True, nullable=False, index=True)
    name = Column(String(255), nullable=False)
    version = Column(String(50), nullable=False)
    author = Column(String(255), nullable=True, index=True)
    description = Column(Text, nullable=True)
    plugin_package = Column(JSONB, nullable=False)
    security_validation = Column(JSONB, nullable=True)
    capability_validation = Column(JSONB, nullable=True)
    marketplace_listing_id = Column(UUID(as_uuid=True), nullable=True)
    status = Column(String(50), default='pending', index=True)
    supported_industries = Column(JSONB, default=lambda: [])
    required_tools = Column(JSONB, default=lambda: [])
    compliance_certifications = Column(JSONB, default=lambda: [])
    installation_config = Column(JSONB, default=lambda: {})
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)


class MessageThread(Base):
    """Model for hierarchical message threads."""
    __tablename__ = "message_threads"

    id = Column(UUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    conversation_id = Column(String(64), ForeignKey("conversations.id", ondelete="CASCADE"), nullable=False, index=True)
    parent_message_id = Column(UUID(as_uuid=True), ForeignKey("message_threads.id", ondelete="CASCADE"), nullable=True, index=True)
    thread_path = Column(Text, nullable=True)  # LTREE path for hierarchical queries
    message_content = Column(JSONB, nullable=False)
    message_type = Column(String(50), nullable=False, index=True)
    message_status = Column(String(50), default='active', index=True)
    created_by = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), nullable=True)
    message_metadata = Column(JSONB, default=lambda: {})

    # Relationships
    conversation = relationship("Conversation", back_populates="message_threads")
    creator = relationship("User")
    parent_message = relationship("MessageThread", remote_side=[id])
    children = relationship("MessageThread", back_populates="parent_message")


class MessageEditHistory(Base):
    """Model for message edit history."""
    __tablename__ = "message_edit_history"

    id = Column(UUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    edit_id = Column(String(255), unique=True, nullable=False)
    original_message_id = Column(UUID(as_uuid=True), ForeignKey("message_threads.id", ondelete="CASCADE"), nullable=False, index=True)
    edited_message_id = Column(UUID(as_uuid=True), ForeignKey("message_threads.id", ondelete="CASCADE"), nullable=False, index=True)
    edit_type = Column(String(50), nullable=False, index=True)
    edited_by = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    edited_at = Column(DateTime(timezone=True), default=get_utc_now)
    edit_reason = Column(Text, nullable=True)
    diff_data = Column(JSONB, default=lambda: {})

    # Relationships
    original_message = relationship("MessageThread", foreign_keys=[original_message_id])
    edited_message = relationship("MessageThread", foreign_keys=[edited_message_id])
    editor = relationship("User")


class ConversationResubmission(Base):
    """Model for conversation resubmission tracking."""
    __tablename__ = "conversation_resubmissions"

    id = Column(UUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    resubmission_id = Column(String(255), unique=True, nullable=False)
    original_conversation_id = Column(String(64), ForeignKey("conversations.id", ondelete="CASCADE"), nullable=False, index=True)
    new_conversation_id = Column(String(64), ForeignKey("conversations.id", ondelete="CASCADE"), nullable=False, index=True)
    branch_point_message_id = Column(UUID(as_uuid=True), ForeignKey("message_threads.id", ondelete="CASCADE"), nullable=False)
    resubmitted_by = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    resubmission_time = Column(DateTime(timezone=True), default=get_utc_now)
    resubmission_reason = Column(Text, nullable=True)
    original_workflow_context = Column(JSONB, default=lambda: {})

    # Relationships
    original_conversation = relationship("Conversation", foreign_keys=[original_conversation_id])
    new_conversation = relationship("Conversation", foreign_keys=[new_conversation_id])
    branch_point_message = relationship("MessageThread")
    resubmitter = relationship("User")


class IndustryTemplate(Base):
    """Model for industry-specific templates."""
    __tablename__ = "industry_templates"

    id = Column(UUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    industry = Column(String(100), unique=True, nullable=False, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    specialized_nodes = Column(JSONB, default=lambda: [])
    workflow_patterns = Column(JSONB, default=lambda: [])
    compliance_requirements = Column(JSONB, default=lambda: [])
    data_requirements = Column(JSONB, default=lambda: {})
    security_requirements = Column(JSONB, default=lambda: {})
    performance_requirements = Column(JSONB, default=lambda: {})
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)


class WorkflowAdaptation(Base):
    """Model for workflow adaptation tracking."""
    __tablename__ = "workflow_adaptations"

    id = Column(UUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    adaptation_id = Column(String(255), unique=True, nullable=False)
    workflow_id = Column(String(255), nullable=False, index=True)
    persona_id = Column(String(255), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    adaptation_type = Column(String(100), nullable=False, index=True)
    adaptation_triggers = Column(JSONB, default=lambda: [])
    performance_data = Column(JSONB, default=lambda: {})
    adaptation_config = Column(JSONB, default=lambda: {})
    success_metrics = Column(JSONB, default=lambda: {})
    created_at = Column(DateTime(timezone=True), default=get_utc_now)

    # Relationships
    user = relationship("User")


class WorkflowExperiment(Base):
    """Model for workflow experiments (A/B testing)."""
    __tablename__ = "workflow_experiments"

    id = Column(UUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    experiment_id = Column(String(255), unique=True, nullable=False, index=True)
    experiment_name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    control_workflow_config = Column(JSONB, nullable=False)
    variation_workflows_config = Column(JSONB, default=lambda: [])
    traffic_split = Column(JSONB, default=lambda: {})
    success_metrics = Column(JSONB, default=lambda: [])
    experiment_status = Column(String(50), default='draft', index=True)
    start_time = Column(DateTime(timezone=True), nullable=True)
    end_time = Column(DateTime(timezone=True), nullable=True)
    created_by = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships
    creator = relationship("User")


class ExperimentParticipation(Base):
    """Model for experiment participation tracking."""
    __tablename__ = "experiment_participations"

    id = Column(UUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    experiment_id = Column(String(255), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    variation_assigned = Column(String(100), nullable=False, index=True)
    assignment_time = Column(DateTime(timezone=True), default=get_utc_now)
    metrics_data = Column(JSONB, default=lambda: {})

    # Relationships
    user = relationship("User")

    # Constraints
    __table_args__ = (
        UniqueConstraint('experiment_id', 'user_id', name='uq_experiment_user'),
    )


class WorkflowPerformanceMetrics(Base):
    """Model for workflow performance metrics tracking."""
    __tablename__ = "workflow_performance_metrics"

    id = Column(UUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    workflow_id = Column(String(255), nullable=False, index=True)
    persona_id = Column(String(255), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    conversation_id = Column(String(64), ForeignKey("conversations.id", ondelete="CASCADE"), nullable=True)
    execution_time = Column(Float, nullable=True)
    memory_usage = Column(Float, nullable=True)
    success_rate = Column(Float, nullable=True)
    user_satisfaction = Column(Float, nullable=True)
    business_relevance = Column(Float, nullable=True)
    custom_metrics = Column(JSONB, default=lambda: {})
    recorded_at = Column(DateTime(timezone=True), default=get_utc_now, index=True)

    # Relationships
    user = relationship("User")
    conversation = relationship("Conversation")
